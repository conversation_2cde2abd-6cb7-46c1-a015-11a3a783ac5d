---
marp: true
theme: default
paginate: true
backgroundColor: #0d1117
color: #ffffff
title: What is FastAPI & why is it so popular?
---

<!--
Welcome to the FastAPI introduction! In this lesson, we're exploring what FastAPI is, why it's so loved, and what makes it stand out in the Python ecosystem.
-->

# 🚀 What is FastAPI?

**Modern API framework for Python**

- Type hints driven
- Built on Starlette + Pydantic
- Production ready

---

<!--
Think of FastAPI as a microframework that handles only the API layer, but it does it incredibly well. It's called "micro" because it doesn't try to do everything - it doesn't handle databases, ORMs, or frontend templating. Instead, you decide how to structure your project, but it still provides all the production-level features you need.
-->

# 🔬 Why "Micro"?

- No built-in DB/ORM
- No frontend templating  
- You choose the architecture
- Production features included

---

<!--
Let's see what makes FastAPI so popular. These are the killer features that brought it adoption among major companies like Netflix, Microsoft, and Uber. Type hints automatically validate inputs and generate OpenAPI documentation. It's async-first, built on ASGI for high performance. It stays in pure Python territory, so you don't need to learn new syntax. The developer experience is amazing with auto-reload, great autocomplete, and interactive documentation.
-->

# 🌟 Why So Popular?

- **Type Hints** → validation + docs
- **Async-first** → ASGI performance  
- **Pure Python** → familiar syntax
- **Great DX** (developer experience) → autocomplete, auto-reload
- **Industry adoption** → Netflix, Uber, Microsoft

---

<!--
Here's what makes FastAPI magical. Your type annotations become runtime validators and generate interactive Swagger documentation automatically. This simple code creates a POST endpoint that validates the incoming JSON against your Pydantic model and generates beautiful API docs. It's like getting Swagger, Pydantic, and Flask in one clean package.
-->

# 🔍 Type Hints in Action

```python
from fastapi import FastAPI
from pydantic import BaseModel

app = FastAPI()

class Item(BaseModel):
    name: str
    price: float

@app.post("/items/")
def create_item(item: Item):
    return item
```

**Result:** Auto-validation + `/docs` generated!

---

<!--
You can write async endpoints by default, which makes handling high I/O applications a breeze. This is especially powerful when you need to call external APIs, databases, or message queues. You can mix and match synchronous and asynchronous functions seamlessly in the same application.
-->

# 🧵 Async Support

```python
@app.get("/delayed")
async def delayed():
    await some_async_function()
    return {"status": "done"}
```

**Mix sync & async seamlessly**

---

<!--
FastAPI offers blazing-fast performance. It's built on Starlette and Uvicorn, which form a highly efficient ASGI stack. Performance benchmarks show it's comparable to Node.js and Go, making it perfect for real-time applications and high-throughput async workloads.
-->

# ⚡ Performance

- **FastAPI + Uvicorn** = ASGI stack
- **Benchmarks** comparable to Node.js, Go
- **Perfect for** real-time & async workloads

---

<!--
The developer experience is top-tier. You get automatic reload during development, a built-in test client for easy testing, dependency injection for clean architecture, authentication helpers, and automatic documentation with both Swagger UI and ReDoc. Everything you need to build professional APIs is included.
-->

# 🛠️ Developer Experience

- Auto-reload in development
- Built-in test client
- Dependency injection
- Auth helpers
- Auto docs (Swagger + ReDoc)

---

<!--
Here's why I personally love FastAPI. First, it's obsessed with type hints - your IDE gets perfect autocomplete, you get runtime validation, and documentation is generated all from one source of truth. Second, it promotes good practices by default with built-in validation, clean separation of concerns, and structured code. And the documentation is fantastic - that's a serious bonus for any framework.
-->

# ❤️ Why I Love FastAPI

1. **Type-hint obsession**  
   IDE autocomplete + validation + docs from one source

2. **Good practices by default**  
   Validation, separation, clean structure

3. **Fantastic documentation**

---

<!--
This course will show you FastAPI in action, especially through the lens of Test-Driven Development. We won't cover everything FastAPI can do - that would take weeks. Instead, we'll focus on what's essential and reusable: building real API endpoints, using FastAPI with TDD, working with test clients, and refactoring for maintainability. We won't cover advanced topics like OAuth2, background tasks, or websockets - you can check the official docs for those.
-->

# 🧭 Course Scope

**We'll cover:**
- Building real API endpoints
- FastAPI with TDD
- Test clients
- Refactoring for maintainability

**Not covered:**
- OAuth2, background tasks, websockets
- (Check official docs for these)

---

<!--
Let's recap what you've learned in this introduction. FastAPI is a modern, async-first web framework built for Python 3.8 and above. It uses type hints to automatically validate data, serialize responses, and generate documentation. It offers amazing performance combined with excellent developer experience. And it has strong industry adoption with great community support.
-->

# ✅ Key Takeaways

- Modern async-first framework (Python 3.8+)
- Type hints → validation + serialization + docs
- High performance + great developer experience
- Strong industry adoption + community

---

<!--
Now it's your turn to get hands-on. Clone the starter repository - you'll find the link in the course resources. Create your first FastAPI endpoint - a simple GET request to `/ping`. Run the server using uvicorn, and then visit the `/docs` URL to explore the auto-generated Swagger UI. This will give you a feel for how FastAPI works. Good luck!
-->

# 🎯 Learning Activity

1. Clone the starter repo (link in resources)
2. Create your first endpoint: `GET /ping`
3. Run with `uvicorn`
4. Explore `/docs` - auto-generated Swagger UI

**Get hands-on experience!**

---

<!--
That's it for lesson one! Up next, we'll write our first GET endpoint and see how tests fit in right from the start. You'll learn how to use Test-Driven Development with FastAPI to build robust, well-tested APIs. See you in the next lesson!
-->

# 👋 See You in the Next Lesson!

**Coming up:** First GET endpoint + TDD
