---
marp: true
theme: default
paginate: true
backgroundColor: #0d1117
color: #ffffff
title: Writing a Ridiculously Simple GET Endpoint
description: Module 3 / Lesson 02 — Writing a Ridiculously Simple GET Endpoint
---

<!--
Welcome back to our FastAPI journey! In this lesson, we're going to write our very first API endpoint. And I mean ridiculously simple - we're talking about a health check endpoint that just tells us our app is alive and kicking. Think of it like checking if your car engine is running before you drive anywhere. It's the most basic thing you can do, but it's absolutely essential. By the end of this lesson, you'll have a working API endpoint that you can call from your browser, and you'll understand exactly what makes a regular Python function become an API endpoint. Let's dive in!
-->

# Writing a Ridiculously Simple GET Endpoint
### Module 3 / Lesson 02 — FastAPI Introduction

🚀 **Our First API Endpoint**
- Health check: `/health`
- Returns: `{"status": "ok"}`
- Live coding demonstration

---

<!--
Before we start coding, let's talk about what we're building. A GET endpoint is like a question you ask a server. When you type a URL in your browser and hit enter, you're making a GET request. You're essentially saying "Hey server, can you give me some data?" The server then responds with that data. In our case, we're going to create an endpoint that answers the question "Is the API working?" with a simple "Yes, everything is okay."
-->

# What Is a GET Endpoint?

**GET** = "Give me some data"

🌐 **Real-world analogy:**
- Browser → Server: "Are you working?"
- Server → Browser: "Yes, I'm alive!"

📍 **Our endpoint:** `/health`
📤 **Response:** `{"status": "ok"}`

---

<!--
Now let's see this in action. I'm going to create a new file called main.py, and we'll build this step by step. First, I need to import FastAPI class from the fastapi package. Then I create an instance of FastAPI class - this object contains all the information about our API.
-->

# Step 1: Create the FastAPI App

```python
from fastapi import FastAPI

app = FastAPI()
```

💡 **What's happening:**
- Import the FastAPI class
- Create an instance called `app`
- This `app` will handle all our routes

---

<!--
Here's where the magic happens. This little decorator - that's the @ symbol followed by app.get - is what transforms a regular Python function into an API endpoint. Without this decorator, health_check would just be a normal function that nobody could call from the internet. But with this decorator, FastAPI says "Okay, whenever someone makes a GET request to the /health path, run this function and send back whatever this function returns." The function itself is dead simple - it just returns a dictionary with status set to "ok". FastAPI will automatically convert this dictionary to full HTTP response understood by the browser.
-->

# Step 2: Add the GET Endpoint

```python
from fastapi import FastAPI

app = FastAPI()

@app.get("/health")
def health_check():
    return {"status": "ok"}
```

🎯 **The magic:** `@app.get("/health")`
- Registers a route
- Converts function to API endpoint

---

<!--
Now let's take a moment to understand what just happened with that @ symbol. This creates what we call a decorator in Python. Think of a decorator like a wrapper around function - it doesn't change what's inside, but it adds something extra to it. In this case, the @app.get decorator takes our simple health_check function and wraps it with all the web server functionality. It tells FastAPI "when someone visits /health with a GET request, call this function." The path "/health" becomes the URL endpoint, and whatever our function returns gets sent back to the browser as JSON.
-->

# Understanding the Decorator

```python
@app.get("/health")  # ← This is the decorator
def health_check():  # ← Regular Python function
    return {"status": "ok"}
```

**Without decorator:** Just a function
**With decorator:** API endpoint that:
- ✅ Responds to GET requests
- ✅ Lives at `/health` path
- ✅ Auto-converts return value to JSON

---

<!--
Now that we understand how the decorator works, let's save this file as main.py and run it. I'm using the fastapi dev command here. This is the best way to start your FastAPI server in development mode. Just 'fastapi dev' followed by your filename, and FastAPI will automatically detect your app instance and start the development server with auto-reload enabled. This means the server will automatically restart whenever you change your code. This is super handy during development because you don't have to manually restart the server every time you make a change. Remember, this is for development only - we'll cover production deployment later in the course.
-->

# Step 3: Run the Server

```bash
fastapi dev main.py
```

**Command breakdown:**
- `main` → our file `main.py`
- `app` → our FastAPI instance
- `--reload` → auto-restart on changes

🖥️ **Expected output:**
```
FastAPI   Starting development server 🚀
...
server    Server started at http://127.0.0.1:8000
```

---

<!--
Perfect! The server is running. Now let's test our endpoint. I'm going to open my browser and navigate to localhost port 8000 slash health. And there it is! We get back our JSON response with status: ok.
-->

# Step 4: Test in Browser

🌐 **Navigate to:**
```
http://127.0.0.1:8000/health
```

✅ **Expected response:**
```json
{"status": "ok"}
```

---

<!--
Here's something really cool about FastAPI - it automatically generates interactive documentation for your API. Without writing a single line of documentation code, you get a beautiful, interactive API explorer. Let me show you. I'll navigate to localhost:8000/docs. This is called Swagger UI, and it's generated automatically from your code. You can see our health endpoint listed here, and you can even test it directly from this interface. Click on the endpoint, then click "Try it out", then "Execute", and you'll see the same response we got in the browser. This is incredibly useful for testing and sharing your API with other developers.
-->

# Bonus: Free Interactive Documentation!

🌐 **Visit:** `http://127.0.0.1:8000/docs`

**What you get for free:**
- 📖 Swagger UI interface
- 🧪 Test endpoints directly
- 📋 Auto-generated from your code
- 🔄 Updates automatically

**Try it:** Click endpoint → "Try it out" → "Execute"

---

<!--
There's also an alternative documentation format. If you go to localhost:8000/redoc, you'll see a different style of documentation called ReDoc. Some people prefer this cleaner, more traditional documentation style. Both are generated automatically from the same code - you don't have to choose, you get both! This is one of the things that makes FastAPI so powerful for API development. Documentation is often an afterthought, but with FastAPI, it's built right in from day one.
-->

# Alternative Documentation Style

🌐 **Also try:** `http://127.0.0.1:8000/redoc`

**ReDoc interface:**
- 📚 Clean, traditional docs style
- 📱 Mobile-friendly
- 🎨 Different visual approach

**Both docs update automatically!**

---

<!--
Let's recap what we've accomplished in this lesson. We created our first FastAPI endpoint with just a few lines of code. We learned that the @app.get decorator is what transforms a regular Python function into an API endpoint. We saw how FastAPI automatically converts our Python dictionary to JSON - no manual serialization needed. We ran our server with fastapi dev and tested our endpoint in the browser. And we discovered that FastAPI gives us beautiful, interactive documentation for free. This health check endpoint might seem simple, but it's the foundation that every API needs.
-->

# Key Takeaways

✅ **FastAPI endpoints are just Python functions with decorators**

✅ **`@app.get("/path")` creates a GET route**

✅ **Return values automatically become JSON**

✅ **`fastapi dev main.py` runs your server**

✅ **Free documentation at `/docs` and `/redoc`**

---

<!--
Now it's your turn to practice! I want you to create a second endpoint called /ping that returns a JSON object with message: "pong". This is a classic API pattern - ping/pong endpoints are commonly used to test if an API is responsive. Create the endpoint, restart your server if needed, test it in your browser, and then check it out in the automatic documentation. This will help solidify what you've learned and give you confidence to create more endpoints. Remember, the pattern is always the same: decorator, function, return value. Once you get comfortable with this pattern, you can build any kind of API endpoint you need.
-->

# Your Turn! 🎯

**Create a `/ping` endpoint that returns:**
```json
{"message": "pong"}
```

**Steps:**
1. Add `@app.get("/ping")` decorator
2. Create function that returns the JSON
3. Test at `http://127.0.0.1:8000/ping`
4. Check it in `/docs` too!

**This is the classic ping/pong API pattern!**

---

<!--
Congratulations! You've just written your first FastAPI endpoint. In our next lesson, we'll explore how to handle different types of data and add parameters to our endpoints. But for now, take a moment to appreciate what you've built. You have a working web API that responds to HTTP requests and returns JSON data. That's the foundation of modern web development right there. Great job, and I'll see you in the next lesson!
-->

# 🎉 Congratulations!

**You just built your first API endpoint!**

**Coming up next:**
- Adding parameters to endpoints
- Handling different data types
- More HTTP methods

**Keep practicing with the ping endpoint!**
