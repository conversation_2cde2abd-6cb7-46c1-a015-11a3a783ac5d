---
marp: true
theme: default
paginate: true
backgroundColor: #0d1117
color: #ffffff
---

# 🚀 EXPLAINED: FastAPI Introduction  
## Lesson 1: OpenAPI & the Auto‑Docs

From zero to live API docs in 5 minutes.

---

## 🤯 The Pain of Manual Docs

**Ever struggled with:**

- Keeping API docs up to date?
- Explaining endpoints over and over?
- Sharing JSON examples in Slack?
- Maintaining Postman collections?

---

## 😍 What if Docs Were Automatic?

**FastAPI gives you:**

- ✅ OpenAPI 3.1 spec generation
- ✅ Live Swagger UI
- ✅ Clean ReDoc view
- ✅ Interactive testing

**... with zero extra code**

---

## ✨ Let’s Start with an Example

```python
from fastapi import FastAPI

app = FastAPI()

@app.get("/hello")
def say_hello():
    return {"message": "Hello, world!"}
```

🧪 Run:
```bash
uvicorn main:app --reload
```

---

## 🔎 Open Your Browser

```
http://localhost:8000/docs
```

👉 Try it live in Swagger UI

or

```
http://localhost:8000/redoc
```

👉 See clean static docs

---

## 🔍 What is OpenAPI?

- Machine-readable API specification
- Built-in standard for FastAPI
- Enables:
  - Docs generation
  - Validation
  - Mock servers
  - Client code generation

---

## 🛠️ FastAPI = Docs for Free

With just:
```python
@app.get("/hello")
def say_hello(): ...
```

You get:
- Route path
- Method (GET)
- Response schema
- Status code
- Try it out support

---

## 🎁 Bonus: Add Input Models

```python
from pydantic import BaseModel

class HelloRequest(BaseModel):
    name: str

@app.post("/hello")
def greet_user(request: HelloRequest):
    return {"message": f"Hello, {request.name}!"}
```

➡️ Your docs now show JSON schema, required fields, examples!

---

## 🎉 Bonus 2: Response Models

```python
class HelloResponse(BaseModel):
    message: str

@app.post("/hello", response_model=HelloResponse)
def greet_user(request: HelloRequest):
    return {"message": f"Hello, {request.name}!"}
```

📦 Define what your API returns

✅ Swagger will reflect it instantly

---

## 🧪 Try It Live!

1. Go to `/docs`
2. Expand your POST `/hello`
3. Click **"Try it out"**
4. Enter body:

```json
{
  "name": "Grzegorz"
}
```

5. Execute and see response!

---

## 📌 Key Takeaways

- FastAPI generates OpenAPI docs **automatically**
- Built-in Swagger + ReDoc UIs
- Pydantic models enhance input/output clarity
- No Postman, no wiki needed
- Docs always match your code

---

## 🧠 Learning Activity

Try it yourself:

✅ Create a GET and POST endpoint  
✅ Add a Pydantic model  
✅ Add a `response_model`  
✅ Open `/docs` and test it live!

---

## 🔁 Coming Up Next...

👉 Dependencies & Request Validation  
👉 TDD for APIs  
👉 Real-world structure

🧪 FastAPI isn't just fast — it's built for serious, testable code.

---

# 💬 Questions?

Let’s discuss!  
🧠 Why does autogenerated docs change the way we work?  
💡 How would you explain OpenAPI to your teammate?

---

# 🎯 Your Mission

Before the next lesson:

- Add a second route
- Use query params
- Use a body + response model

Then show someone your docs ✨

---
