Transcript
25 August 2023, 08:12am

<PERSON><PERSON><PERSON><PERSON> started transcription

<PERSON><PERSON><PERSON><PERSON>   0:08
For reference, what to do what to add?
And maybe I will close the the doors here and light some.
That's some lightning. It's OK. It's not scarring that the light is not too scary.
Yeah, that's good.
Okay.
So.
We will start very simple from from. It'll be theory theory. So if you type D in Google you will find.
Something like this. This is some random web page, but generally, and this chart graph is something that people always start from.
That we need to have red face. That the you we need to write some test that is failing because it is written for something that doesn't exist.
And that's really important part. Then we are trying to make the test pass. That's the second step of TD and the third one is refactoring.
And.
Too big, like.
This simple chart has.
So many challenges and that.
A TD has.
At that, it is just amazing that in such simple picture there are so many things that may go wrong. So what are the biggest mistakes of doing D?
Like working with testing the first one is that people often start from test.
And not from the starting start from writing the code, not from the test. So this is the the first big mistake on of <PERSON> that they are not doing TD, they are just writing the code and then writing the test. And what are the?
Risk of that?
At the biggest risk is how to test the test.
So how the hell we will know that the test will fail in the future? Is missing of the future is broken if we never see the failing test so.
That's the game changer. You don't know if your test actually testing anything. If you don't see if it is failing. If something is wrong, so that why we need to start from the test to see if the missing feature actually makes the test failed.
So that's the first challenge. Writing the test 1st and it's not easy to do so and to write the test at the beginning, we need to.
First of all, no how to write the code.
So we need to have some idea of how our code will look like, so we will be able to actually make some workings and and and prepare some data for for the test. But that's difficult part second part we need to exactly know what we want to achieve. That's the second difficult part because and how to prepare the test.
If we don't know what will be the results, right? So the challenge here is to actually found that.
Hmm.
Outcome of the test.
And.
Of obvious challenge at the end is we need to know how to write tests, so we need to have some experience in writing tests and to be able to write the test without existing code because.
Yeah, we we need.
Some puff that that we can take on writing that at the beginning, so that's challenging.
At.
So.
Starting from the test.

Adam Domagalski joined the meeting

Grzegorz Kocjan   4:28
Then if a hello Aidan.

Speaker 1   4:35
Hello, can you hear me?

Grzegorz Kocjan   4:37
Yes, we can hear you and I'm glad that I can visit the joint.

Speaker 1   4:43
Ohh yeah, I'm I'm gonna jump in a bit later. I just wanted to to check if you started already. Because yeah, life is catching up on me, but I'll I'll join. But a bit later. Just wanted to say hello.

Grzegorz Kocjan   4:50
Yeah.
OK.
And I I started recording so like that the beginning also will we will be able to just watch later before the night.

Speaker 1   5:02
Ohh great.
Great. Thank you.

Grzegorz Kocjan   5:06
At.
No problem so.
The second mistake that is usually taken when you writing TD is the green face. So OK, I started from failing tests.

Adam Domagalski left the meeting

Grzegorz Kocjan   5:21
At.
I I overpassed this this this challenge then the mistake that usual people make is that they trying to write a good code on the green face. And that's a mistake. You should write a ****** code on green face. It should.
To ask pass the test, nothing more. And that's challenging because like even if even if you are not able to like write very fancy beautiful code, we are often.
Not we are not.
Hmm.
Brave enough, I would say, to write ****** code on purpose.
We are always trying to you to use our best knowledge, use our skills just to write a good code from the beginning and that's the wrong.
Because.
Until we write some features and and write the code until the end, we actually.
We are unable to make informed decision on how this code should look like, how the structure should be, so it is good to write the city code at the beginning just to fulfil all the features.
And then make it beautiful.
So the last thing that is.
That the first thing that is our usual, that that people do wrong, that they forget about refactoring.
And it's not like.
They doing it wrong. They're not doing it at all and.
I don't know if you saw my presentation about test driven pandas. I I held it some time ago in.
Hmm, in our blah, blah blah, how it was.
Uh training set to be that, that that we have on.
Hmm, regular working hours.
I will show you the stats from.
From that.
Presentation. So I was doing a panda CD presentation and statistics from the comments I was that I was I had 11 green commits so I was adding eleven small features. I had 20 red commits where I was adding some test. There were two bugs so I was.
Fixing some back and of course I was reproducing them in the test so so here there were commit with adding new tests that we produce the bugs and the fix. But there were 34 refactoring commits. This is the.
Most important part of of D for me.
Because the fund is in refactoring so.
At.
Writing that that's at the beginning.
Is.
Huge effort.
It is difficult, it is challenging and.
Often people.
Failed on on writing the test at the beginning because it needs an effort and then and then they don't want to spend that effort. That investment on on writing the test there.
Thinking that, OK, instead of focusing on writing test, I will spend that effort, that energy on writing actual code, and that's a little misleading, because if you write the code at the beginning.
Then you will also need the same effort to write the test, but if you spend some time on writing the test at the beginning, then writing the actual code to pass the test will be much easier because you will already have a test that you can run instantly as many times as you want in many different ways, and you can experiment a lot with your solutions and without test without being able to check if.
What I'm writing actually words or doing what I want, you will spend much more time on just, you know, clicking the Pi, running kit, checking if everything works, and if you are working on some application that has prompted and back end then most probably you will write something in the backend, reload the application and then try to click on the front end. That takes a lot of time so.
Investing in writing the the test at the beginning.
Is important it it feels.
Sometimes, like a waste of time, I could spend this time on writing the feature, but actually long term it is much faster.
To write the test, then write speak code and then play a lot of with refactoring like I did here and.
Today we will.
I work in that way so.
The stats from this workshop are a little bit different, but similar. There will be a lot of, there will be refactoring at the end.
But we will try to make a ****** code at the beginning.
Ohh, one thing regarding this graph, it is very simplified chart.
Hmm. But one important thing, it's not.
That simple.
Flow the flow with refactoring is that you can do it always, so it's not like you are writing the failing test then passing the test then refactoring.
You can do whatever you like, so you can write test, make it past, write the test, make it past, write the test, make it pass, and then do some refactoring.
And that's usually what I do because to do a proper refactoring we need some code base to work with and then more feature then more complete. Is our work from times of.
Acceptance criteria.
The better.
And if we are in terms of acceptance criteria?
Hmm.
Okay, maybe we will talk about it later, so.
There is this fast APD in it. Here is a.
Description of what we're going to do. So during the workshop, we'll create a small service using ASAP I that will be responsible for maintaining and mapping of SK U to products.
So we will have a bunch of schedules and will assign them to products. This is part of our larger product, that process game sales data, but generally will focus only on this simple microservice.
And.
For those who don't know.
But probably this is all all of us, like a nightmare. Unless there is. This representation represents a single game DLC that can be sold on Steam, for example, and.
One product can be represented by group of many SKUs related to each other. So for example a game and it's the bloc's or game in different platforms or different versions and etcetera. So for example which are and which are game of the year edition can have different scales, but generally it is the same product. So when we search for this team we will.
We'll we'll search for Witcher. We will find many SKUs, many rows for rescues. But for a game studio it can be a single product.
And they can treat it like a single product and and single bag of money that they earn from that product. So if we are analysing their style data, we get the sales for each schedule, but we want to organise it for the product because that's the.
Important part for them.
So.
Going to.
I'm not sharing the screen, Molly. Come on.

Mohamad Ali Nasser   14:13
I didn't wanna interrupt you, but I have the image open here because I like.

Grzegorz Kocjan   14:17
OK.

Mohamad Ali Nasser   14:18
Yeah.

Grzegorz Kocjan   14:19
Hmm.
Is it better for you now?

Mohamad Ali Nasser   14:24
Yeah, yeah.

Grzegorz Kocjan   14:25
Okay.
So those are the stars that I was referring to. So I had 11.
Green and 12 red commits and 34 refactoring commits from that workshop, and I was saying about this.
And D most common chart that is everywhere in in the world.
And I cannot read the table because.

Mohamad Ali Nasser   14:51
Okay.

Grzegorz Kocjan   14:54
Alright.
Hi top of the window. OK, so.
First, first task task 0.
Ohh OK task one. Write your first Test.
And.
Like the the challenge, don't bother about naming things, creating API, setting up database does right. A simple test with asserts alternate.
So.
Uh.
Clone this repository.
Install.
Uh, here you have dug the empty folder with.
Future code in some in some time and we we have a simple.
That's folder and this is the structure created by poetry at the beginning. So generally this is the code that is created by poetry. It is default setup.
So clone the repository.
And write your first Test. That's your first.
Challenge to do remember, don't bother about naming things. Creating any Pi, setting up anything just.
Try to have a cell phones in test.
In the mean time, I will also open the store and after each task I will show you my result.
And uh.
Or later for for Adam, but also for you.
But at every task.
In this second repository without innit so fast App TD here we have all results on tax, so you can go through all the process progress.
Looking at this second repository and there are also solutions that I.
Talk.
Parties.
Workshop.
And do you have any problems with setting up environment and?
Writing the test.

Mohamad Ali Nasser   21:31
No problems, it's running.

Grzegorz Kocjan   21:33
Hmm.
You have test all.
When you finish, let me know that that you finished and then we will move forward.
All you have also the test.
Ready.

Mohamad Ali Nasser   21:50
Yeah, it's running.

Grzegorz Kocjan   21:52
But figuring that.
That's too many Internet by them in the.
Under them.
Ohh fun fun.
So I.
Here is my VS code and Marie. Do you see it in a good way?

Mohamad Ali Nasser   22:08
Yeah.

Grzegorz Kocjan   22:10
Okay so.
For me, the start is usually the same, so I'm writing the test underscore X that π.
That's file. Why? Because I don't know what I will test. So at the beginning I just need the minimum that is required to run the test some tests and inside I have amazingly name that the underscore X best and have asset falls in it.
So and and this is what I actually do.
I do it very often and sometimes I start in that way when I'm.
Hmm, adding some new feature and always when I starting a new service or new project. Why?
There are three reasons for doing so. So first of all, if I can run those tests then it means that my Python environment works. So this test cheques for me. If my environment is working and nothing else is broken.
Second thing I can check if.
I have a. If I can run the tests.
If I can see proper space that report, so I run the test, I run it in vscode, everything is on red, everything works fine. I see the the summary, the test was running that is were failing and that's.
And information.
For me that if something will break in test.
It means that something is wrong in the logic, not in the environment.
And that's like a I I don't know how you how it was for you, but for me in like previous day of working when I was preparing some new new service, I was like preparing.
And virtual environment database project writing some code not no test at all. I was trying to make difficult connection as well. Queries run migrations, setup database tables right and then get some data, write some get method.
And after all of that, I was running get.
The API get and nothing was working and I was like OK what is wrong? If my environment is broken? If my database is not working if migration was set up correctly and before running the code for the first time as I was usually spending 1/2 of the day the day or like sometimes the days at the beginning when when I was trying with just every of these steps. So right now now right now I'm reversing.
The process to minimalize the steps that I'm checking at the single time.
That's really important because in good TD the blah blah blah and I closed it.
Yeah. The circle that you see here.
Takes minutes.
Not days. So the circle I write the test make it pass takes minutes that the.
The goal that that.
Benchmark that you want to hit is not like that. If you are not doing that in mins then it's wrong. But if you are always doing it that flow that circle in days, that's a red flag.
So we should be able to OK minimalize what we are talking at at single iteration and that the smallest thing that we can check if my environment works the.
But that's not the only thing. There is a second very important thing that I'm checking. I'm creating a breakpoint.
Right clicking and running the back.
So.
I'm not only checking if environment works, but also if I have proper resetup.
I D for the buggy.
And in vs code.
That's not bad. Trivial because my charm works out-of-the-box.
But in this code.
For example, the the thing that you have here.
Hmm, this is run run picture. Sorry. Here, here we have some some additional things that needs to be set up. So for example I have bites enabled and you need you need test disabled. So all sense through and without those plug I will not speak any of those tests. Here in this testing tab. So I need some better configuration to add.
And the beginnings to make it work, though.
Running the debugger is very crucial for me so.
Please try to play some breakpoint in this asset falls and try to run the ******. See if it is a.
Running and if you can see variables, the second thing that you can do that I close the window, so I'll rerun it again.
That we can try is debug console and in this debug console and you can actually do any kind of magic and this is something I'm not checking this part every single time, but I want to show you that you can.
Uh.
Even in your test.
You can.
Price the breakpoint and start writing some code and you can see that this code affects like local variables. I just create local variable.
Why? This is very important because.
Many times I use it.
These, the backbone sole to write a new code and new test. So I have some structure. I'm placing the breakpoint.
Writing some code in the debug console and if it works, if it does what I'm expecting to do, then I'm passing it to the test X and or or something somewhere else.
And I see that Marley's typing, so I'm wondering.
What that would be?
Not this typing for forever. No, right.

Mohamad Ali Nasser   29:02
OK, I I can say it out loud maybe easier it. Yeah, I I wanted to say that I'm not sure if this is the case for everyone, but for me only as code.

Grzegorz Kocjan   29:06
Yeah.

Mohamad Ali Nasser   29:12
Ohh when I wanna do tests I need to add the test folder to the config. So either I added inside the settings or Jason or by clicking control.
Uh shift, P and then there you will, there's select.
Uh tests and you select the folder test so.
So for example, if you go to the test Jason and your I'm not. I'm not sure if this is the case for everyone, but I usually set up a test I need to add the folder to settings that Jason.
So here you have a dot.

Grzegorz Kocjan   29:49
At.

Mohamad Ali Nasser   29:54
You have a testing dot pytest orgs line 10. Yeah, you. Yeah. Usually I have to add the folder tests either here or in the VS code settings.

Grzegorz Kocjan   30:00
Yes, here I have a dog.
At.
So.
That's a.
All right. So these prices are for generated automatically by VW S code.
So the dot is by the default I I I don't remember exactly how it was, but there is a clickable way to select if you are using pytest or unit test in your project. I don't remember where it was exactly.

Mohamad Ali Nasser   30:39
Control shift control shift P.
And type tests.
Ohh yeah, conspires and configure configure test. Then it asks you to select unit test by test and when you select it you.

Grzegorz Kocjan   30:50
Yeah, exactly. So, so when I was sleeping this pytest, it was adding this test arcs dot.

Mohamad Ali Nasser   30:58
Okay if you click it now it will ask you which folder are the tests and.

Grzegorz Kocjan   31:03
Ohh.

Mohamad Ali Nasser   31:04
So here is a you said. So you is either select the route or the test folder.

Grzegorz Kocjan   31:09
So the reason why I said the route is because.
Um.
In π test you can add some additional plugins.
And they can check more than just running the tests.
So I.

Mohamad Ali Nasser   31:24
OK.

Grzegorz Kocjan   31:27
I use it in some some projects. For example you can integrate like Black eye sort and static code analysis into pie test. So it will run additional commands and if you run only Pytheas contest folder then this static analysis will work only on on test folder. If you are using dot then it is running through the whole project.

Mohamad Ali Nasser   31:54
I I see.

Grzegorz Kocjan   31:54
So also for for your production code.
And.

Mohamad Ali Nasser   31:57
Yeah. OK.

Grzegorz Kocjan   31:59
Hmm.

Mohamad Ali Nasser   32:01
No, you're right. I just put the root and I can still see the test, but usually when I'm working somewhere else just it can lead to some bugs of not finding the tests. But you're right, it's working with the root, so sorry for the interruption.

Grzegorz Kocjan   32:16
No, no, that's uh, that's totally.
Good question.
And the.
Like I said in, in PyCharm it is easy because it is working always out of the boxing in a VS code. We need to that to set up this.
The conflict at the beginning to to have it working and like I said, writing this first Test actually.
Can.
Make us focused on having a working environment. As you see, we had discussion about configuration, about running tests, about the back console.
Without writing any actual code.
So.
And.
I think that uh, for now we can move forward.
And to.
Not to write any code because second task.
Is.
About knowing what how we can actually test what we want to do, and second before you start, I will review and give you some explanation what we want to achieve and why.
So.
Search for an example of how to write a test for fast API and hint documentation is your best friend for that.
Ohh.
That's the.
Like I was.
Describing this, this, this.
A graphic for you? It is difficult to work on TD without knowing what we want to do and how to do that. So we want we need to know how to write the test and usually when I was working previously when I was starting my journey with with testing, I was writing the code and then I was trying to test it to to learn how to test existing code in TD we need to change that.
Saviour to 1st trying to search on how to write the test and then actually moving forward.
So that's why we have this task to search for. How, for example, we need to search example how to test test HP I services because that is our requirement. We need to write a microservices first API and.
Why and?
Ohh.
OK. Uh.
Again, I will search for images for reference and all of those images are incorrect. But recently I was searching for one like.
Keep correct, not.
Not efficient for what I want to say, but I found this one that actually.
Show.
Pros exactly.
Ohh the case so.
If you are work want to work on TD and on new service.
We need to break the testing, pipe the pyramid and.
It is my personal experience and my third preference.
For you and like maybe for different products and.
Different cases. It might be a little bit different experience and preferences, but I really like to focus acceptance test.
And.
Even like the guys who talk a lot about TD, they are like gurus of TD and creators. They are not using a TD but they are using a D so.
I this is also what we gonna do today so.
It is not test driven development but acceptance test driven development and.
That's something that really, uh, connects, connects with each other, so we are not focusing on like regular unit test, but we will focus on acceptance tests.
The reason for that is that if we have functions in our code and we we are testing functions with unit tests and we are treating unit tests as a more functions that we will run passing the arguments, etcetera.
It is really difficult to do refactoring.
Because we cannot move one functions changing definition or remove it or merge different functions.
Have we? While we have like hundreds of tests?
For those functions, because each refactoring will require us to modify a lot of.
Test and that's terrible. That's that's.
That's a misery and.
When I was working at the beginning with test, that was my biggest struggle that whenever we wanted to do some refactoring, we were forced to modify like 20 test, 30 test. And that's not the way to go if.
Any refactoring requires for you modification of a lot of tests and this is something that is recurring. That's a red flag that your tests are wrong.
And that they need to be a little bit change so.
I I will focus on this workshop of on acceptance test. What doesn't mean we will focus on on acceptance criteria. Acceptance criteria exists in our giraffe. For example in data thing we love giraffe so we have JIRA and acceptance criteria.
A spoiler for you like side note for you got is that we don't use giraffe all and we don't have acceptance criteria with any our task but usually some it is something that that that that we have or we should have one like going by by the book.
But we decided not to go by the book.
Hmm so.
Why acceptance test?
Because using acceptance tests allow you to do a lot of refactoring and acceptance tests in microservice. Word for me is is our test on a Pi level. So and this is my advice for you. If you want to start TD and you are working with microservice start with the running the whole thing.
So for past API service it will be running the whole API so running get running put, running post. So that's the first.
A task for you is to search how to write test for fast API, how to test the get method, how to test post method and and find it in documentation. Let me know if you already did it or it's your it's time for you.
To start the solution and.
Yeah.
Ride the hand when you're done.
Hmm.
Done.
Tip tandon.
Bike Mary.

Mohamad Ali Nasser   40:42
Yeah, almost. I'm just copying the.
Code from plastic guy.

Grzegorz Kocjan   40:49
How much don't copy it.
Just said.
But I'm so OK. What? What? What? I know.

Mohamad Ali Nasser   40:56
Yeah, I I searched it, I searched it. I'm just.
Copy and pasting it right and that the.

Grzegorz Kocjan   41:10
So in the old way. So by using Google I would type fast API testing documentation in New World.
And probably people, many people will ask that GPT how how to write the test in the.
For the past API.
Both things are correct but.
If you are using ChatGPT for, for for that.
Remember that fast.
Chad deputy.
Is outdated and usually in software development. Having a documentation that is 2 years old is at the doctor.
I thought.
I'm starting from usually Google, if you if I type even with typo as you as as I usually do, I'm I'm writing with a lot of typos.
The 1st result is testing Fast API. That's pretty easy. I see some blah blah blah explanation of how it works, what is in in the in the in the behind of the fast API. So there is a starlet project. There is a pptx framework that is running request and we can use pytest. So it's good to go through this documentation to know better.
What to do? But here is a simple.
Called a two run.
Test in.
Uh pie test.
For fast API, so generally this is the first example and this is something that we were looking for. So if we have some method.
Read written in in fast APIs so it is just running Hello World. We can write a simple test there is this client. It is a test test client that can be important from fast API.
We can call get and we can check response status code and Jason.
And.
OK, I know that you all guys probably know how to write some tests in in fast 8P I, but generally this is something that I always do for something new. So I'm trying to search how people are testing it. Maybe there is something on Stack Overflow, maybe on on official documentation.
The best thing that the best scenario for you is when you find solution on the documentation. Because the person who writes the framework knows the best how to test it, right?
The community often makes some mistakes and community often use the old way of testing or old patterns, etcetera.
And you, and of course the ChatGPT is also outdated. It doesn't have information for last two years though.
Uh.
If you don't have information on official documentation how to test something, then try to search otherwise or or Astra, GPT. But if you're asking ChatGPT and double check it.
Uh, because it is helping a lot.
But always double check it.
Confirm if it is not outdated.
Hmm.
What for example I have this.
The case that I was working with Ciprian recently on Terraform.
And.
I use. I use study PT for for working with Terraform because he's giving a lot of hints on how things can be done and I'm just describing OK. I want to do do, do, do and this, but it often makes mistakes that he places some.
Configuration that doesn't exist and I if I working with Taji always have documentation opened and I'm confirming OK he wants me to set up this configuration.
I'm passing that config name and checking in documentation exactly what it is doing and then actually I'm trying to do that.
So.
In terms of testing and cetera, if charge PT is saying you OK that you should use some test client imported from the the fast API testing. I'm checking double checking that with the documentation if that's actually something correct.
And that's a really good thing to also use Judge PT in such a way.
We've done the following from documentation because in documentation usually we have a very simple examples.
They are too simple to use in actual word and charge PT can help you with.
Uh, more real world examples. So if you are start discussing with him that OK, I have start a picture and I'm using it that and I have some dependencies I have database. I have this and this you can ask him what would be preferred solution to test that and he will probably give you some good hints on the solutions. What to do how how to combine different libraries etcetera. But still you need to double check if that's still a bite.
Response, or if it's not something that is, for example deprecated.
Hmm, but this is our way of testing APISO.
You know how to write the test.
Now.
We will focus on acceptance criteria and.
There are two ways.
That I work with TD.
There is first way that we will cover today when we exactly know what to do. So we have some acceptance criteria. User can get a scale by its ID. Example ID looks like this. So it is some prefix.
And and some number.
And the user cannot new SK you with SK UID&S name. So those are two acceptance criteria those acceptance criteria.
In a perfect world.
When working things from other and blah blah blah and those acceptance criteria are written by the team.
I'm planning on refinement.
Uh.
Before even work on actual code starts, so usually if we are producing some features, usually something that we get at the beginning.
And.
When we were working at platform, team were not using acceptance criteria, but the test that the task were usually somehow described and and and on the tests level on the planning we are discussing what to do and adding some some descriptions. In data team we usually have some design meetings designs that for example Anya prepare and showing us exactly.
What we want to achieve generally how? How, how, how we would like to to see and for example, last time we were talking about way of using auto planner and there were two two cases. So we know exactly that we want to use out of planning in always enabled mode. So this is our acceptance criteria right. So they exist before usual before we start the work.
And in 80D we are focusing on those acceptance criteria and we are trying to write test for those acceptance criteria. There is a second case that I work with Todd. So when I don't have acceptance criteria, I'm doing some research.
And this is the way when I like I I saw you before I using this this debug console and more often when I'm writing something and I don't know exactly what I want to do.
And.
For, for, for, for you Mali. And sit down. It is some kind of replace replacement for repeater notes for me.
I'm more familiar with using Python's than the the back console rather than Jupiter notebooks, but generally I I not using it at all practically, but trying to do a research inside the test, so I'm writing some. I'm even working away that I'm starting to write production code inside test so you know I can just click, run it and see what it does, then use the debugger.
And try to evolve the code. What it what is doing inside the test and then when it is ready I'm copying or moving the code to actual function and in test I'm just skipping that sessions and etcetera.
Is the, that's.
For the debugger, isn't that into the regenerated part file content and tickets?
Sometimes yes.
You got the little before nothing. I mostly familiar to accuse the.
With the with the with the. Don't know. Anyway, just to play with the data frames so that are downloaded with the our results and processing key in files that I downloaded from the production or any other weird I don't have it.
Here for for this task. I hope that this because this slide was an optional for some.
Uh.
And some presentations.
Ohh this tonight.
Ohh it is deleted here in this version. So in vs code you have this extension called Jupiter.
To Peter, and if you have it, it installed, you can click right in in the the beggar mode. You can right click on that frame and show it as a table. So I'm using usually and that.
But.
Important notice that I felt sometimes on.
Analysing some pocket files in that way because this.
Hmm, extension that you can see the data frame inside.
Wait, I have it.
I I I have the movie.
I will try to. I will try to show you.
Yay.
I remember I I made a short movie showing how how to.
How to that? So I'm running the bath?
Of some test I have some local variables I'm searching for data frame and with that I can see a view value in data frame and it just show.
Uh, and and industrial the table and this is the way I'm previewing the the packet files. So for example when Mali or or basil or Massiel are sending me some pocket files and I need to do something with that. I'm starting with opening it in that way.
Important notice is that this is a strict representations of values. So in this table we can see string representations.
And sometimes it is not correct.
So sometimes I need to dig through the variables or to display period in the data frame in the back console and because.
Yeah, my my Marley wrote that I should run the workshop on running parked in a different way.
So I had a case when there was a a.
Typo with the float like the. The problem with the flows that it has some.
Ohh.
*** **** it, the precision was not like 2022 places after the dog, but it was infinite like.
And here you cannot see it in this in this view because it is string representation and it simplifies it to.
To.
Hmm, the string and it was showing like correct values. But actually in that frame they were incorrect.
And that lives are not so such easy. Yes, exactly.
So this is not it is very good, but if you want to go into the details of the data frame of the values you rather need to.
To Peter or the back console I'm I'm using the back console for that, so for example I'm displaying OK data DF dot colours and I see that columns types or. I'm using the watch, so be below variables you have watch.
How would it how it works in the practise? Because this is something that I can show you. So I have the back I have watch, I can press fix.
Fix is not available. I'm running the back.
It stops name X is not defined.
But when I.
Ram and create these variable. Now I see it in what?
And in what for example I can do?
Ohh.
My son, this is something that I do commonly.
With creating microservices running, I just creating a watch response to Jason because when I when we run the test like the too many open windows.
Even though.
Hmm, I closed the. I closed the testing. No here. So if you have this response you see that. OK, this assertion has response recent, but maybe the we will get some status code 400, right? Or status Code 500 will not even get to this assertion, right? So I placing the break font break point at this point.
And we've response, Jason, I will be able to preview, OK, what was in the response without adding some print debugging or or anything else. So I often use such such watch.
In API, I think data frames it can be.
The aircraft dot.
Columns.
I was gonna be anybody else. Yes, it can be anything. And. And as you can see, if it doesn't exist, it doesn't break it it. Yeah. So. So it it is. It is working without.
Hmm.
And you any risks, right? So it can only help you.

Mohamad Ali Nasser   57:33
Just one more thing I find helpful in the debugger debugger is that if you click on the data frame.
And you toggle, you open the arrow, you can see the shape you can see.
A couple of things. You can see types. So sometimes if I click on the arrow, it's very quickly I can without typing in debug console I can see what's.

Grzegorz Kocjan   57:46
Yeah.

Mohamad Ali Nasser   57:56
It's empty. What's generally in it.

Grzegorz Kocjan   57:56
Yeah. So, so if the object is, so if you have simple time like here, this does does does the string or integer we cannot do more but with complex objects like Tata frail we can go into the details and without watching we can just expand data frame and see the columns and expand columns and see how columns looks like and something that I sometimes also use. I'm using hover so if you hover on some variables. So for example on columns.
You will see that nicely formatted columns.
So we don't need to have this watch or you can hover on this variable and as you can see here is a name error that DF is not defined. But if it will be defined.
Um.
Hmm. Nope, it's not there.
But if it would be different if you hover, you will have nicely for mass at least of the columns to the types, so covering also.
Hovering or on on some variables also helps here.
Ohh OK.
Hmm, that's great.
And.
Thus, create is something that we tested recently with Cyprian and on on one of the features.
So in the book.
There is this awesome book by Kenbeck TD in this is the Polish cover. So the Polish cover is totally different from English one. So Polish cover is is the green one. The English probably is. Is this one. This is correct.
There were wonderful which was telling me another Polish no.
Yes, dignity, I was terribly this. This translation is actually.
I lost the screen.
OK, this one actually is.
Translated pretty good because it is an old translated. It is old book. It is like it is from 2010 I think. Or we will even even longer.
Generally can't back is known as a father of TD.
And.
It's not correct.
He is the father of the naming of the of that workflow. He named that TD and wrote this book that covers like the the way of working.
And.
Of TDD.
But TD, when he wrote this book, people from different projects were going to him and saying that, hey, we were working in that way.
And for for many years. And thank you for blah blah blah, you know, gathering all that knowledge. So he mentioned that the oldest project that people came to him and and told that they were working.
On with the D is mercury project.
So Mercury project was the first human Space Flight programme of United States, running from 1958 to 1963.
And those guys came to Kent back and told that they were right in the code using TDD.
So TD is much way longer than the than we are.
So in this book he wrote a nice hint that for me is excellent. So we will try to use it.
We will not write the test. We will write the list of the test, forget schedule SUID endpoint. So in our acceptance criteria, first acceptance criteria is to get SK used by ID. So now we will try to find the list of the test for that endpoint and.
Here are some examples, but name example, test user or test get user. So test EU test get SU are bad name examples. Why? Because they don't give us information what we want to test and do what we are expecting. So good name exactly good name examples are test update user with invalidate returns 400 status code test update user with invite data turns list of invalid fields.
With reason and our fight.
It is very expressive test name.
Not always we need to.
Ohh past such a long test name.
But it is good to be expressive here and keeping things short.
Do not repeat yourself and others like practises that usually we need to.
Feels inside our production code doesn't apply for tests, so here you can have a really long expressive.
Function name and that's totally OK because you will not call this function correctly. We will not import this function, it will be just run and it will be show shown in this list only so.
Right now I see only like 1 test, but if you will have hundreds of tests and you will see that.
Test user will file.
And it is one of hundreds of tests. You have no idea what is from.
You need to open the test, read the whole test and then you will make some assumption what was wrong and usually what I found that when you name test test user it is usually a test that test is testing many things at once.
So this test is long. It's taking like 200 slime, 300 lines of code and that's not helpful. What is helpful if you have hundreds of tests and if you if your tests are failing, you know what is wrong only by looking at the name. It is something really difficult.
It is really difficult and but it is worth going through to to that direction to make the test name more expressive.
And and of course.
If you use that name test user like I mentioned, you will most probably test a lot of things if you use test name like this.
I bet that you will not try to test so many things in in such such test because you are limited by the name.
In a bye bye this testing and it is also a good hint for you.
To focus so, OK, right now if I'm. If I'm starting to write this test, I'm focusing on.
Uh running the.
The post method of put method right update put method with invalid data and I need to check check if there result contains the list of pills that I pass in connected. I I'm focused directly on what I need to do next. That's really important to have this cycle of two minutes 3 minutes.
Red, green, refactor.
Process so tasks for you write please of tests for get SK USKID and point and the hint where to do that.
I started from writing them in the code so we can do something like this test.
Asked of course with typo test user, right?
That. OK, so those are invalid names. Try to write them in such way. So in in some strange long string I in on the top of the file. Try to write 3 tests.
I think it would be good enough if you came up with more than.
Well, far more.
Uh, what you want to test?
Invalid data and happy birth happy path and what you came with.
If that acceptance criteria.
Hmm.
How are you, punk?
OK so.
I will show you my list.
Let me know if you add something.
And more or if if you have something different.
So.
There is that I have test get a schedule for not existing ID returns for for status code.
Second one test get a schedule for not existing. Idea returns message in the dietary speak so it gives some more information in the details in the Jackson.
That's good. OK, test get SK U for existing idea turns to 200 Subs code and get a scale for existing ID returns as KID and its name.
And here there is information that this test can be speeded into two 1-2 and.
I'm not starting from splitting that.
I'm testing the whole response.
Usually at the beginning, but at some point it can be painful to always does the whole response because it.
We will have like here.
Will have like 554 different tests, right? If.
If we test, we if we will test some different test cases. So for example filtering searching through some fields.
And some access for forgiven users and will always check the whole response.
If we add new fields there to the response, we will need to modify all of those tests.
So at the beginning it is easier to test the whole response. So to assert if it returns ASKUID and the name in the single test. But at some point it may be less painful.
And easier to split this test test and only check one thing at a time. So if if SK, YD exists, if name exists in one of the test if. Sorry in in in report processor for example.
I will try quickly as search for example for an example.
We have two kinds of tests, like the acceptance unit tests and one is checking if the required fields are returned.
And then our separate tests that are checking different fields.
It's.
Start. It's something that I was like, um.
Hmm.
Testing for some time, if that's works fine or not, but generally it looks like this so and it's not the API, it is that a friends we are working here, but generally it would be the same similar approach to to.
To a Pi. So I'm testing Tesco creates other required columns. So here is the list of required columns. If one of the columns will be deleted or added then this test will require modification.
But you see that?
These tests will also be needed to modify, right, because here we also are testing.
All that.
At the whole record, but for example here we are only testing single fields, right? So we are only testing what exactly is affected by this specific case. So here the product name and package name, custom group, product type, those are the things affected and if I will add something totally not related with this test, this test will not need any modification so.
At and for example, here we are only checking human name column, right? So.
Hmm.
It is difficult, but it's good to spend some time on finding the right balance at the beginning, testing the the whole object is easier because you need 1000 drug moving forward, but when time passes you will need some refactoring. Also on your test if you will see that it is painful to add something new to the API to the like here to the to to aggregator.
It is good to.
Ohh.
I see why it is painful and fix that by factoring so so this this.
Refactoring part is at different stages, right? So we are not only doing refactoring after all the tests are passing if.
And adding some something requires a lot of changes in in the test.
If writing a tests and making sure that we have everything covered for writing, any future needs us to modify all those tests, then we can stop, start from refactoring and then moving forward. So not that so, so not always. Writing a test is first thing. Sometimes you can start from refactoring, then going to the test, then making the best.
Hmmm OK so with.
A quick question, yeah.
Family.
Xbox code and example say your name in one test.
Best in the scale.
I need to have the 200 and.
Is.
That's why your name is this.
So and here is when.
And.
That's a really good question because I wanted to show you that this only that you are so that the list here is what we can begin with. But.
At.
The next task is for you to write the first failing test.
This one.
But the question about this tool we can met them.
And that's totally fine.
At.
What I do often is that I'm starting with this test.
I'm checking that OK, it's it's there.
And to pass this test you don't need to write any of Sqdn name in returns. You can you what you need to do to pass this test.
He's only.
Writings and point like this, but does nothing at all.
It just exists. So this is the first step that you can check the the minimal step that you can take to move forward. So I'm adding the OK status code is there.
I can move forward. OK now how to get the SKID? So those are two, two things. Separate things that you are checking. First if my first API part is set up correctly then if it returns actually what it's needed.
Ohh so separating those two steps at the beginning can help you avoid some problems. OK what was wrong? The path is incorrect or the skills invalid but.
And many times, and I'm ending up with the.
Solution that you propose, so at some point such test doesn't make sense.
And that's also important thing to now to know that.
When you work in TD you are create you are creating sometimes test that doesn't make sense in the near future. So when you will have a lot of a lot of those tests, you may move the check for 200 status code to a separate test and.
Like 2 to two. One of the tests and uh or not have it at all. So.
Hmm.
It's good, but I would start from a smaller steps.
And and then move forward with with actually.
Creating hand and point out that return something.
So the task form write your first failing test and and try to use as fuel lines of code as possible.
As few. So that's a challenge for you guys.
Try to write this test with last call last line of code that I did.
The one rule, there's no cheating, so you need to be compiled with Pepe and black coding stuff that so you cannot write some one liner properly.
You need to write something that is valid, but here is an example from fast API documentation how to start so you can have fast API test client app, fast API, some endpoint testing client. So this is something that we were showing for the.
Whole time. So write the first Test.
Failing test so that test get test you for not existing ID returns for for status code I will pass you the best name in the chat so you can copy and and paste it. Try to write the first Test.
Or that one?
If you have any problems or finished.
Does.
Let me know.
I remember that you need to write only this one test, right?
And you if you have any questions like Mali, raise your hands. Mali. Come on, come on.

Mohamad Ali Nasser   1:32:50
No, no question. I thought that you want us to raise like at least there is my hand if you're done.

Grzegorz Kocjan   1:32:57
Okay.
Hello.
Phone.
Off.
That individuals code means that there is something wrong.
With.
And the general code structure in such cases are usually open terminal and run tests from Python command directly.
Here it works, yes.
Even if I try to refresh it.
Think hydroxide thing.
It's strange that.
Can you go in the terminal to?
Uh. Holder above.
And here run I just.
That's.
Right.
Ohh.
Yeah. So no module named fast API. You don't have configured visual environments to point to your.
And ohh OK which ones better so that the?
Ohh.
You fell into the trap that you had five test installed globally.
In your in your Python.
And it was to the test picks what working for you. Even if you had, if you even when you had not configured this code to use your retail environment.
But now it is. It is failing on discovery because it's not able to find fast API. So you need to change the path to to your.
Um.
Have Internet.
Basically.
Tell me another.
I.
You you are really bad student, you know are not listening.
You have.
You are doing too much. Yeah. Patriot info.
I'm usually when.
I want to search for.
Virtual environment path.
I'm running Peter Shell because I'm using it all the time, not the.
Market.
Uh, but the shell because it is running the shell and showing exactly where the puff is and I'm.
Running it, the colours are terrible in that.
In the projector marginality, the the path for that. But the Mali type like.
Maybe he's not ready for the brain. The.
Yeah. So there is this poetry and info that has Molly typed and and it actually does the same.
Uh, I think.
And even more.
I will not be able to check if you write less.
Line of course on me, because you for sure write more of them. Ohh for.
But the test.
For the test, the test, but totally.
Fully 2 lines of this.
It's the.
OK. And of course some example.
2.
Ah.
Is it working for you now?
I can't you.
The bottom one.
Yeah, I mean the bike.
They've finished me speak.
Hmm, OK news about.
The trust? Debra.
Oh my God.
Boy is that ohh 2.
Connect to WSL.
And now you need to open that folder you pull the.
Get report in WSL.
Or in windows.
Ohh yeah yeah yeah, because you had opened, so you need to have you need to be connected.
From here, open the folder, point the folder with the project.
Because you had a visual environment installed on the WSL and you need to run the visa code also there.
Yeah.
Ohh OK, close that again and.
That's from new file open.
Folder.
Here you are on. You're not on the moon.
You are.
Or not.
No, you are disconnected from.
Can you click again here?
Connect to W cell.
Yeah, now it should work.
The open folder not fine.
You need to open that folder.
Ohh you will have the settings.
Start again.
Hmm. And he and tweet that I want. And OK now.
Because this will open the settings. Settings are on the. This code settings are on the on the folder level.
Yeah.
And now still you need to configure the.
Development.
Control shift key.
Interpreter type interpreter.
It translate interpreter here.
And enter interpreter path.
It's not that one. You need to find the one from the cash.

Mohamad Ali Nasser   1:42:10
He he needs to if he wants, he can copy and paste the.
Um, the path and they just.

Grzegorz Kocjan   1:42:16
Heard. Yeah.
It took click the to.
Hard to.
Yeah.
PATD.
Mean.
And here you have all the facilities set for the Python.
And anyone you choose will be OK.
And now you can.
Right. To test, run the tests here.
Okay it's failing. OK, no problem.
And that's like totally normal thing that when we setting up new project when we starting something from the start, there are tonnes of things that we can misclick we can do incorrectly that while this is the reason for running this test, it's.
A test at the beginning to to limit the number of mistake problems that that we can have in this.
Potentially.
Hmm, mature environment that is for like many years and it's not not there so.
And.
While I was working on this workshop, I did exactly this solution.
So I.
A cut. So I'm not copying, I'm cutting from the list. So now the list is limited to only three. There was this one test here but.
I caught it and post it here so you can see it. It is actual test implemented.
And.
There is nothing more, so I know that I want to call client get.
I want to call USURL and I'm passing some. OK. This is the idea not existing. I'm signing out loud that I'm more that I want to fetch something that doesn't exist.
If I would pass something like.
Read that.
123410.
From the first look, it doesn't look like something not existing, right? It it looks like something that potentially could exist.
And and if it is possible, I'm trying to use variables that saying out loud that hey this ID is not existing one, sometimes it is not possible because we have some validation of format and for example this needs to be done by WYD then it is not possible to do that. But if it is I'm trying to do so and now interesting fact I did just what I told you to do. So to write the first failing.
Test.
And for me, I failed writing failing tests because it is possible.
And who wrote at the beginning passing test?
The one who's talking, yes.
By accident. By accident I wrote test that I was expecting to fail because.
I was get asking you for not existing ID returns for 04 status code.
And method in details.
I started.
From taking only the status code, because that was the minimal thing that I wanted to check if the code code is correctly.
And.
Ohh here is too much there was without this message detail and when I run this test it passed. Why it fast? Because 404 status is not only for not existing case you but also this is the code for not existing path. So when I run this debug and when I was running you see that I'm using this response Jason watch I have details not found and this is the default response from Fast API that this.
Love doesn't exist, and not that that the SQL doesn't exist.

Mohamad Ali Nasser   1:46:42
I had the same.

Grzegorz Kocjan   1:46:42
Like.

Mohamad Ali Nasser   1:46:44
I had the same.
Thought because it was passing on 404 but it was passing for the wrong reason.

Grzegorz Kocjan   1:46:52
Yes.

Mohamad Ali Nasser   1:46:54
Then I had to add.
Like a condition that.
If not existing needs to be in adjacent.
Otherwise, return for four.

Grzegorz Kocjan   1:47:05
Exactly. So as you as you can and and I didn't want to answer that when you were asking question, if you can match status code and respond to one test because I get the list with separating the response for, for and messaging the details.
Because I used to do the smallest possible steps and this step was too small sometimes.
The.
When, when, when, when I'm trying to plan what I want to test. I'm I'm trying to plan something that doesn't make sense and that was also the case and the don't. Don't be afraid of such situations. You just need to react on them, OK. It failed, but it failed on tiny thing. I know precisely. OK, I did something wrong with this. That it is not valid. That's not failing. But as I was referring at the beginning.
How to test the test?
This is the way I'm testing if this test makes sense. It didn't make sense, so I I I'm testing that.
And now?
When I will I had.
That.
Hmmm. The things.
So I have all the tax here with all the solutions and I have read with a question mark try to write the First Rule test that is and it was not failing. So I read.
Or.
I fixed that.
By adding the check with the SD not found. So having some more precise information and as you can see, we failed on where is the line on the message right? So details is not found while expected was excuse was.
Uh. Not found, so we felt on this assertion this assertion was OK so.
When we have this first Test, I have very important question for you guys.
And that's a tricky part.
Because I I forget about this for the second time when I running this workshop and I run this for the second time so it's 100% hit with my mistake.
But.
How many of you did any of commits during the workshop?
So the question for you to think what would you do if something could go totally wrong?
If you would totally mess up.
Not.
Like.
What if you would just get received a Windows Update while going to the toilet and you will get back with restart the computer and no history of what you did previously.
Added Barry.
Sad and.
To be honest, it's not something very unlikely to happen. It is very often happening that we are changing some lines. We are doing some refactoring, moving, copying some code and by accident makes sense, making some mistakes that breaks everything.
And they have no idea what happened or we just by accident deleted our work.
And.
In the same book TD by can't back. It's not my advice like I would give you this this advice without reading this book, but the father of TD also is giving that advice so.
It's pretty important though, commit at every step.
So every time.
Ohh blah blah blah blah blah blah blah blah blah every time we are switching from one step to another one. So if you have writing, if you have test, do the committee. If you have passing the code do committee further refactoring do commit.
Though them as often as possible because the true flow of D is that you are writing a failing test doing commit, you are making the test pass. You are doing the commit but.
There is a possibility that test will not pass, that something will break and the advice from can back is to do the Git revived.
So when something is not working or if you are doing some refac, especially for refactoring.
And that's crucial art. And if you are doing refactoring, if at any point your test are starting to fail, do get reversed. So at each step of refactoring so the whole block of refactoring could contain like 20 of commits, do small commits as soon as as frequent as possible. If you do some small step, just do gift card and you may ask OK, what to do next? Should I?
Three domestic W with 100 commits and push 100 commits to to the main branch. No, that's not a good thing to.
So how you are working with your regal project? Because this don't bring value.
But what's up?
Something's that goes so important thing. If you will go to this past APID if you want to see how my comments look like here there is a bunch of comments for each green, red, green refactoring. There are bunch of comments about showing each of the steps that I took to prepare this workshop.
This is not the way of doing committal guitarist.
This is a way of working, so I recorded that so we can go through that process of the workshop.
But what you want to do is to have a single commit for a single.
Uh.
Uh.
Portion of work. So for example.
Few test small feature is a good good commit. You can have commit for each acceptance criteria. You can have commit for a whole task. It depends on you. So there are two things that you can do work in continuity with your TD workflow because we want to do as many comments as possible as most frequently.
But we don't want to push the mess to the main branch, so you.
I love, love, love not the this from.
So whenever we using GitLab or GitHub or.
All other possible.
Hmm.
We can check some Ciprian works.
Ohh.
So if you are adding.
No, but I don't.
When you're cooking, you will see that.
So so when you yeah. So when you creating a magiquest there is a check checkbox squash commits when merge requests accepted. This is the one of the solutions. I don't like it but it is the date one. So you are creating bunch of comments we are pushing them so in the next request.
Ah.
Here you will see 100 commits 2000 commits whatever. How how, how how big it is will be but at the end you will have one squashed commit when it will be matched to the main. That's the simple solution to do and this is a good starting point.
Thus, to be familiar with the flow, what I do.
Ohh no, no, no, no, no. I will not use big. Come on.
That's that's my pupil. Poor, poor.
I want to make my life more miserable, so I trying to test Bing for a while.
And I I I regret that gate.
So.
This is the command that I use for for working in.
And work.
In in any of the code I'm using Bitcoin Ahmed. So what can what Amanda does?
Does it show some nice graph now?
Ohh OK, I will watch the.
And in the in the gate.
The here you can go through all the solutions on all the things that I was doing you have.
Not the branches, but tax on each task. You can see the task and the solution, so you can go step by step by the task and I would propose you not to go to the final solution at the beginning, but if you will review them, go go through the step by step to see that progress, how it was and and that would be the best way to to move it forward.
Thank that. No problem.
So what Amanda does is, and it's not shown here in a nice way that I wanted to see.
Yeah. No, no, no graph at alls.
So generally amens replaces the existing commit.
You have always one commit on your branch at the beginning. I'm even not focusing on.
Creating a good commitment and just creating Git commit Veep VIP so so name will be given in the future when I know what exactly I wrote it will be easier to to give our commit message when everything is ready. But I'm using Git commit amend every time I add something and it will replay replay that one commit in the in the history. So when I push it, it will not require like squashes.
From the.
Digit lap or GitHub. It will be already ready for, for, for, for my drink. And why? Because sometimes it is worth not to have one.
Uh.
But sometimes it is worth to have like 12-2 commits, 3 commits and and have it organised in a better way to have a better good comic history. I care about this history, but that's a separate topic and you can watch my presentation about that that for all that I think I was also helping at the beginning of my work. So probably before you marry joined, but I don't remember.
It's ready to go. I showed this the picture around the.
Ah, now you are telling me.
When I when I.
Ohh yeah, I understand.
This doctor OK?
So I mean is really helpful after Ahmed, you need to push force with this of course.
I.
It is more difficult.
I'm. I'm saying I'm. I'm saying that.
Have a strictly you need to learn not only eat basics, but a little bit like middle to understand how it works, how committed our stored and etcetera a good to know. I don't know if you were showing his stash.
I don't think so Marley for you.
If you are working with Git comment, you will always replace the commit. So in the history you will have only one commit, nothing.
Uh, more?
But there is a stash. This is extended history and it is held only locally. So as I told you.
I'm using VIP as a git commit message at the beginning and you have proof random random gift history and and it it looks like this. So I was using VIP.
I commit message.
And it's not Tash. **** get breath.
Log please.
Laura lock.
Sorry this is what I want to show. It was the simple and.
Don't talk much. The weather in church, so as you can see, Reclock has some additional informations on history, exactly what was happening with my gift that every command that was Fran, it's.
Result is stored in reflec, so I was reusing VIP moving to dynamic A P I I was doing some check up But then I was doing commit on and so I was replacing the commit that was previously this one with the new Shah and then I was doing again.
I meant and replacing the shop.
But why? It's the number is interesting to download.
So maybe the histories.
I don't know.
But that that doesn't mushroom.
It is how many commits are there from the head.
OK, but it seems that should read it pops down, not.
Yeah, it it it. It is the the newest one are on the top changes the new K then it's the number of comments from the head. OK. Yes. So head is here. So this is the main. Yeah. And this is the number of commits from the hat. But it is it negative numbers.
261.
Comments.
From the main, but what it's not the commits it it is activities on the gate, so.
And when doing Git commit command in the Git history, you will we've git history will not be able to just go back to the like, not the previous state but the.
One being before, so before or three before, but you can go to amend and OKI can checkout this one or this one and I will I can move whatever whatever version of this comment in the past.
Uh, what? And and you can do that, but by checkouts, so git.
Eat.
Back out and this shop and it will work.
I will not do that because I don't know.
Yeah, there are some trailers here, so I don't want to lose it.
So.
Ref lock is always there to to show you exactly what was happening with your branch. But reform is only start locally and it has some.
History limit.
It is like by default I think that is 2 months.

Mohamad Ali Nasser   2:03:16
So.

Grzegorz Kocjan   2:03:20
Of history, Emily.

Mohamad Ali Nasser   2:03:22
So.
So there's two things. If I did commit, let's say 5-6 commits, I can do just get checkout. If I wanna go back to three commits before and continue from there.
Um.

Grzegorz Kocjan   2:03:37
Yes.

Mohamad Ali Nasser   2:03:38
Or or I can go there and do an amend without commit, but continue from the earth as it is not in conflict with the new commits. I can still do that.

Grzegorz Kocjan   2:03:49
So generally, if you will go back to the path, so OK, I was doing comment three times and I want not to reverse the latest changes, but go to back in the past like 2 commit before to to amend before then you will create a separate branching. So like fork right? So so you will have your newest newer version on the right right side, but you will start working on the left side. So if you commit from that part you will.

Mohamad Ali Nasser   2:04:07
OK.

Grzegorz Kocjan   2:04:20
I have, like you branch and it is important because if you want to get rid of that.
Branch to call me if you want to.
Move your brat.
2 something in the ref log to some commit in the past. You need to revert revert.
Branch to.
Hmm to previous commit and usually I'm I never remembering that command because I use it like once a year, twice a year, so not very often and I'm searching OK how to do that.

Mohamad Ali Nasser   2:04:53
Hmm.

Grzegorz Kocjan   2:05:01
And here you will see that OK this is your detach hat.
So I'm checking out to, for example, the comment from the ref log and if you want to make comments while you have the blah blah, you need to.
Pick out some branch to no, it is critical new branch.
But.
Where is.
No, it's not that.
Hmm.
Reset it should be some kind of reset.
Okay maybe not this season.
How to move Branch to comment?
Hmm.
Okay I will. I will, if you will have such case, I will set for that comment. Maybe not. Not spending too much time on that, but.

Mohamad Ali Nasser   2:06:10
He.
Yeah, no, don't worry. Don't worry about it. I'm just don't worry about it. I was just wondering generally if.

Grzegorz Kocjan   2:06:19
Then there are the you need to move your branch to the committee in the past to the red log on on some commit on the ref lock, and so you will be able to continue smoothly as from from that point.

Mohamad Ali Nasser   2:06:31
Okay.

Grzegorz Kocjan   2:06:33
Usually I would say that to to find that with.
OK, so the.
Next step of our TD workflow and OK, we have something like yeah school in English aggression.
Similar. So we moved to eat from TD it it it, it might feel that it is not connected but it is. It is a crucial point of TD to also work with Git.
And to have.
Good understanding of it because you will not be able to fully like have like.
These powerful 10 each developer performance in TD.
If you will not know how to write test good how to write code good, how to organise your code in good architecture and without knowing it.
So those are all connected things that needed to need are needed for you to actually work efficiently.
With TDP, because without frequent comments.
And it.
If something will go wrong into the deal, will need still a lot of time wasted probably.
Hmm.
On on finding what was wrong. OK, next task make the test green. So I wonder what is the Cyprian solutions for that. So we have this one single failing test.
That should look like this. It is failing because I'm not having the SU not found message. So the task 5 is to the make the test green.
And like I was mentioning at the beginning, it doesn't need to be pretty. Don't organise your coat. Let it be ugly. Don't organise it.
By the simplest way to pass the test.
And there is a hint you can return specific status code and message in Fast API with HTTP exceptions. So here is the link.
You can search how it looks like, but generally.
Like the first code example, you need to import HDP exception and write that exception with status code and details and it will return you. So here should be some kind of like SK U not found. So the challenge for you again.
Do as little as possible. It can be ugly to pass that test.
Try not to do more.
And you can raise your hand if you can already did that. That part. OK you you also did that partner, yes.

Mohamad Ali Nasser   2:09:34
Yeah, yeah.

Grzegorz Kocjan   2:09:38
Not from the yes. So that's a good point and I don't care so.
The four just here, so I need to.
Make it work so the the the paths of give giving the solution might be used to be different, but I want to show you exactly what I was doing because a low because there is some trick here.
Switch that commit.
And the trick is.
Not to do more than it is required for you to do so to pass the test.
That SKU is not found. Who we need to create the API and write the exception, nothing more.
That.
Difficult.
To do.
Yeah, everybody does it. It's it's normal to overkill because everybody was teaching us. OK, you know what to do, just do it.
And now don't do what you know that you want to do. Do only what is required by the test. It is why this test driven development. My test drives me only to write the exception, nothing more. So right now.
It's green, it's OK. Requires requirements are passed.
And.
Mali how did you pass the Python code?

Mohamad Ali Nasser   2:11:23
You.
Will you go to format?
You have three dots.
There you have a code snippet. You paste it and you select Python. If you want Python or.

Grzegorz Kocjan   2:11:38
Nice.

Mohamad Ali Nasser   2:11:45
Sometimes it's not as nice as in matter most, but it works.

Grzegorz Kocjan   2:11:50
Like what they did the same.
Like but I.
Ohh here is the.
Like qualities.
3 balls.

Mohamad Ali Nasser   2:12:05
So.
You see, when you want to type in the.
Let's say in the main channel, not in the teams and the video call you have a.

Grzegorz Kocjan   2:12:17
Yeah, here.

Mohamad Ali Nasser   2:12:17
And format.
Then you have like a A, you know a.

Grzegorz Kocjan   2:12:23
Ah.

Mohamad Ali Nasser   2:12:25
The Scots snippet sign you see it neither near the link insert link logo.
Icon.

Grzegorz Kocjan   2:12:32
Sorry.
And.
OK, amazing. I didn't know that that that that's possible in thing. It's so hidden.

Mohamad Ali Nasser   2:12:44
Yeah.

Grzegorz Kocjan   2:12:46
You okay so.
What? Uh, what you did, Murray, is.
Totally. What people are doing.
Ohh, you are actually a step.
Further, so you didn't do escort queries in database, so that's that. That that's good. You just fake the database and it's amazing because it is one of the best in our workshop.
But you also, but you still move too quickly and.

Mohamad Ali Nasser   2:13:22
Yep.

Grzegorz Kocjan   2:13:24
To, to be honest, it's not something that that I am able to avoid because here.
Ohh.
I'm telling out loud this this presentation tends to event planners don't do more than it is required to pass the test. Don't do more, but still.
Uh.
When you see the the these statistics though, those are two bugs.
One bag was done in a.
I don't remember. OK. No, no, never mind. But.
Second, back back I did because I wrote too much code and it was not covered by the test.
And when I was doing some refactoring I noticed OK, this line doesn't make sense. It's stupid.
And.
And then I write the test that was actually missing in the process. I didn't test that picture at the beginning and turn. Now that that that I had a huge bag because.
In this task I was grouping the results by the student group, so I have some great book here and it turned out that I was passing all the students to the all groups. So for example I have students for three groups and in each of the groups I have students from all groups instead of from the given group and that was the bug. And so that was a huge bag, right? And it was not covered by the test because I did.
A little bit more than it is needed.
So like again, it is difficult to write only what is needed to pass the test.
It is a simple is. It is a really simple case, so here it was easier not to do more and to pass the test. But sometimes she you know you need to write some magic code or or or you need to have some structure to actually make the code running and then by accident you are actually implementing something that that is not tested yet.
So.
It is in real life really difficult, but it is worth remembering that those tricks are totally allowed and and it those it is often a good way of of, of working.
And in fact, the test that the least that I presented to you at the beginning. So I started from the Western Marios, right. So I started that, OK, this SQL doesn't exist because I have experience that it is easier to test such things.
So if you have some acceptance criteria, you write some like five of six different test cases. You are not starting. OK, I will pick the first one you need to.
OK, no problem.
You need to think.
Which of the tests that you propose on the list will be the easiest one to to to?
Ohh I have to write at the beginning.
OK, let's move forward. This one will be probably quick because you already have the solution. Task six right, that test that cheques you guess you returns start to 200 for existing SUID and and how to add existing SKU implement your database and add it to the at the beginning of the test example database implementation and it is very similar to what.
Maria already.
And so do you.
Hmm, so we can work on on a dog thing.
Hmm.
Your spanner in the function, right? So I will show you my result.
Ohh it is tasked #6.
Reached commit.
I just found today that it was away and I I'm amazed that you can. It is terminal right? And from terminal you.
Then go through the tax and just preview what is going on there so.
I created a database, it's a dictionary.
And and I wrote the test that test get asked you for existing. Kindly returns 200 status. I added that.
Item to database get the schedule and return status code. It is a little bit different from what Mali posted. It is very similar the difference.
For me is important.
Because I have.
You really created a I know if you are listening with the bottle or not.
But let's pretend that you are.
Then.
You created a database with initial.
Ohh.
Item.
And if you will start writing the test, that will check if 1234 item exists, you will not have it in the test to to see what exactly is expected here I created a empty database.
That can be referred in any part of the code, but inside the test I'm.
Creating an item that actually represented.
To be honest, this is.
Total ****. What is happening here? Why? Because database is a global state.
If I if I will modify this test here.
It will be shut for other test also.
So.
OK, if you are back, Molly, I repeat what I just said at the because it's important the difference between your solution is that you created an item already in the debt that is on the top.
And in my solution I put the creation of the item for the test inside the test. So this is the structure given.
When?
Then. So in the test I have already what is given as an input for for the test, then for for for the application. Then I'm running the application and checking that result. We with this approach and not forced to scroll to check OK what exactly the schedule looks like and I mentioned that it is.
Ohh ****** solution, it is like a very draught of how we can work because database DB.
He's a global state, so if I modify this state here.
If I modifying this state here in this test, the state will be shared between all the tests, so it's not a perfect solution, it does. It is our really best solution, but it is something that we can work with.
And we can improve that in like really near future so.
Hmm. We can move further task 7 make the test green.
And Molly, do you have the test for checking the?
Uh, if that still exists?

Mohamad Ali Nasser   2:21:25
Um, yes, it's uh, the one I shared no.

Grzegorz Kocjan   2:21:30
It is for not existing and for for existing.

Mohamad Ali Nasser   2:21:32
Ohh for not existing. Ohh yes yes I have one. Well for the name. Basically it's the same As for the Askew I can.
Sir.
Q i.e.

Grzegorz Kocjan   2:21:45
Because task 6 is to write the failing tests.
So.
So I still have the 404 not found because I didn't implement anything. I still have the right exception, so I'm not writing too much code and this is again the way of testing the test. I can pass the place the break point around the ******.
And I see that still ask U is not found even I give the schedule so I can check that OK in my database SQL is existing but if the.
Test will not leave the application will not give me that SKU from database I.
We'll get failed test report.
So this is something that I I'm checking that writing that test first, then writing the code.
Hmm so.
Task 7 make the test green.
How to make test pass? I'm getting the scare you from the B if USU is not known then write exception.
And again, that that's a tricky part because.
I could and.
Just return SK you right because I it it is obvious thing to do.
You get the escape U.
If it's not, is, if it is known, then I'm writing that this escape was not found.
And it is obvious that it is if it is found, get through your should return the schedule right?
But how I will write failing test for checking if I'm returning correct schedule if I already returning it right it will be not possible to write failing tests so.
I'm writing the test the the the functionality that does not return anything, it just.
Ends with 200 status code.
So right now I can run all the tests and not existing code returns for for earn status message in the details, but the existing scale glass returns to 130.
And it was something that also we discussed when pilot asked if we can measure those too happy PAP tests. So yes we can, but.
The.
We can implement that separately.
And OK, of course this is a.
Hmm.
Very small.
Example, maybe a little bit overkill from.
From the other side, right? So not to writing too much, but actually doing too less.
Uh, maybe in the real world, we will make those steps a little bigger because they are tiny and but this is the the, the, the, the workshop. Only we cannot work on fully like real case scenarios. And we are working on.
On something fake and and I want to show you the flow that very often it is really easy to write too much.
So uh, task 8.
Write up test that no.
OK.
I'll speak to B. Write the.
You DB.
It's not.
It gets.
Yeah, that totally makes sense. We'll move to that. But I I will show you how it it is tricky to to work with such big B and.
But but but it is like the next task and I I will show and explain that for for you both.
So Malik, can you can you tell me at what stage are you? Because maybe there will like move like 2 to further.

Mohamad Ali Nasser   2:26:33
Yeah, I I have the status code 200 and a test to get the ID ask UI.

Grzegorz Kocjan   2:26:41
Okay.

Mohamad Ali Nasser   2:26:42
That is also pressing.

Grzegorz Kocjan   2:26:44
OK.
Ohh now we need to write the test that cheques and get sqrd returns as KID and name with expected values. Run run that, run that test and verify that this failing. Then and only then write code to pass the test.
So.
A little speed up. You know the flow now. First we were writing the test, then running it, checking if the spelling and then writing the code to pass the test. So we need to test that text if we are returning as KYD and the name.
Hey I will show you.
Umm what I did?
How my solution looks for for that?
So.
From the beginning, from the code right now, after writing those tests that you will see, I only then wrote this return scale before it was not existing so.
Assume that uh.
It is still a red face.
We have a the previous test and now we have the very similar test, but I posted that two versions here.
So the gun babe, please also see that there is a slight difference. Two different approaches that we can take, of course, probably even more.
But get the schedule for existing ID returns SK UID and name version one. So we are preparing the SU we have ID and some name we are fetching it from.
Hmm, from the API and we are asserting response Jason equals to.
So Jason object and it was something that I was mentioning previously that can be done.
But there is a second version.
Very similar, but assertions will look differently. Now we are assigning new variable results SU, so this is our Jason result and you have two separate assertions.
Without searching if USQID is equal to something and name is equal to something. Those are very similar.
Best.
And both of them have some advantages and disadvantages.
And.
Which of versions though?
Comparing the whole.
Dictionary or filled by field. Which version is better depends on the product.
And depends on material of the product.
Like I was mentioning the the full dictionary comparison assertion is better at the beginning usually and field by field is better at some later stage.
But even if it is better, it is easier to.
Past something totally invalid that way. So.
I was telling you that I we have separate.
The best for tracking number like the the list of the fields in that frame. We could also do the same check as a separate test here and then checking field by field.
If we will only have such tests like such only the the video version we've checking field by field.
We can.
At additional risk result like additional field to the response and none of the tests will fail by accident, right? So it's not a very good situation when we modify the behaviour of the code and nothing will expose in our face.
So we will we with with such approach we need to be careful but and this approach is safer but will be more painful.
We will have even more test.
So it's worth remembering that both are correct.
And you need to make like I would say informed decision which version of of tests.
To to use and.
Important thing.
And.
Repetition.
Dry.
Thrive principle. So do not repeat your do not repeat yourself. Don't repeat, don't repeat yourself. Right.
Don't repeat yourself. Doesn't apply for tests.
In testing, you want to repeat yourself. If you are creating here an object.
Don't use the reference here so.
Do not do something like that.
Cancel.
Because if you will pass something wrong in initial state.
I you will take if the wrong value is returned, but as a result and here is not such.
It's not such a big impact here, but in.
Many, many, many tasks. I saw that people were calculating something passing into the.
Ohh there.
Hmm.
To the algorithm and checking the output is on the output will be the same parable so.
Like I would say something like.
Like that.
Is really really wrong.
Because you don't know if the.
Hmm.
Operations that you write here are correct if you are. If you didn't do any typo.
But with this work.
In such cases.
To pass some real value, even if you already have this value in this variable, pass the row.
Value without referencing.
Ohh.
Off.
Because why?
Because if we make some typo here.
And.
The equation will be incorrect. The previous version will still pass. Now if we are.
If we are doing calculations separately and we are asserting only the value value.
Without referencing to variable then this test will fail.
So this is really important, not.
To dry.
My, my, my throat is dry.
But and the dog?
OK.
But your test.
Please, please don't use dry in tests.
It is. It is very often a a back door for bags to use and they are using that.
To to do so, one more thing, what what is wrong with this?
Ohh, with this approach with database it it's not very good because.
If I.
Delete here.
Ohh yeah, I have key error because uh, I commented out.
Return. So if I run this test.
I will get OK key error ask you.
ID because it will result with.
The result here response if with the back.
Is that a?
That's KU is not found, right? That's correct because I.
Hmm.
Deleted the but.
Hmm.
The price? The price.
What happened?
Like I was mentioning.
We have a global state that is shared between texts.
So even if I commented out the setting up of the DB.
If I was running previously this test, I was setting up the DB for this test and for this test also.
So we have a.
Very good, very, very bad situation when our results of the test depends on how many tests of and we are running and the order of the test.
So. So that's not a good situation.
And we will try to fix that.
So your task the 9th task. I think that we will try to do that task now and after this talk will have a blank break. If that's OK for you money because we have we had planned to have a break since 1:00 PM to 2:00 PM.

Mohamad Ali Nasser   2:39:30
Okay, no worries.

Grzegorz Kocjan   2:39:32
So task 9 factor your test to use by test pictures to create existing SQL in the database if you don't.
Move your API code to a separate module. It is a good time to do it also.
And.
Because I don't know if you noticed.
But for now my production code so my first API, my endpoints and also test are all existing in single file.
I don't have anything in fast 8PID module here. I have only nothing more everything taste still is in test underscore X. Why? Because.
I don't want to spend time at the beginning on having a good structure, good naming. I just want to feel some acceptance criteria. Have something called work, some code working, and if I don't have any structure to put the code, I don't want to bother.
Doing that and it is sometimes easier to to do so and that way. Of course, if you have already.
Well structured code and you are adding some feature to a mature project. Then such a pro doesn't make sense.
But if you are starting with something new or something from scratch, even it is sometimes possible to write everything in a single test and only then start to extracting that to to functions. But still in our single.
Hmmm. File.
It's it's not something like that. I would recommend you to do always but.
It is not a.
A crime to do so because you will not pull that any further and etcetera, OK, so try to use pytest fixtures.
I would normally do some explanation once what features are, but you both already very familiar with that.
Try to make it work. I will don't show you that.
The mystery shop. It's not very, very straight forward so.
Don't worry, I will give you like 5 minutes. So we'll end up at 1st at 1:00 AM.
PM.
Okay I will try to.
I I will start showing you.
What I did for this ask this time I will not show the like the the result in the PS code because here I took a little bit more steps than usual and like I said many times I'm trying to split work as to as little steps as possible. So between task 9 and starting working on task then.
I did several small steps so I I have here right 123-4567 commits. We factor refactor, refactor, refactor some red and green and refactor.
So a lot of small refractories and like.
That's important. Each small refactoring should have a separate committee, of course here.
It is.
Not it doesn't make sense to to to commit and describe everything you are doing in normal work, it's you can always use just the amount and and move forward. But here I wanna show you exactly what types are in this plugged in for the purpose of the workshop. So.
I at this stage I only moved the API code to separate module. So I created a main pipe file I just copy and paste the the the function and in the test I imported the app and the the DB.
Ohh instead of creating those this API and point in in the test.
It is in uh. It is important here.
So two separate files and that's commit or amend.
And worth of change and every test is passing here. Remember, refactoring is a good refactoring when everything is still working. If it stops working then it is screwing around. It's not not refractory.
Create picture to create single SU so.
Inside tests.
I created a single SUDBSODBI move this those lines that were at the beginning of few tests to.
Ohh.
To uh fixture.
And I added this picture to a single test only. So as you can see I.
Use it only for single test. Other tests are are still working on all the way. Creating this schedule for each single time but.
I'm moving step by step, so I'm taking another test removing removing the the the database, adding manually and moving to a fixture. So I'm doing that step by step each time running all the tests, making sure that everything still works. That's really important to do small refactorings and checking if everything is working. Why? Because like I also mentioning.
If something stops working in refactoring in working in 3D.
You are doing great revert. So I want to revert as less as possible, so I need to commit as often as possible and again only the commit with with with factory.
Another understand.
And.
Now if I have those tests, I want to fix a this bug when.
Hmm, like I told you.
If we.
Uh.
That we were not cleaning up the database, right? So if we were, if we were moving the database at this one scale and we run all the tests, it was still passing. So I wrote that test that cheques if my database is cleaned. So at the end of the file I just wrote the test test. Make sure that we clean up the database I.
Don't use the fixture on setting up anything and I just checking if it is returning for, for or or or 404.
And it was failing because I had been back.
So if I have failing test now, I can improve my code and make it fast.
So.
I did definitely.
And did debit clear and debit clear and then beginning at the end, of course, at the beginning is something here I feel like.
So much it's not fully required, but like for a sake of of making sure that it is clear. Then I started using that.
And I use yield. What is happening in this picture? What what? What did he held is doing? So if I'm calling this using this picture, this is what is happening before the test.
Here we are returning to the test and at the end it is happening after the test so yield.
Allows me to get back to the test and.
Have some clean ups after running and the test this how generators are used. This is for what generator are used in in pictures.
Ohh.
DB is our dictionary.
It is, it is still, it is still.
Our our dictionary. It is the.
And as the.
A small step I was doing some uh remaining in ups in the test names.
So.
Nothing fancy.
This is a trick to have expressive test names and make them not that long. So what is happening here? All the tests were starting from test get rescheduled for.
Or get test get get SUBYID. So I rename the file to test get SUBYID and now I'm not.
Saying that I'm testing get killed by ID, but that's not everything I D or transfer of four because now I can remove that repeated part from the name and this is the pattern that we can like. Fracture OK says get SUBYID and now I can create an A and this could be a folder and inside this folder I can have file test or non existing ID and separate file for test for existing ID and then only like.
OK, return for four status and return message or return.
200 tattoos, sqdn name, etcetera, etcetera.
So if your name very expressive names.
Are taking too much space and you have some repetition.
In in test names at the beginning, for example, you can like group them in single file and and and and remove that repetition from the name and make it shorter. So as you can see right now they are are are not not that long.
And that's it. We have a database that is cleaned, that is.
Did we didn't spend much time on creating the database, right? So we didn't need to run an immigrations, etcetera. It already exists. The whole table of scales are there.
So that was a pretty fast implementation and if you would do that in like a normal workflow, it would take.
Maybe like 20 minutes. What is the benefit? You can now share your code.
And with others, and they can for example start working on front end and this is exactly what I did at the beginning with events planner.
I don't know if you remember that even planner didn't have any database and it was working on a park. That file that is stored locally.
And I give it to mark. OK, Mark, here you go. And if he was clicking that and while he was pudding.
And events from events planner. They were also stored in dictionary.
Or like weeks like two or three weeks. They were only starting dictionary and the even we deployed that version to the Azure and it was our development that okay you can click you can play around the front end with the back end. It works but if we will deploy new change it will just remove your all the events and like.
It was possible to tell the application in that state.
And to do a lot of things without worrying about about about infrastructure and what's nice during Marek work, Asha work. When they working on the front end, we were able to focus on creating actual database and we'll get back to that after the break so.
Uh, we'll see at 2:00 PM. Maybe. Maybe I'll put longer because we are already after the first PM.
So I see you. Then I will stop the meeting like stop the recording. I think. Or I don't know where it's recording.

Grzegorz Kocjan stopped transcription
Transcript
25 August 2023, 00:08pm

Grzegorz Kocjan   0:04
Yay.
We're back on track again.
Hello after the break.
I have.
Hope.
That we will not fall asleep after dinner.
Ohh I need to share screen for Molly again.
Because he will not mention that I'm not sharing the screen because he's so polite.
Ohh.
And my did you had any problems with the task 9?
We're preparing fixtures.

Mohamad Ali Nasser   0:43
Ohh.
Yeah, I didn't have any problems. I didn't. I wasn't able to do it.
I saw it after you what you did, but I didn't understand the.
The.
The test that you do so.
I have a function called single sqldb like the one you.
Explained and all the test test run normal, so I'm not sure if there is anything else I need to to do.

Grzegorz Kocjan   1:19
So you don't understand this test test. Make sure that we can update the TB.

Mohamad Ali Nasser   1:27
Ohh I see. OK, so if you run this.
You should get them something empty because.
The bio fixture is emptying the data, the DB every time.

Grzegorz Kocjan   1:40
Yeah. So for example, imagine that.
We are removing the dose DB clean.
At.
We will run all the tests and as you can see the the last Test is failing. It is returning to 100 because this is. Carol was created for the previous test. If we run only this one test it will pass because.
And then you interpreter Python was started and already test was running. So this single SQ picture was not cold at all. But if we running all the tests then this one is failing. If we have this bug that we are not cleaning state between the test.

Mohamad Ali Nasser   2:12
OK.
OK.
OK, then it's clear this case, OK.

Grzegorz Kocjan   2:28
It's.
Ohh I would not say that it is like usual way of of working.
We've TD it. It's not like you are creating affect DB for each picture and and etcetera, but you may.
And.
The.
Biggest driver for doing so like why we are doing this here is because it's easier and and that's the.
Driver for me to finding different solutions of working on code work working traditionally. So OK I have to.
Calculate some data is give me a second sorry.
I need to answer the phone.
Sorry for that, I I had a call. I was trying to call Lady in the break and with for for the how how construction but.
She just she just called back now.
So.
Going back to topic that the driver is to make life easier and simpler and working traditionally when.
To actually write some code.
Like.
Having something in database, reading some parked files requires you to prepare a lot of things, so to prepare database or prepare packet fire or or to prepare connection for to external service and somehow to authenticate some. And especially if you're running code locally, you need to somehow to connect to that service that.
Is sometimes really complicated and time consuming, and you are spending a lot of time to set up something.
And that's to start working.
And for days you may not give any value.
And the actual value to to to the customers or to your friends, etcetera. Other teams and sometimes it is much easier to do some hacks like OK, my database is a dictionary or to do some kind of refactoring, kisses for for example I was.
For like few days I was doing 3 factoring that.
Then after the refactoring, cars are able to implement feature in like 20 minutes.
And if I would not do the refactoring for two days, I would spend like 2 weeks implementing this feature and having huge branch with a lot of changes and and and possibility of of breaking something. Because if the bigger changes that the the the higher the risk of downtime and failures.
So.
Reversing the process like here, I'm not focusing on creating database. Database is dictionary. I'm focusing on creating functionalities. What it gives for events planner. It gives us a possibility for front end.
Front and our front end team Marek to be able to work on a Pi that was like a little bit fake, but we can we could have progress and when he did your he his changes he he was able to show that to Anya and we could talk about how even standard is working in this its initial state and what we need to add more and even I was adding not nice fossils.
The functions to to eat to, to, to calculate calendar, etcetera. And there was no database at all, even if it was required from like technical point of view and for fulfilling so.
Hmm.
It it is not like the.
And that.
Maybe the thing that war is worth doing, but it is worth figuring out if there is a simple way of of working and of avoiding that. Usually it is somehow connected with code architecture so.
Here, right now I so soon I will show you a trick that will help us actually get rid of the data that the database very quickly.
But before that we will add some features. We will add some more features because we hit like we implemented everything that we have right now in acceptance criteria. So we have our acceptance criteria harsh too. So user can set squ product name, then user can unset SKU, product assignment. So.
We need to have product.
Nine, somehow somehow connected to to ask U. Then we need to set the onset shift so.
This is task there.
Let's go for it. Write a list of test.
Failing at those acceptance criteria, at least three, so we need to figure out three, accept 3 acceptance tests, but actually it's not. Those are not unit. Those are acceptance. As for those criteria and for simplification, assume that from now as Q will have bad field product name so.
Hmm we can.
Uh. Work with those assignments in anyways. The simplest way would be to have product name filled in the SU.
Let's keep let's keep it simple as as as this. So let's have product name in scale. Use put SUSKID for those operations. Remember about edge cases.
So.
Let's.
Assume that we will have this SUID product.
Assignment assignment by by by this method.
Think about educations. What can go wrong in this in this in this method and work to fulfil those new acceptance criteria for product assignment. Flow stays the same. So feed the test from the list right? It check if it is failing and make it green.
So.
Uh.
Don't. Don't try to rush writing the tests.
Hmm.
OK, maybe maybe some expansion.
I think that I I missed that one previously. Why to write this list of tests?
Because.
We don't want any distractions while working on some parts and.
It is proven that it is most efficient to work in batches. So for example we kind of work like a factory right now. It's not like it is not efficient to when one guy is moving from one spot in the factory to another one to finish. For example a car.
Keys on single spot for the whole day. Doing one thing repeatedly because it is the fastest way to do and here we want to spend some time discovering what worth testing. Figure out what would be the good test names and not maybe not good test name but good cases to test what acceptance criteria I want to fulfil. So we have those two acceptance criteria and they are obvious tests that we can add but of course.
Real life shows that OK when we have acceptance criteria in JIRA, usually we will have like much more actual acceptance test than than than there is in our task description.
So and and thinking that thinking only about OK what I want to test what are the test educates, etcetera is usually the most efficient. And then moving just implementation you will not need to be constantly getting back to that date when OK I have written test what to do next and then you need to go like to another mindset to figure out. OK analysing.
What in the what are the acceptance criteria? What are the edge cases then picking the next text test and then writing the test? And if you do that for a single test only like figure out what will be the next test after writing this test and this accept proffering that proofing like test you will need again go to the analysis state and and like deep changing your heart like OK I'm right now I'm analysis checking what acceptance criteria I need to meet.
And then I'm putting a heart of of developer.
You are keeping one hot As for as long as possible, so so you are trying to figure out those acceptance test for.
That you need to fulfil and then writing them, just picking one by one will be much faster. So it is a performed like you time optimization to do that in the batch.
Um, because this, this those kind of works requires different.
Uh.
Different time of different types of focus.
So.
Please first write those list of tests and start writing.
1 by 1.
And.
Additional note, write those new testing are separate file.
It is a good idea like I like. I was mentioning having all that the last refactoring that I was doing, I renamed the test fixed to actually test get.
Asking you now will have get update like update, stay or something that's updated skill or something like that.
And does the group them in a bit in that context right? So so it will be easier to find them in the future. But right now it will also easier to.
Hmm.
Like to giving them the name cause you will not have to repeat the full name each time.
And there is a hint here because right now.
In our tests we have this single SKU in database picture. In this file, right?
If you want fixtures to be used in many Python files, you can use contest file, so you can.
Start now.
From small refactoring, so moving the pictures to to contest and.
Because we want our test client to be used in multiple files, it would be also good to have it also as a fixture. So as you can see here, I'm not having a test client like as a global variable, it is a fixture and.
Here I'm using this picture to actually call the.
Called the API, I'm not using the global variable, I just remove it.
This is a good idea to keep everything as a pictures in in by test because it will be cleaned between the test runs.
And because each fixture fixture content created for each test separately, so we are removing a global state. So possible?
Potentially, if we would have test plant as a global variable like like it was previously, we could end up with the same situation that we had with database that it was leaking, that the state between the tests so.
So we want to avoid that situations and and have this.
Separated. So that's it for you guys, let me know.
When you finish that batch of work and I will then show you.
How I was working.
And if you have any question in the mean time asked us as whenever you want.
Molly, everything on your side. OK. You have any problems?

Mohamad Ali Nasser   25:38
But first, we're good.
Probably falling in the same trap, but it's OK.

Grzegorz Kocjan   25:49
Doing too much.

Mohamad Ali Nasser   25:51
Yeah, maybe, maybe, but maybe it's not too much. I don't know what I will see.

Grzegorz Kocjan   25:57
Hmm.

Mohamad Ali Nasser   25:58
But if everybody's done, we can go ahead.

Grzegorz Kocjan   26:02
No, no.
But I.
That the.
But the strikes?
Also understandable and instead of corporal.
Okay.
Does that bother?
Because you have a QID and product name.
You didn't send any kind of production and input.
And.
I'm not sure.
If.
Yeah. Because you see the item is an object.
Type not like a simple. So because input you are usually sending Jason right.
So two things that it's wrong time and you need to pass something right.
So Maria, also for you, you we can assume.
And that you like. Usually we are doing something like.
Port.
And.
SU.
I'm I'm trying to write OK, I I will maybe write it in in.
In here.
My day will hide all my test names because they are so amazing.
So usually we are doing something like.
Both ask you.
That's OK, UYD.
And inside the body.
Hmm.
Not doing something like that.
So we can assume that our API will look like this that we are calling this put and the body.
Ohh that port is adjacent.
And this version has product name as the name or.
None. If you want to understand it.
This this is like a simplified version of of of a Pi. We could design it totally different, so we could for example have like.
Assign and here.

Mohamad Ali Nasser   28:48
Yeah.

Grzegorz Kocjan   28:53
Product name in the URL and we could have.
On the sign.
And have totally 2 two different methods for that, but having it that way is simpler at the beginning.

Mohamad Ali Nasser   29:10
Hmm.

Grzegorz Kocjan   29:11
Another word it it and what is the best one usually depends on how.
Products are structured and contract between teams etcetera. Ali.

Mohamad Ali Nasser   29:23
Yeah, question regarding fast API. It doesn't have body parameter right? It has Jason or data or something like that or.
Tell us about the parameter I forgot.

Grzegorz Kocjan   29:34
Yours or so. So.

Mohamad Ali Nasser   29:38
Remember, it was Jason, there's data.

Grzegorz Kocjan   29:52
So usually.
We have something like this.
So this is the example from.
Uh documentation, right? Updates.
We have item and item ID because put usually update something existing so we have ID here response model is optional.
And we have method I function with item ID, so the item ID from the path and item is from it is the body.
So type item.
Here is a pedantic model.
So in first API you want to have an single.
Argument in the function that is pedantic model and it is assumed to be a.
The body.

Mohamad Ali Nasser   30:55
OK.
So so when I call the put, I don't necessarily need to send the Jason.
Was a product name, right?
So just use item.

Grzegorz Kocjan   31:09
So.
So item ID is the SKID in our case.

Mohamad Ali Nasser   31:16
Yeah.

Grzegorz Kocjan   31:17
And item would be a a base model with product name only. So with only one field.

Mohamad Ali Nasser   31:26
OK.

Grzegorz Kocjan   31:27
Then it would be it. Then it would behave like.
OK, I'm expecting single.
Hmm.
Single filled in the Jason body.

Mohamad Ali Nasser   31:42
OK, I I will not do the base model item class now just try to send it as normal string and see if.

Grzegorz Kocjan   31:50
Generally first API.

Mohamad Ali Nasser   31:51
Or Jason or Jason with one string. Yeah.

Grzegorz Kocjan   31:56
There is another way.
I.
No, it is the same.
Ohh.
Ohh.
It's not here. There is another way of you ohh using request directly.
So you can have request argument with type request from Fast API.
And in the request you have.
I wonder where is the connotation? What is ohh.
And in the request.
You have method. Your L Heather's query parameters.
Cookies and the body.
So we can call the request body or request Jason.
Without calling without creating a A pedantic model.
So you can combine.
Without model you can.
Other arguments request type request and here to request Jason.

Mohamad Ali Nasser   33:22
OK.

Grzegorz Kocjan   33:40
The disadvantage of that approach is that it will not generate you the documentation.
Because if you using.
That pedantic model.
Then ask you here seeing that.
Example.
From the condition it creates you the documentation and inside documentation you see that the item schema is named Pride Description Tax. If we call which will get the body directly, there will be nothing here for what is the body? How would they looks like and if you see here it is even example value that you have all the fields, Jackson and etcetera and if you click and try out in in this.

Mohamad Ali Nasser   34:20
Hmm.

Grzegorz Kocjan   34:34
It is called Swagger Y.
This this web application.
You even have.
That all the fields passed for you.
In the in text field and you will just only need to fill their values without creating the whole prison.
But generally you can start simpler and and having provement later on.

Mohamad Ali Nasser   35:03
OK.

Grzegorz Kocjan   56:07
You can have with you going.
Ohh baby, I understand thinking about the traces.
But you did like the basic product assignment and assignment, yes.
OK.
I.
So my your hands are busy, but can you see the screen?
So great. OK. So I will just show you.
Again, what I was doing during that that a task and like previously it was not a single commit.
It was actually a bunch of commits, so as you can see there is the list is even bigger than previously. And of course it is because right now we have a bigger batch of work.
So.
At first I created this fixture, not a that was in the example, so we move it.
I to to a separate file contest file. Those are places that are loaded automatically by by test and we can play place confidence files at any place so it doesn't need to be on the top test folder. It can be somewhere in in some of the subfolder. What is important if our contest file is in some sub folder.
Then it the content of this contest file will be only visible for fires in that subfolder, so we can for example.
Have some some fixtures only usable for part of our the testing staircases and and we can place it not on the top confidence file, so it will not grow forever. We can just organise our fixtures with it and I instead of using this global lighters moved the client's client to to every single.
There's nothing else changes, so that was the one commit and it was it was refactoring because all the tests are were passing after this date.
Again, then I that I ordered are put failing tests, so I created a list.
So it is the least like.
Like we did for the first example test existing. So yeah, the the first Test was test not existing. Idea returns for all for status and massage in details. So it is exactly the same test that we wrote at the very beginning but instead get we have put a test existing ID and product name in body returns to 100 status. So at this point this test will check.
Hopefully if we can send proper body inside a for for.
For blah for our IPI test existing unassigned that I D and product name in body returns assignment as scale so.
I'm referring that.
I have unassigned schedule. I'm passing product name in the body so I will receive in the response assigned a scale test as the existing assignment ID and product name in the body returns updated assigned as scale. So this test if I have some assignment and I'm putting new assignment it will be updated. So I'm verifying if.
The actual already assigned this to be updated.
That's.
The.
Sometimes I I found that such cases are skipped.
And I found some cases I was working on the projects where actually that was, uh, a big bug. That. OK, I will say able to set some field.
But I was unable to update it or to change the value.
Though if we are designing like if we are planning, it is important to remember if we are setting something around. Unsetting it is also worth checking if changing the value is also.
Possible.
And test existing assignment ID and the non product name in body and assigns product from a scale. So I'm checking if sending product name as none will unassign and test existing unassigned ID and nonproduct naming body returns and assigned SKU.
So those are two separate tests I'm checking if it unassigned and if it returns unassigned.
A scale but.
It is only the least I will just figuring out what can be tested.
Important notice in practise usually this test it is very difficult to write this test. This list complete at the beginning and many times when I have such a list for for some features. Usually it is the fact that I'm starting from like. Here are some basic and while writing some test and can can I'm coming up with a new ideas and I'm just putting the note on the list that.
OK, there is some edge cases worth testing and I'm getting back to finish what I was doing.
So it is important to to write some notes about edge cases that you are you have in the mean time and.
Not to switching the context to actually solving that problem right now, not to finish in what we are we're doing.
It was in the book about.
I think refactoring, but maybe it was also understood that.
And.
We have limited stock on our head.
So if we are putting too many things on To Do List, or if we have too many things to remember that we OK, we are doing this thing. But to do that we need to modify something. But to modify this this another thing we need to change something else in the totally different file or totally different service.
And if we have a lot of work in progress files open, it is really difficult to handle such case.
So instead we can try to write our code.
In backward compatible mode or like this, just to put what we need to do on on on.
Some kind of a note at least or or here, just not to lose the focus on on what we are doing and if too many things are started at the same time that the more difficult it is to finish anything.
So.
Hmm, that approach is not. That approach. Is not only a good for creating test cases or writing tests, but also to to writing more complex solution more complex.
Solving more complex code.
Problems.
So yeah, the first Test.
And I make it past.
So you already know that trick I created the put method, updated QID and it was rising.
Always rising HTTP exception.
Nothing more and here.
I did small refactoring because this single SQ in database was originally in this get a scale by ID file and now I will need that.
Fixture in many files, so I move it to the contest and as you can see.
Separate commit without nothing.
More on it.
Only then.
Hmm, I added another test, so at the existing ID and product name in body returns to 100 status.
And that test was failed. So I was able to.
Then they put back. We always have this.
Raise exception. So how to pass it the same way as with the get?
I'm getting asked you from the DB and not returning anything like previously we don't want to return SK you because the test doesn't require that.
To pass.
Hmm.
So.
Here.
A red green assigning product I decided to.
Hmm, I speed up. I took a little bit with the commit, so we don't have separated.
OK.
Here, not here. Here so.
It is the fixture.
So I'm not importing kid, I'm using the picture.
Way off.
Working with the code so.
Envious code.
In the main test folder I have contest file. Here I have the picture and picture are visible in all test.
In all files in the same folder and sub folders.
And if we?
Does he use that pictures? We don't need to import anything we I only import the test client for the typing type annotation so I can get some hints on what methods there are and but that's only for for.
Easier development. It's not necessary. The only thing is that this is the client, so it is the fixture name and.
It is the biggest way of of importing things you you don't use direct imports and.
For me, if I'm importing something from the test inside the test, that's a code smell for me. So if I'm like OK here, I'm not importing anything from a fast API TD because I can just call the.
Called the API, but here I'm importing the app and DB.
And that's OK to import your application code inside test, because you are testing application. But any UTS that I had only for tests I'm not creating any module that can be imported in test. I'm just using fixtures for that. So whenever I need to have some utility for for test I wrap it around fixtures.
Okay.
I.
The the the reason for that is for that, for example, while the case that we had with database. So right now we can have we can ensure that the database is.
He's cleared, cleaned between the.
Uh twin, the test run, etcetera.
And no, there is no global state and.
Like.
I would say that I saw many bad design patterns.
Created by importing some utils in tests.
That's why I'm avoiding that. It's not like a very strict rule that that you should follow, but I like to follow up to have some constraint on me not to fall into some some traps. So everything for me in test in utilise for tests should be fixtures and because they are super powerful and we can do like everything with them.
OK, so we make this test passing. We have red green so ohh how to hide the those changes?
So here I wrote test that existing unassigned ID and product naming body returns assigned.
Ask U and here I needed to.
Ohh there there was a typo in this line I like I like I said previously, I like to. I love to make a lot of typos so this test was not checking if I'm actually sending correct Jason.
And and it was not existing in the code. But when I understand it appears that this test start failing.
It is a little bit.
A little smell that might test failed because of some kind of different reason.
Because it's not it. It fails because it was not failing because I was sending incorrect data and. But let's say that.
It might be ignored.
It it will happen but not your test will fail for a wrong reason.
Uh, part of the the the everything was OK but it but it was failing.
But generally we should try to avoid such situation and check if we could avoid them in the future.
Future. So here we have a one of the approaches for such situations, so I'm checking if assigned ID and product name in the body returns assignment scale.
So here I'm updating the SU. I'm getting the response of product name is assigned to that the school is assigned to product and then I'm also calling get.
Who fed the SU again and compare if actually it was thought in let's say database.
Just relying on the API response usually is not enough for the test and.
I would say that it's more of of our experience but experience.
And that sometimes we modify something especially for for like modifications put and post we modify something.
We return modified value but it was not stating the DB and it was happening many times. So our tests somehow needs to check if the update methods actually stored the value in the database correctly.
And there are two ways of doing that, like general two ways. First way.
Is here when we are actually calling calling other HP. I already well tested.
So we already tested get method so we are getting that SU and checking if it is returning the product name.
And that way we are sure that after updating the the in the database it is still updated.
A product that it is not like forgotten.
Add underway would be to check the value in that database. So without calling the API.
I bought does, but by calling the database or going through some repository class etcetera.
Both are correct.
And and.
I prefer to check if something is stored in the DB.
Rather than calling the API but.
And.
It's rather.
Preference.
Caused by.
Fact that we are not always have the get method for the I for the given item so.
Of.
And that was one case when when we were working on something with Ciprian that.
The two that you created an API get method just for test because it was not existing.
And and it was happening also for me many times that that we didn't have method to get some object.
We have some food we have, we had post but we had like a global set or returning the whole list of items only. And to test if that item was updated to requires to like OK getting the whole list of objects or all the entities in the database and then searching the one that we were updating and then checking if it has correct value. It was complicated therefore I was usually preferring to to.
To get it directly from the database or from the repository pattern.
But if it is possible to to test it in that way, so by calling the API it is a little bit more safer.
And this this way of testing is more resistant to changes. So for example if we are if we would like here call the SQL query directly to check OK Get me products get me schedule with that I D and check the product so we'll call here as well query directly every single time that for example will change that something in database there is a risk that we will need to change many tests.
So it is not very good to have assertions that are very low level. So like running a SQL queries.
It would be better to have uh repository, so some wrapper around database access we we will soon write one for our database here.
But still it is possible that our repository may change and then we'll need to modify extra test the the lowest risk on changes are on a Pi level because they are usually changing the less often than the internal stuff of the of the test.
Verify if assign update works.
So uh.
I added a single assignment schedule in the DB, so I have two fixtures, 11 at unassigned scale. Here I have assigned at the schedule and I'm. I'm using this assignment schedule and I'm updating it with the new product and checking if after updating the new product is there and and and that's it.
Another verify if removing assignment works.
Ohh very similar. OK I have single assignment schedule. I'm sending product name none and I'm checking if it equals non and here there is a a comment that we can treat that in a two ways and it depends on the design of API. So we can send always product name. So if it is assigned or unassigned the key always.
This, but in some APIs it may happen that if for example product if escape is not assigned will not include that field in their response. But that's a more business decision. So it depends if you prefer to keep the key, keep the key in their response even if it is empty then it will look like this. But it may happen that it will look differently and then.
Checking if it actually turns on assignment.
I D.
Yeah.
Is there?
The street is that as the product name.
Okay.
Uh.
Empty strings are usually something that needs to be discussed on the contract level, so.
It depends on the project. If empty string is.
Unassigned value or assigned value because it may be assigned value without name empty string because empty string is something different than none.
And it it it is very difficult to answer what what is the best and because in in that the simplest the simple answer is that I would prefer not instead of empty string but the correct answer is that in many cases empty string is better than none and because of performance issue with because of type consistency you don't have optional.
And you always have this.
And.
Hmm.
The same type, so it is easier to handle that and for example.
And there is a really good design pattern called.
Empty object. I think empty state that you don't have anything like none you always.
Return some object and some object are treated as a known or unassigned. So here for example you could have empty string or string and assigned.
Returned from the API, but it totally depends on like the contracts between teams, between services and and how what is the general approach. So this is like a broad topic even for or for very long discussion.
And how how we could?
At work with that.
There was a little bit in inconsistency in data because in one of the concepts the old one I didn't modify, I didn't product name and right now we have had every response. We have the product name because.
Like I said, we can treat it differently. I I made decision previously that we should return and none if it is unset, but here it was missing so so that was actually a bug fix I would say not refactoring.
And that's it.
Now the two most difficult task.
Maybe 1 the the the one simpler, so I was referring.
And now you might probably be less familiar with that, and I'm sharing that ohh usually with with the piano when we are working with events planner that doesn't matter and there is very good.
And this pattern repository pattern that we.
We are trying to hide all.
Access to database or to some external service?
Through some object or some module.
And have all the raw connection, all the database queries, all the request handling only inside single file or single module or someone single place. And whenever we want to connect to some OK some fetch some data from database or or to call some API in other places. In business logic we are not doing that directly but through the repository.
So.
The task 11 is for us to be prepared to prepare ourselves for.
Changing the base we want to get rid of that.
Did this dictionary the dictionary, but we want to make it as easy as possible as as possible. So to do that we start from refactoring.
And at the end of this task you should access the DB directional Dire dictionary. Sorry only inside SQL report. All other places should use the SQL report methods. So how this best implementation looks like in a Python is that we have SKU so we have base model so it is OK ID, name, product name and we have SKU repository that for stupid.
Returns SK, U or none.
And we have safe method that forgiven SK U returns updated SU.
Like the same as two and it should be stored.
Inside this dictionary.
And it would be good to run our test as frequently as possible, and like I was mentioning many times, perfect situation is that in your test will never fail even in the middle of refactoring that these are a little bit tricky but.
Now.
We are at this day.
Where in the name we have our DB and we are constantly accessing that DB.
From the global variable, right? So we want to wrap it up, maybe create a separate file and here only direct it through this class called SQL repo.
Hmm, maybe I will give you like a 10 minutes.
For that, do as many thing as as many things as you can.
And after 10 minutes I will show you again I my solution and will move forward just to finish out at 4:00.
And if you have any questions, please ask as soon as possible.

Mohamad Ali Nasser   1:46:07
Jewish are you? Are you talking or?
With everyone? Or are you human?

Grzegorz Kocjan   1:46:12
Oh my God I I was muted for the whole time.

Mohamad Ali Nasser   1:46:15
I was wondering what you were doing then. I was thinking maybe you were talking with everyone else.

Grzegorz Kocjan   1:46:18
Huh.
Ohh I was admitted for like a 10 minutes talking.

Mohamad Ali Nasser   1:46:25
I thought it was. It took a lot of time.

Grzegorz Kocjan   1:46:28
Ah.

Mohamad Ali Nasser   1:46:30
It's OK. It's OK. It's OK. I can continue where you are now. I'll look at the code. If there's something I don't understand, or I'll ask you.

Grzegorz Kocjan   1:46:30
Get.
Oh. Oh, OK. I I I will. Maybe finish that with you.
And next week.
At, but OK, generally we are moving from accessing database directly and using the SU repository pattern in every single place, even in test. So I'm creating even skull repository fixture for that and and using that to create this.
This is the existing fiction that we were using for the whole time single scaling database and single assign the schedule in database.
So we are using the repository from that and and.
With a that approach we can do something really powerful because.
Like we did in events planner, the same the same thing.
We want to have database with some initial state.
So uh, because we are testing something and we don't want to like create every time the the we don't want to recreate the state every single time when we restarting the application because it will complicate our our test etcetera.
Like the test with the for example, building the UI application.
So we want to start the first API, so I'm saying about actual starting the service we want to start with some initial states so having this.
Example SK you already there and here I'm passing that database to this dependency. So instead of creating database there I'm passing the.
I'm passing the dependency.
Hmmm here in.
To to the ask you report report and the difference is that instead of accessing database here from from the global state.
I'm creating a in it initializer and storing database reference inside repository. What it give us is that inside the tests.
We can create fixture called DD that returns empty dictionary.
And remember picture picture by default is created.
From scratch for every single test. So right now we are covering all the clearing of database because we are not referring to the database that is global state in our application.
But actually we are creating it here.
And here we have application. Instead of just calling the the client we can use dependency override.
Method to return a little bit different SKU.
That uh would be returned by the dependency in the past APISO.
When we run Fast API from the command.
Like running Kitten and having the working server, it will create the repository that is in the main.
But when we are running tests.
This that these SK repository will be created by by this Lambda function and here we have a fixture. So this fixture that has dependency on database that is empty database retreated for each single test so.
This thing maybe not simple trick, but this trick allows us to create empty database every single time so so right now we are not.
Clearing some global state database but we are creating new database for each test.
The benefit of that is that in the old picture it was impossible to run multiple tests on the same time, so we have this possibility to run multiple.
Black test in threads.
It was not possible because still we have global database that was shared for the whole test run.
And we need to run test in the sequence. Right now each test has a has its own database, so we can run them in parallel and totally speed up test execution. Of course here it takes like seconds like maybe even half of the second. But still it is more clean solution than it was previously.
And.
How does it look?
In this.
Right now, so we have up fast API. We have our database and we have our dependency that is referring to this.
Rat refuelled database.
And this is our working API. When we start the.
A.
The application but in context we have this separate database. Separately we are creating a stable positively and we have this dependency override and thanks to that we have totally different databases that are used for tests and for production code.
So.
At the end the the last task for us is to use actual database and we will use SQL Alchemy and some.
The sequel, like Database that is kinda read me there is all description how to configure.
Everything.
But I will show you the.
That whole solution there so.
Hmm, two configure database in the main file.
I just created a variable that will have point to the database file. It is database.
BT dot S collide.
And you are creating engine.
So this will initialise the engine for like production database and we have get session. It is similar to this get DB from.
From Fast API documentation, but this time it is with the context manager and the yield session. It is similar like try except but the context and other is I I think nicer because if there will be exception here it will be rolled back and the session will be returned the connection automatically. So the cleaner solution would ease to to use with statement and now we have SK repository that instead of.
Processing this hour, our fake database. Right now it is accessing the session.
So it is getting this this session dependency. In past API we can change dependency so we have dependency but requires all other dependency in this other dependency can require other dependency and it all works nicely. So here we have got SQL that is changed but as you can see in mine nothing else changes so all the endpoints are not touched at all.
So everything stays the same. Only thing that we are changing, we are changing this one dependency that was previously created.
From for for for the dictionary. Now it is created for a base session.
And we are creating repository OK. Here is the most of the work. So we are creating the sequel Alchemy Table in the database. It looks similar like this. SU from pedantic but it is actually sequel alchemy table in newer format. So sorry it is alchemy 2.0.
And we have all the mapping of the object to table down here.
For the pedantic, we need to add configuration on mode, so it allows us to create pedantic.
Distance from.
Model and in our skl U report instead of getting database as a dictionary we actually getting database connection through the session and we and and instead of accessing database like a dictionary but actually doing SQL execution so we are here we have actual database query and we are creating SU from ARM.
And that's it. We have also the safe method it is.
Similarly, we are using sequel Alchemy Mechanism to update database. So we have BSU and we are signing name and and product name and well saving that to database and this method is a little trick that we start using.
Have from some time it is safe that works as insert or update.
So. So that's a little trick.
And of course, we need to change something test. But in test we are mostly adding things, so engine we are creating another engine and here it means that we are creating engine in the temp.
In the memory, so we can create a sequel alchemy database in memory and it will be destroyed after the test run. So we have database that is created for all session.
And we are creating session that begins and rollback and closes for each class because this picture is run for each test.
And here.
We have client so instead of dependency override get we have overriding get session instead of overriding get SK repository because right now get repository get escape report requires session and we can say that OK for the production code session is for our file that we stored in the repository and for test this is only the temporary database created in.
In a memory and that's it.
So having the repository pattern having this effect database use it from the beginning, we are able to move from totally not production ready salaries.
To production ready one without any changes in the code.
So all of those tiny steps that we were doing.
Till now I where a little bit more complicated we are doing. We were doing a little bit more because like those database some refactorings.
And.
You see that we would we were able to switch to production ready sabase to fully working sequel, alchemic question etcetera without any changes to to the API, to the tests etcetera. And that's the good definition of tests. If our tests are written in such a way that we can do such magical things.
Then it's good.
It is bad if for each simple change or each architect architectural decision change. If we need to change many tests, a lot of code out of existing code and then this is. This is something from this is something worth fixing 1st and then moving to some architecture changes or for example changing the the the databases.
But right now we have a test that even if we were changing something.
Crucial for our products, so we were changing database. We were not forced to modify all the tests. We weren't modifying 0 test and.
That's.
The point of the TD that we need to write a good code to be able to to work in TD. It's not. It is really difficult to to work with facility code.
And and working with TD because I took to to create a good test we need to recreate all the dependencies for that test and if our code is dependent on everything in every single place, it is impossible to test that.
It it's it's that date. So we need to first of all learn how to separate our dependencies. How to revert dependency and this is the pattern that we did with the repository pattern that we have this repository and instead of accessing that repository directly we are passing it through the dependency.
So dependency mechanism in in first API is actually a dependency inversion. So our main code is not dependent.
From the database.
We can implement our logic without worrying about about database connection etcetera. So that's how I really powerful and I I would say that core of the test it is even more important to reverse the tendencies for the TD done, actually knowing how to write a good test because.
We will not need many complex testing treats. We don't need to know many complex stepping trick if we can write code in i.e. Easy, well organised way.
Ohh.
OK, so I think that it was it from my side. If you have any question I will be lucky to happy to, to.
To answer that, and like I mentioned that the repository is there.
Hmm.
The repository with all the solutions.
Is that one?
Ohh so she could you can go through all the steps that that I I I took.
For, for, for this workshop. And if you have any thoughts or you just wake up next morning and.
Uh, you will find that OK. I I have some question. Please ask it because like I was saying I want to create a.
Course from that and and that will be a really helpful if you will ask any additional questions even like.
It will come up something later.
Hmm.
Okay so.
We are good to go.

Mohamad Ali Nasser   2:02:54
And.
I don't have any questions right now, maybe later, but thank you for the explanation. I did learn.
Ohh.
Um.
I did learn a lot of things I do testing sometimes not. Sometimes I try to do testing whenever I can, but not in such structured way. So thank you.

Grzegorz Kocjan   2:03:24
Thank you, Molly.
So see you Monday.

Mohamad Ali Nasser   2:03:31
To bye bye.

Grzegorz Kocjan   2:03:33
Bye.

Grzegorz Kocjan left the meeting

Grzegorz Kocjan stopped transcription
