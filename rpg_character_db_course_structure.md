# RPG Character Database - TDD Course Structure

## Course Overview
This course teaches Test-Driven Development (TDD) principles and practices through building a RESTful API for an RPG Character Database using FastAPI and Python. Students will learn how to apply the RED-GREEN-REFACTOR cycle, create effective tests, and incrementally build a production-ready application.

## Module Types (Based on MVP Compass)
- **EXPLAINED** - Theory sessions explaining TDD concepts
- **ACTION** - Hands-on coding tasks following TDD methodology
- **SETUP** - Environment setup and configuration steps
- **DEMO** - Instructor demonstrations of TDD techniques

## Course Modules and Lessons

### Module 1: Introduction to TDD and Project Setup

#### Lesson 1: TDD Fundamentals (EXPLAINED)
- What is Test-Driven Development?
- The RED-GREEN-REFACTOR cycle
- Benefits of TDD in software development
- TDD best practices

#### Lesson 2: Project Setup (SETUP)
- Setting up the development environment
- Installing required packages (FastAPI, pytest, SQLAlchemy)
- Project structure and organization
- Creating the initial repository

#### Lesson 3: Writing Your First Test (ACTION)
- Introduction to pytest
- Creating the first failing test
- Understanding test assertions
- Running tests and interpreting results

### Module 2: Building Core Character Functionality

#### Lesson 4: Character Retrieval - Not Found (ACTION)
- Writing a test for non-existent character retrieval
- Implementing the GET /character/{character_id} endpoint
- Handling 404 responses
- Testing error messages

#### Lesson 5: Character Retrieval - Success (ACTION)
- Creating an in-memory database
- Writing tests for successful character retrieval
- Implementing character retrieval logic
- Validating response structure

#### Lesson 6: Character Creation (ACTION)
- Writing tests for character creation
- Implementing the POST /character endpoint
- Validating input data with Pydantic
- Testing successful creation responses

#### Lesson 7: Character Update (ACTION)
- Writing tests for character updates
- Implementing the PUT /character/{character_id} endpoint
- Handling validation and error cases
- Testing update functionality

### Module 3: Test Organization and Refactoring

#### Lesson 8: Test Fixtures and Organization (ACTION)
- Creating reusable test fixtures
- Organizing tests by feature
- Moving common test setup to conftest.py
- Improving test readability

#### Lesson 9: Refactoring API Code (ACTION)
- Separating API code into modules
- Refactoring without breaking tests
- Improving code organization
- Testing after refactoring

### Module 4: Character Attributes

#### Lesson 10: Character Attributes Management (ACTION)
- Writing tests for attribute assignment
- Implementing attribute endpoints
- Testing attribute validation rules
- Handling attribute updates and removals

### Module 5: Repository Pattern

#### Lesson 11: Introducing the Repository Pattern (EXPLAINED)
- Understanding the repository pattern
- Benefits for testing and code organization
- Dependency inversion principle
- Planning the refactoring approach

#### Lesson 12: Implementing Character Repository (ACTION)
- Creating the CharacterRepository class
- Refactoring existing code to use the repository
- Updating tests to work with the repository
- Testing repository methods

#### Lesson 13: Dependency Injection with FastAPI (ACTION)
- Using FastAPI's dependency injection system
- Refactoring endpoints to use dependencies
- Testing with dependency overrides
- Improving testability

### Module 6: Skills and Equipment

#### Lesson 14: Character Skills Management (ACTION)
- Writing tests for skill assignment
- Implementing skill endpoints
- Testing skill level requirements
- Handling skill updates and removals

#### Lesson 15: Character Equipment Management (ACTION)
- Writing tests for equipment assignment
- Implementing equipment endpoints
- Testing equipment class compatibility
- Handling equipment updates and removals

### Module 7: Database Integration

#### Lesson 16: SQLAlchemy Integration (EXPLAINED)
- Introduction to SQLAlchemy ORM
- Defining database models
- Relationship between entities
- Planning the database migration

#### Lesson 17: Migrating to SQLAlchemy (ACTION)
- Updating repository to use SQLAlchemy
- Creating database models
- Refactoring tests to use SQLAlchemy
- Testing database operations

#### Lesson 18: Testing with In-Memory SQLite (ACTION)
- Setting up in-memory SQLite for testing
- Creating test fixtures for database testing
- Ensuring test isolation
- Testing database transactions

### Module 8: Advanced Topics and Project Completion

#### Lesson 19: Error Handling and Validation (ACTION)
- Implementing comprehensive error handling
- Adding input validation
- Testing edge cases
- Improving API robustness

#### Lesson 20: API Documentation and Testing (ACTION)
- Documenting the API with FastAPI
- Creating integration tests
- Testing the complete API
- Final project review

## Task Progression (Based on Git Commit Flow)

Each lesson will follow this progression:

1. **Task Introduction**: Explain the task and acceptance criteria
2. **RED**: Write a failing test
3. **GREEN**: Implement the minimal code to make the test pass
4. **REFACTOR**: Improve the code while keeping tests passing
5. **Verification**: Ensure all tests pass and requirements are met

## Assessment Criteria

Students will be assessed on:

1. Adherence to TDD methodology
2. Test quality and coverage
3. Code organization and readability
4. Implementation of all required features
5. Handling of edge cases and errors
