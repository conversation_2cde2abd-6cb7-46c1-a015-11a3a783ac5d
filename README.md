# Love-Driven Testing

## Generate next draft section:

    Ok, now let’s write the actual course content for my course. Rather than doing it all at once though, let’s do it one module at a time, and more specifically, one lesson at a time. 

    Adopt the role of a professional Python Developer with focus on using TDD who's preparing online video course with live coding. Write a detailed lesson for lesson of the module of my program. Remember, here’s what it is:

    Module 3: EXPLAINED: FastAPI introduction
    Lesson 3: Writing a ridiculously simple GET endpoint

    Maintain a conversational yet instructive tone, incorporating real-world examples at every step that junior or mid developer can identify with. 

    Strategically structure the text with headings, sub-headings, bullet points, and numbered lists where appropriate, filling out the actual content for each element as you go. Split it into visual part (what to show on the secreen) and voice text that I will talk during video recording. Create timeline from content (what to show on screen, what to talk, next thing to show, what to talk). For theretical parts keep minimum amount of text visible and focus on voice explenation. If needed decscribe visuals. Format in in mardkown using marp (https://github.com/marp-team/marp) for presentation and Mermaid if you will need some charts. Use comments for slides to put there what I need to tell (voice transcript)

    Clean up slides and comments. I want to have on each slide visuals of what I will tell and in comment section I want exactly, word by word, what I gonna tell on this slide

    Conclude the lesson with key takeaways in bullet-point format, followed by a learning activity. 

    Make sure to write the lesson as if it were for an online course. Don't make it an outline for the lesson, I want you to write the actual lesson script that I can present in an online video.

    return result in a downloadable file in format {module_number}_{lesson_number_two_digits}_{topic}.md 

    marp file template:
    ---
    marp: true
    theme: default
    paginate: true
    backgroundColor: #0d1117
    color: #ffffff
    title: {lesson title}
    description: Module {module_number} / Lesson {lesson_number} — {topic}
    ---