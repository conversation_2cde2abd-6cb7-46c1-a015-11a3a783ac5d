# RPG Character Database Project Requirements

## Project Overview
This project will implement a RESTful API for managing RPG game characters using Test-Driven Development (TDD) methodology. The API will allow users to create, retrieve, update, and delete character data, as well as manage character attributes, skills, and equipment.

## Technical Stack
- FastAPI for API development
- SQLAlchemy for database operations
- Pydantic for data validation
- pytest for testing
- SQLite for development database

## Core Entities

### Character
- `character_id`: Unique identifier (string, format: "CHAR:{id}")
- `name`: Character name (string)
- `class_type`: Character class (string, e.g., "Warrior", "Mage", "Rogue")
- `level`: Character level (integer)
- `race`: Character race (string, e.g., "Human", "Elf", "Dwarf")

### Attribute
- `attribute_id`: Unique identifier (string, format: "ATTR:{id}")
- `name`: Attribute name (string, e.g., "Strength", "Intelligence", "Dexterity")
- `description`: Attribute description (string)

### Skill
- `skill_id`: Unique identifier (string, format: "SKILL:{id}")
- `name`: Skill name (string)
- `description`: Skill description (string)
- `required_level`: Minimum level required to learn the skill (integer)

### Equipment
- `equipment_id`: Unique identifier (string, format: "EQUIP:{id}")
- `name`: Equipment name (string)
- `type`: Equipment type (string, e.g., "Weapon", "Armor", "Accessory")
- `rarity`: Equipment rarity (string, e.g., "Common", "Rare", "Legendary")

## Relationships
- Characters can have multiple attributes with specific values
- Characters can learn multiple skills based on their level
- Characters can equip multiple equipment items based on their class

## API Endpoints

### Character Endpoints
- `GET /character/{character_id}`: Get character by ID
- `GET /characters`: List all characters (with optional filtering)
- `POST /character`: Create a new character
- `PUT /character/{character_id}`: Update character information
- `DELETE /character/{character_id}`: Delete a character

### Character Attribute Endpoints
- `GET /character/{character_id}/attributes`: Get all attributes for a character
- `PUT /character/{character_id}/attribute/{attribute_id}`: Set attribute value for a character
- `DELETE /character/{character_id}/attribute/{attribute_id}`: Remove attribute from character

### Character Skill Endpoints
- `GET /character/{character_id}/skills`: Get all skills for a character
- `PUT /character/{character_id}/skill/{skill_id}`: Add skill to character
- `DELETE /character/{character_id}/skill/{skill_id}`: Remove skill from character

### Character Equipment Endpoints
- `GET /character/{character_id}/equipment`: Get all equipment for a character
- `PUT /character/{character_id}/equipment/{equipment_id}`: Equip item to character
- `DELETE /character/{character_id}/equipment/{equipment_id}`: Unequip item from character

## Acceptance Criteria

### Acceptance Criteria #1: Character Management
- User can create a new character with name, class, level, and race
- User can retrieve character information by ID
- User can update character information
- User can delete a character
- User can list all characters with optional filtering

### Acceptance Criteria #2: Character Attributes
- User can assign attribute values to a character
- User can update attribute values for a character
- User can remove attributes from a character
- System validates that attribute values are within acceptable ranges

### Acceptance Criteria #3: Character Skills
- User can add skills to a character if the character meets the level requirement
- User can remove skills from a character
- System prevents adding skills if character doesn't meet level requirements

### Acceptance Criteria #4: Character Equipment
- User can equip items to a character if compatible with character class
- User can unequip items from a character
- System prevents equipping incompatible items based on character class

## Development Progression

The project will follow a TDD approach with incremental development:

1. Start with a simple in-memory database (dictionary)
2. Implement basic character CRUD operations
3. Add attribute management
4. Add skill management
5. Add equipment management
6. Refactor to use repository pattern
7. Migrate to SQLAlchemy for database operations
8. Implement dependency injection
9. Add validation and error handling

## Testing Requirements

- All features must be developed using TDD (Red-Green-Refactor cycle)
- Tests should be organized by feature/endpoint
- Use pytest fixtures for test setup
- Implement both unit and integration tests
- Ensure test isolation and database cleanup between tests
- Achieve high test coverage (minimum 90%)
