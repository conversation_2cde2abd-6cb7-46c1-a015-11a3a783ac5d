# TDD Course: RPG Character Database

This repository contains all materials for the Test-Driven Development (TDD) course using a RPG Character Database project as the main example.

## Course Overview

This course teaches Test-Driven Development principles and practices through building a RESTful API for managing RPG character data. The course follows a step-by-step approach, starting with basic TDD concepts and progressing to advanced implementation techniques.

## Course Structure

The course is divided into 17 lessons, each focusing on specific aspects of TDD and API development:

1. TDD Fundamentals
2. Project Setup
3. First Test
4. Character Retrieval (Not Found)
5. Character Retrieval (Success)
6. Character Creation
7. Character Update
8. Test Fixtures
9. Refactoring API Code
10. Character Attributes
11. Repository Pattern
12. Implementing Repository
13. Dependency Injection
14. Character Skills
15. Database Integration
16. API Documentation
17. API Versioning and Deployment

## Repository Contents

- `lesson_transcripts/`: Detailed transcripts for each lesson
- `lesson1_tdd_fundamentals/` through `lesson17_api_versioning_deployment/`: Code for each lesson
- `rpg_character_db_requirements.md`: Project requirements
- `rpg_character_db_course_structure.md`: Detailed course structure

## Getting Started

1. Read the project requirements in `rpg_character_db_requirements.md`
2. Review the course structure in `rpg_character_db_course_structure.md`
3. Start with Lesson 1 and progress through each lesson sequentially
4. Each lesson builds on the previous one, so it's important to follow the order

## Prerequisites

- Python 3.10+
- FastAPI
- SQLAlchemy
- Pytest
- Docker (for deployment lessons)

## How to Use This Repository

Each lesson directory contains:
- `README.md`: Overview of the lesson
- Test files demonstrating the RED phase
- Implementation files demonstrating the GREEN phase
- Refactored code examples where appropriate

Follow the lesson transcripts for detailed explanations and step-by-step instructions.

## Development Workflow

The course follows the RED-GREEN-REFACTOR cycle of Test-Driven Development:

1. RED: Write a failing test
2. GREEN: Write the minimum code to make the test pass
3. REFACTOR: Improve the code while keeping tests passing

This cycle is repeated throughout the course, with each lesson building on the previous one.
