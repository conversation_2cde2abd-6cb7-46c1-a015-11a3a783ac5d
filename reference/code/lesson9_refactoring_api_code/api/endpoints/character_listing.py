"""
New endpoint for listing all characters.
This demonstrates adding new functionality while maintaining existing tests.
"""
from fastapi import APIRouter, status
from typing import List

from rpg_character_db.models.character import Character
from rpg_character_db.db import db

router = APIRouter(tags=["characters"])

@router.get("/characters", response_model=List[Character], status_code=status.HTTP_200_OK)
async def list_characters():
    """
    Get a list of all characters.
    
    Returns:
        A list of all characters in the database
    """
    # Return all characters as a list
    return list(db.values())
