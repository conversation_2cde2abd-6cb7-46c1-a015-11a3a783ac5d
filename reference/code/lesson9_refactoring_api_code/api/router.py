"""
Centralized router for all endpoints.
This file organizes all API endpoints into a single router.
"""
from fastapi import APIRouter

from rpg_character_db.api.endpoints import (
    character_retrieval,
    character_creation,
    character_update,
    character_listing
)

# Create a main router
api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(character_retrieval.router)
api_router.include_router(character_creation.router)
api_router.include_router(character_update.router)
api_router.include_router(character_listing.router)
