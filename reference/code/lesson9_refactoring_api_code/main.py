"""
Refactored main application file.
This demonstrates the REFACTOR phase of the TDD cycle.
"""
from fastapi import FastAPI

from rpg_character_db.api.router import api_router

app = FastAPI(title="RPG Character Database API")

@app.get("/")
async def root():
    """
    Get a welcome message.
    
    Returns:
        A welcome message
    """
    return {"message": "Welcome to the RPG Character Database API"}

# Include all API routes
app.include_router(api_router)
