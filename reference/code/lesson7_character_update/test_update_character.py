"""
Test for updating an existing character.
This demonstrates the RED phase of the TDD cycle.
"""
import pytest
from fastapi.testclient import TestClient

from rpg_character_db.main import app
from rpg_character_db.db import db

client = TestClient(app)

def test_update_character_returns_200():
    """Test that updating a character returns a 200 status code."""
    # Arrange
    character_id = "CHAR:TEST123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human"
    }
    
    update_data = {
        "name": "Updated Character",
        "class_type": "Paladin",
        "level": 6,
        "race": "Human"
    }
    
    # Act
    response = client.put(f"/character/{character_id}", json=update_data)
    
    # Assert
    assert response.status_code == 200

def test_update_character_returns_updated_character():
    """Test that updating a character returns the updated character data."""
    # Arrange
    character_id = "CHAR:TEST123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human"
    }
    
    update_data = {
        "name": "Updated Character",
        "class_type": "Paladin",
        "level": 6,
        "race": "Human"
    }
    
    # Act
    response = client.put(f"/character/{character_id}", json=update_data)
    
    # Assert
    character = response.json()
    assert character["character_id"] == character_id
    assert character["name"] == update_data["name"]
    assert character["class_type"] == update_data["class_type"]
    assert character["level"] == update_data["level"]
    assert character["race"] == update_data["race"]

def test_update_character_updates_database():
    """Test that updating a character updates it in the database."""
    # Arrange
    character_id = "CHAR:TEST123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human"
    }
    
    update_data = {
        "name": "Updated Character",
        "class_type": "Paladin",
        "level": 6,
        "race": "Human"
    }
    
    # Act
    response = client.put(f"/character/{character_id}", json=update_data)
    
    # Assert
    assert character_id in db
    assert db[character_id]["name"] == update_data["name"]
    assert db[character_id]["class_type"] == update_data["class_type"]
    assert db[character_id]["level"] == update_data["level"]
    assert db[character_id]["race"] == update_data["race"]

def test_update_non_existent_character_returns_404():
    """Test that updating a non-existent character returns a 404 status code."""
    # Arrange
    character_id = "CHAR:NON_EXISTENT"
    update_data = {
        "name": "Updated Character",
        "class_type": "Paladin",
        "level": 6,
        "race": "Human"
    }
    
    # Act
    response = client.put(f"/character/{character_id}", json=update_data)
    
    # Assert
    assert response.status_code == 404
    assert response.json() == {"detail": "Character not found"}

def test_update_character_with_invalid_data_returns_422():
    """Test that updating a character with invalid data returns a 422 status code."""
    # Arrange
    character_id = "CHAR:TEST123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human"
    }
    
    # Invalid level (less than 1)
    update_data = {
        "name": "Updated Character",
        "class_type": "Paladin",
        "level": 0,
        "race": "Human"
    }
    
    # Act
    response = client.put(f"/character/{character_id}", json=update_data)
    
    # Assert
    assert response.status_code == 422
    assert "detail" in response.json()

def test_update_character_with_invalid_id_format_returns_422():
    """Test that updating a character with an invalid ID format returns a 422 status code."""
    # Arrange
    invalid_character_id = "INVALID_FORMAT"
    update_data = {
        "name": "Updated Character",
        "class_type": "Paladin",
        "level": 6,
        "race": "Human"
    }
    
    # Act
    response = client.put(f"/character/{invalid_character_id}", json=update_data)
    
    # Assert
    assert response.status_code == 422
    assert "detail" in response.json()
