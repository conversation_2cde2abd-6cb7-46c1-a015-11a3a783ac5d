"""
Character model definition with update models.
This defines the structure of character data in our API.
"""
from pydantic import BaseModel, Field, validator
import uuid
from typing import Optional

class CharacterBase(BaseModel):
    """Base model for character data."""
    name: str = Field(..., description="Character name")
    class_type: str = Field(..., description="Character class (e.g., <PERSON>, Mage)")
    level: int = Field(..., description="Character level", ge=1)
    race: str = Field(..., description="Character race (e.g., Human, Elf)")

class CharacterCreate(CharacterBase):
    """Model for creating a new character."""
    pass

class CharacterUpdate(CharacterBase):
    """Model for updating a character."""
    pass

class CharacterPartialUpdate(BaseModel):
    """Model for partially updating a character."""
    name: Optional[str] = Field(None, description="Character name")
    class_type: Optional[str] = Field(None, description="Character class (e.g., <PERSON>, Mage)")
    level: Optional[int] = Field(None, description="Character level", ge=1)
    race: Optional[str] = Field(None, description="Character race (e.g., Human, Elf)")
    
    @validator('*', pre=True)
    def check_not_empty(cls, v):
        """Validate that at least one field is provided."""
        if v == "":
            return None
        return v
    
    class Config:
        validate_assignment = True

class Character(CharacterBase):
    """Model representing an RPG character."""
    character_id: str = Field(..., description="Unique identifier for the character")
    
    @classmethod
    def create(cls, character_create: CharacterCreate) -> "Character":
        """Create a new character from a CharacterCreate model."""
        return cls(
            character_id=f"CHAR:{uuid.uuid4().hex[:8]}",
            **character_create.dict()
        )
