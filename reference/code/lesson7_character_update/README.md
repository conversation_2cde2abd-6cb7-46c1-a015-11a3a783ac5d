# Lesson 7: Character Update

This directory contains materials for Lesson 7 of the TDD course, which focuses on implementing character update functionality.

## Contents

- `README.md`: This file
- `test_update_character.py`: Test for updating an existing character
- `models/character.py`: Character model definition with update models
- `api/endpoints/character_update.py`: Endpoint implementation for character update
- `db.py`: In-memory database with sample data

## Learning Objectives

By the end of this lesson, you should be able to:
- Write tests for PUT endpoints in FastAPI
- Implement request body validation for updates
- Create endpoints that modify existing data
- Handle error cases for updates
- Follow the RED-GREEN-REFACTOR cycle for update operations
