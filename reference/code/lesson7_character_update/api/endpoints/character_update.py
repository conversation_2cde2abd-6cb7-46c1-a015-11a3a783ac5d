"""
Endpoint implementation for character update.
This demonstrates the GREEN phase of the TDD cycle.
"""
from fastapi import APIRouter, HTTPException, Path

from rpg_character_db.models.character import Character, CharacterUpdate
from rpg_character_db.db import db
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter()

@router.put("/character/{character_id}", response_model=Character)
async def update_character(
    character_update: CharacterUpdate,
    character_id: str = Path(..., description="The ID of the character to update")
):
    """
    Update a character.
    
    Args:
        character_update: The updated character data
        character_id: The character ID
        
    Returns:
        The updated character
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Check if the character exists in the database
    if character_id not in db:
        raise HTTPException(status_code=404, detail="Character not found")
    
    # Update the character in the database
    updated_character = {
        "character_id": character_id,
        **character_update.dict()
    }
    db[character_id] = updated_character
    
    # Return the updated character
    return updated_character
