"""
Database connection and model definitions.
This defines the SQLAlchemy models and database connection.
"""
from sqlalchemy import Column, Integer, String, ForeignKey, create_engine, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker

# Create a base class for declarative models
Base = declarative_base()

class CharacterModel(Base):
    """SQLAlchemy model for characters."""
    __tablename__ = "characters"
    
    id = Column(Integer, primary_key=True, index=True)
    character_id = Column(String, unique=True, index=True)
    name = Column(String, index=True)
    class_type = Column(String)
    level = Column(Integer)
    race = Column(String)
    
    # Relationships
    skills = relationship("SkillModel", back_populates="character", cascade="all, delete-orphan")

class SkillModel(Base):
    """SQLAlchemy model for skills."""
    __tablename__ = "skills"
    
    id = Column(Integer, primary_key=True, index=True)
    skill_id = Column(String, unique=True, index=True)
    name = Column(String, index=True)
    level = Column(Integer)
    description = Column(Text)
    
    # Foreign keys
    character_id = Column(String, ForeignKey("characters.character_id"))
    
    # Relationships
    character = relationship("CharacterModel", back_populates="skills")

# Database URL
SQLALCHEMY_DATABASE_URL = "sqlite:///./rpg_character_db.db"

# Create engine
engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create tables
def create_tables():
    """Create database tables."""
    Base.metadata.create_all(bind=engine)
