# Lesson 15: Database Integration

This directory contains materials for Lesson 15 of the TDD course, which focuses on integrating a SQL database with SQLAlchemy.

## Contents

- `README.md`: This file
- `repositories/sql_character_repository.py`: SQLAlchemy implementation of character repository
- `models/database.py`: Database connection and model definitions
- `test_sql_repository.py`: Tests for SQL repository implementation
- `dependencies/database.py`: Database dependency injection

## Learning Objectives

By the end of this lesson, you should be able to:
- Implement SQLAlchemy models for your domain entities
- Create a database repository implementation
- Write tests for database operations
- Configure database connections in FastAPI
- Follow the RED-GREEN-REFACTOR cycle when implementing database integration
