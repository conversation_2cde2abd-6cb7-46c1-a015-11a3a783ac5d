"""
Database dependency injection.
This provides functions for getting database sessions.
"""
from typing import Generator

from fastapi import Depends
from sqlalchemy.orm import Session

from rpg_character_db.models.database import SessionLocal

def get_db() -> Generator[Session, None, None]:
    """
    Get a database session.
    
    Yields:
        A database session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
