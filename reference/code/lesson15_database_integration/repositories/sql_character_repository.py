"""
SQLAlchemy implementation of character repository.
This demonstrates the GREEN phase of the TDD cycle.
"""
from typing import List, Optional
from sqlalchemy.orm import Session

from rpg_character_db.repositories.interfaces.character_repository import CharacterRepository
from rpg_character_db.models.character import Character, CharacterCreate, CharacterUpdate
from rpg_character_db.models.database import CharacterModel

class SQLCharacterRepository(CharacterRepository):
    """SQLAlchemy implementation of character repository."""
    
    def __init__(self, db: Session):
        """
        Initialize the repository with a database session.
        
        Args:
            db: The database session
        """
        self.db = db
    
    async def get_character_by_id(self, character_id: str) -> Optional[Character]:
        """
        Get a character by ID.
        
        Args:
            character_id: The character ID
            
        Returns:
            The character if found, None otherwise
        """
        # Query the database for the character
        character_model = self.db.query(CharacterModel).filter(
            CharacterModel.character_id == character_id
        ).first()
        
        # Return None if not found
        if character_model is None:
            return None
        
        # Convert to Pydantic model and return
        return Character(
            character_id=character_model.character_id,
            name=character_model.name,
            class_type=character_model.class_type,
            level=character_model.level,
            race=character_model.race
        )
    
    async def list_characters(self) -> List[Character]:
        """
        Get a list of all characters.
        
        Returns:
            A list of all characters
        """
        # Query the database for all characters
        character_models = self.db.query(CharacterModel).all()
        
        # Convert to Pydantic models and return
        return [
            Character(
                character_id=model.character_id,
                name=model.name,
                class_type=model.class_type,
                level=model.level,
                race=model.race
            )
            for model in character_models
        ]
    
    async def create_character(self, character_create: CharacterCreate) -> Character:
        """
        Create a new character.
        
        Args:
            character_create: The character data
            
        Returns:
            The created character
        """
        # Create a new character
        character = Character.create(character_create)
        
        # Create a new database model
        character_model = CharacterModel(
            character_id=character.character_id,
            name=character.name,
            class_type=character.class_type,
            level=character.level,
            race=character.race
        )
        
        # Add to database
        self.db.add(character_model)
        self.db.commit()
        self.db.refresh(character_model)
        
        return character
    
    async def update_character(self, character_id: str, character_update: CharacterUpdate) -> Optional[Character]:
        """
        Update a character.
        
        Args:
            character_id: The character ID
            character_update: The updated character data
            
        Returns:
            The updated character if found, None otherwise
        """
        # Query the database for the character
        character_model = self.db.query(CharacterModel).filter(
            CharacterModel.character_id == character_id
        ).first()
        
        # Return None if not found
        if character_model is None:
            return None
        
        # Update the character
        character_model.name = character_update.name
        character_model.class_type = character_update.class_type
        character_model.level = character_update.level
        character_model.race = character_update.race
        
        # Commit changes
        self.db.commit()
        self.db.refresh(character_model)
        
        # Convert to Pydantic model and return
        return Character(
            character_id=character_model.character_id,
            name=character_model.name,
            class_type=character_model.class_type,
            level=character_model.level,
            race=character_model.race
        )
    
    async def delete_character(self, character_id: str) -> bool:
        """
        Delete a character.
        
        Args:
            character_id: The character ID
            
        Returns:
            True if the character was deleted, False otherwise
        """
        # Query the database for the character
        character_model = self.db.query(CharacterModel).filter(
            CharacterModel.character_id == character_id
        ).first()
        
        # Return False if not found
        if character_model is None:
            return False
        
        # Delete the character
        self.db.delete(character_model)
        self.db.commit()
        
        return True
