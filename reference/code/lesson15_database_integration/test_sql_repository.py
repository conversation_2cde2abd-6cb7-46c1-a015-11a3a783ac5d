"""
Tests for SQL repository implementation.
This demonstrates the RED phase of the TDD cycle.
"""
import pytest
import uuid
import os
from typing import Generator
from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker

from rpg_character_db.models.database import Base, CharacterModel
from rpg_character_db.models.character import Character, CharacterCreate, CharacterUpdate
from rpg_character_db.repositories.sql_character_repository import SQLCharacterRepository

# Use an in-memory SQLite database for testing
TEST_DATABASE_URL = "sqlite:///./test.db"

@pytest.fixture
def test_db() -> Generator[Session, None, None]:
    """Create a test database session."""
    # Create engine and session
    engine = create_engine(TEST_DATABASE_URL, connect_args={"check_same_thread": False})
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    # Create session
    db = TestingSessionLocal()
    
    try:
        yield db
    finally:
        db.close()
        # Clean up
        Base.metadata.drop_all(bind=engine)
        if os.path.exists("./test.db"):
            os.remove("./test.db")

@pytest.fixture
def sql_character_repository(test_db: Session) -> SQLCharacterRepository:
    """Create a SQL character repository for testing."""
    return SQLCharacterRepository(test_db)

@pytest.fixture
async def sample_character(sql_character_repository: SQLCharacterRepository) -> Character:
    """Create a sample character in the repository."""
    character_create = CharacterCreate(
        name="Test Character",
        class_type="Warrior",
        level=5,
        race="Human"
    )
    return await sql_character_repository.create_character(character_create)

@pytest.mark.asyncio
async def test_create_character(sql_character_repository: SQLCharacterRepository):
    """Test creating a character."""
    # Arrange
    character_create = CharacterCreate(
        name="New Character",
        class_type="Mage",
        level=1,
        race="Elf"
    )
    
    # Act
    character = await sql_character_repository.create_character(character_create)
    
    # Assert
    assert character.character_id is not None
    assert character.character_id.startswith("CHAR:")
    assert character.name == character_create.name
    assert character.class_type == character_create.class_type
    assert character.level == character_create.level
    assert character.race == character_create.race

@pytest.mark.asyncio
async def test_get_character_by_id(sql_character_repository: SQLCharacterRepository, sample_character: Character):
    """Test getting a character by ID."""
    # Act
    character = await sql_character_repository.get_character_by_id(sample_character.character_id)
    
    # Assert
    assert character is not None
    assert character.character_id == sample_character.character_id
    assert character.name == sample_character.name
    assert character.class_type == sample_character.class_type
    assert character.level == sample_character.level
    assert character.race == sample_character.race

@pytest.mark.asyncio
async def test_get_character_by_id_not_found(sql_character_repository: SQLCharacterRepository):
    """Test getting a non-existent character by ID."""
    # Arrange
    non_existent_id = f"CHAR:{uuid.uuid4().hex[:8]}"
    
    # Act
    character = await sql_character_repository.get_character_by_id(non_existent_id)
    
    # Assert
    assert character is None

@pytest.mark.asyncio
async def test_list_characters(sql_character_repository: SQLCharacterRepository, sample_character: Character):
    """Test listing all characters."""
    # Arrange
    # Create a second character
    character_create = CharacterCreate(
        name="Second Character",
        class_type="Rogue",
        level=3,
        race="Dwarf"
    )
    second_character = await sql_character_repository.create_character(character_create)
    
    # Act
    characters = await sql_character_repository.list_characters()
    
    # Assert
    assert len(characters) == 2
    character_ids = [c.character_id for c in characters]
    assert sample_character.character_id in character_ids
    assert second_character.character_id in character_ids

@pytest.mark.asyncio
async def test_update_character(sql_character_repository: SQLCharacterRepository, sample_character: Character):
    """Test updating a character."""
    # Arrange
    character_update = CharacterUpdate(
        name="Updated Character",
        class_type="Paladin",
        level=6,
        race="Human"
    )
    
    # Act
    updated_character = await sql_character_repository.update_character(
        sample_character.character_id, character_update
    )
    
    # Assert
    assert updated_character is not None
    assert updated_character.character_id == sample_character.character_id
    assert updated_character.name == character_update.name
    assert updated_character.class_type == character_update.class_type
    assert updated_character.level == character_update.level
    assert updated_character.race == character_update.race
    
    # Verify the character was actually updated in the repository
    character = await sql_character_repository.get_character_by_id(sample_character.character_id)
    assert character is not None
    assert character.name == character_update.name
    assert character.class_type == character_update.class_type
    assert character.level == character_update.level
    assert character.race == character_update.race

@pytest.mark.asyncio
async def test_update_character_not_found(sql_character_repository: SQLCharacterRepository):
    """Test updating a non-existent character."""
    # Arrange
    non_existent_id = f"CHAR:{uuid.uuid4().hex[:8]}"
    character_update = CharacterUpdate(
        name="Updated Character",
        class_type="Paladin",
        level=6,
        race="Human"
    )
    
    # Act
    updated_character = await sql_character_repository.update_character(
        non_existent_id, character_update
    )
    
    # Assert
    assert updated_character is None

@pytest.mark.asyncio
async def test_delete_character(sql_character_repository: SQLCharacterRepository, sample_character: Character):
    """Test deleting a character."""
    # Act
    result = await sql_character_repository.delete_character(sample_character.character_id)
    
    # Assert
    assert result is True
    character = await sql_character_repository.get_character_by_id(sample_character.character_id)
    assert character is None

@pytest.mark.asyncio
async def test_delete_character_not_found(sql_character_repository: SQLCharacterRepository):
    """Test deleting a non-existent character."""
    # Arrange
    non_existent_id = f"CHAR:{uuid.uuid4().hex[:8]}"
    
    # Act
    result = await sql_character_repository.delete_character(non_existent_id)
    
    # Assert
    assert result is False
