"""
Dependency injection for repositories.
This provides functions for getting repository instances.
"""
from fastapi import Depends

from rpg_character_db.repositories.interfaces.character_repository import CharacterRepository
from rpg_character_db.repositories.implementations.in_memory_character_repository import InMemoryCharacterRepository

# Create a singleton instance of the repository
_character_repository = InMemoryCharacterRepository()

def get_character_repository() -> CharacterRepository:
    """
    Get a character repository instance.
    
    Returns:
        A character repository instance
    """
    return _character_repository
