# Lesson 12: Implementing Repository

This directory contains materials for Lesson 12 of the TDD course, which focuses on implementing the repository pattern in our API endpoints.

## Contents

- `README.md`: This file
- `api/endpoints/character_endpoints.py`: Refactored endpoints using repository pattern
- `dependencies/repositories.py`: Dependency injection for repositories
- `main.py`: Updated main application file
- `test_character_endpoints.py`: Tests for character endpoints using repository

## Learning Objectives

By the end of this lesson, you should be able to:
- Refactor API endpoints to use the repository pattern
- Implement dependency injection for repositories
- Write tests that use repository mocks
- Understand how to decouple business logic from data access
- Follow the RED-GREEN-REFACTOR cycle when refactoring architecture
