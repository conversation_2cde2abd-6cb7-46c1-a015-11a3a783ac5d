"""
Updated main application file.
This demonstrates the GREEN phase of the TDD cycle.
"""
from fastapi import FastAPI

from rpg_character_db.api.endpoints import character_endpoints

app = FastAPI(title="RPG Character Database API")

@app.get("/")
async def root():
    """
    Get a welcome message.
    
    Returns:
        A welcome message
    """
    return {"message": "Welcome to the RPG Character Database API"}

# Include the character endpoints router
app.include_router(character_endpoints.router)
