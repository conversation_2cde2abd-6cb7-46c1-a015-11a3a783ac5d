"""
Tests for API documentation.
This demonstrates the RED phase of the TDD cycle.
"""
import pytest
from fastapi.testclient import TestClient
import json

from rpg_character_db.main import app

client = TestClient(app)

def test_openapi_endpoint_returns_200():
    """Test that the OpenAPI endpoint returns a 200 status code."""
    # Act
    response = client.get("/openapi.json")
    
    # Assert
    assert response.status_code == 200

def test_openapi_endpoint_returns_valid_schema():
    """Test that the OpenAPI endpoint returns a valid OpenAPI schema."""
    # Act
    response = client.get("/openapi.json")
    
    # Assert
    schema = response.json()
    assert "openapi" in schema
    assert schema["openapi"] == "3.0.2"
    assert "info" in schema
    assert "title" in schema["info"]
    assert "version" in schema["info"]
    assert "paths" in schema

def test_docs_endpoint_returns_200():
    """Test that the Swagger UI endpoint returns a 200 status code."""
    # Act
    response = client.get("/docs")
    
    # Assert
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]

def test_redoc_endpoint_returns_200():
    """Test that the ReDoc endpoint returns a 200 status code."""
    # Act
    response = client.get("/redoc")
    
    # Assert
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]

def test_openapi_schema_contains_character_endpoints():
    """Test that the OpenAPI schema contains character endpoints."""
    # Act
    response = client.get("/openapi.json")
    
    # Assert
    schema = response.json()
    assert "/api/v1/character/{character_id}" in schema["paths"]
    assert "/api/v1/characters" in schema["paths"]
    assert "/api/v1/character" in schema["paths"]

def test_openapi_schema_contains_character_models():
    """Test that the OpenAPI schema contains character models."""
    # Act
    response = client.get("/openapi.json")
    
    # Assert
    schema = response.json()
    assert "Character" in schema["components"]["schemas"]
    assert "CharacterCreate" in schema["components"]["schemas"]
    assert "CharacterUpdate" in schema["components"]["schemas"]

def test_character_model_has_example():
    """Test that the character model has an example."""
    # Act
    response = client.get("/openapi.json")
    
    # Assert
    schema = response.json()
    character_schema = schema["components"]["schemas"]["Character"]
    assert "example" in character_schema
    example = character_schema["example"]
    assert "character_id" in example
    assert "name" in example
    assert "class_type" in example
    assert "level" in example
    assert "race" in example

def test_get_character_endpoint_has_detailed_description():
    """Test that the get character endpoint has a detailed description."""
    # Act
    response = client.get("/openapi.json")
    
    # Assert
    schema = response.json()
    path = schema["paths"]["/api/v1/character/{character_id}"]["get"]
    assert "summary" in path
    assert "description" in path
    assert "responses" in path
    assert "200" in path["responses"]
    assert "404" in path["responses"]
