"""
Updated main application with API documentation.
This demonstrates the GREEN phase of the TDD cycle.
"""
from fastapi import FastAPI
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
from fastapi.openapi.utils import get_openapi

from rpg_character_db.api.endpoints import character_endpoints
from rpg_character_db.dependencies.settings import get_settings, Settings

# Create FastAPI application with metadata
app = FastAPI(
    title="RPG Character Database API",
    description="A RESTful API for managing RPG character data",
    version="1.0.0",
    docs_url=None,  # Disable default docs
    redoc_url=None,  # Disable default redoc
)

@app.get("/")
async def root():
    """
    Get a welcome message.
    
    Returns:
        A welcome message
    """
    return {"message": "Welcome to the RPG Character Database API"}

# Include the character endpoints router
app.include_router(character_endpoints.router)

# Custom OpenAPI endpoint
@app.get("/openapi.json", tags=["documentation"])
async def get_open_api_endpoint(settings: Settings = Depends(get_settings)):
    """
    Get OpenAPI schema.
    
    Returns:
        OpenAPI schema
    """
    openapi_schema = get_openapi(
        title="RPG Character Database API",
        version="1.0.0",
        description="A RESTful API for managing RPG character data",
        routes=app.routes,
    )
    
    # Add API version to schema
    openapi_schema["info"]["version"] = settings.api_version
    
    return openapi_schema

# Custom Swagger UI endpoint
@app.get("/docs", tags=["documentation"])
async def get_documentation():
    """
    Get Swagger UI documentation.
    
    Returns:
        Swagger UI HTML
    """
    return get_swagger_ui_html(
        openapi_url="/openapi.json",
        title="RPG Character Database API Documentation",
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@4/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@4/swagger-ui.css",
    )

# Custom ReDoc endpoint
@app.get("/redoc", tags=["documentation"])
async def get_redoc():
    """
    Get ReDoc documentation.
    
    Returns:
        ReDoc HTML
    """
    return get_redoc_html(
        openapi_url="/openapi.json",
        title="RPG Character Database API Documentation",
        redoc_js_url="https://cdn.jsdelivr.net/npm/redoc@next/bundles/redoc.standalone.js",
    )
