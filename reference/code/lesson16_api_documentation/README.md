# Lesson 16: API Documentation

This directory contains materials for Lesson 16 of the TDD course, which focuses on implementing API documentation with Swagger/OpenAPI.

## Contents

- `README.md`: This file
- `main.py`: Updated main application with API documentation
- `api/endpoints/character_endpoints.py`: Enhanced endpoints with documentation
- `models/character.py`: Enhanced models with documentation
- `test_api_documentation.py`: Tests for API documentation

## Learning Objectives

By the end of this lesson, you should be able to:
- Understand the importance of API documentation
- Implement Swagger/OpenAPI documentation in FastAPI
- Write detailed docstrings for endpoints and models
- Test API documentation endpoints
- Follow the RED-GREEN-REFACTOR cycle when implementing documentation
