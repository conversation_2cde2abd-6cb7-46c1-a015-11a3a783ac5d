"""
Enhanced endpoints with documentation.
This demonstrates the GREEN phase of the TDD cycle.
"""
from fastapi import APIRouter, HTTPException, Path, Depends, status, Query
from typing import List, Optional

from rpg_character_db.models.character import Character, CharacterCreate, CharacterUpdate
from rpg_character_db.repositories.interfaces.character_repository import CharacterRepository
from rpg_character_db.dependencies.repositories import get_character_repository
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter(
    prefix="/api/v1",
    tags=["characters"],
    responses={
        status.HTTP_404_NOT_FOUND: {"description": "Character not found"},
        status.HTTP_400_BAD_REQUEST: {"description": "Invalid request"},
        status.HTTP_500_INTERNAL_SERVER_ERROR: {"description": "Internal server error"}
    }
)

@router.get(
    "/character/{character_id}", 
    response_model=Character, 
    status_code=status.HTTP_200_OK,
    summary="Get a character by ID",
    description="Retrieve a character by its unique identifier. Returns 404 if the character is not found.",
    response_description="The character data"
)
async def get_character_by_id(
    character_id: str = Path(
        ..., 
        description="The ID of the character to retrieve",
        example="CHAR:a1b2c3d4",
        regex=r"^CHAR:[a-zA-Z0-9]{8}$"
    ),
    character_repository: CharacterRepository = Depends(get_character_repository)
):
    """
    Get a character by ID.
    
    This endpoint retrieves a character by its unique identifier.
    If the character is not found, a 404 error is returned.
    
    Args:
        character_id: The character ID
        character_repository: The character repository
        
    Returns:
        The character if found
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character from the repository
    character = await character_repository.get_character_by_id(character_id)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Character not found"
        )
    
    # Return the character
    return character

@router.get(
    "/characters", 
    response_model=List[Character], 
    status_code=status.HTTP_200_OK,
    summary="List all characters",
    description="Retrieve a list of all characters. Supports optional filtering by name, class, and level.",
    response_description="A list of characters"
)
async def list_characters(
    name: Optional[str] = Query(
        None, 
        description="Filter characters by name",
        example="Aragorn"
    ),
    class_type: Optional[str] = Query(
        None, 
        description="Filter characters by class",
        example="Ranger"
    ),
    min_level: Optional[int] = Query(
        None, 
        description="Filter characters by minimum level",
        ge=1,
        example=5
    ),
    character_repository: CharacterRepository = Depends(get_character_repository)
):
    """
    Get a list of all characters.
    
    This endpoint retrieves a list of all characters in the database.
    It supports optional filtering by name, class, and level.
    
    Args:
        name: Optional filter for character name
        class_type: Optional filter for character class
        min_level: Optional filter for minimum character level
        character_repository: The character repository
        
    Returns:
        A list of characters matching the filters
    """
    # Get all characters from the repository
    characters = await character_repository.list_characters()
    
    # Apply filters if provided
    if name:
        characters = [c for c in characters if name.lower() in c.name.lower()]
    
    if class_type:
        characters = [c for c in characters if class_type.lower() in c.class_type.lower()]
    
    if min_level:
        characters = [c for c in characters if c.level >= min_level]
    
    # Return the characters
    return characters

@router.post(
    "/character", 
    response_model=Character, 
    status_code=status.HTTP_201_CREATED,
    summary="Create a new character",
    description="Create a new character with the provided data.",
    response_description="The created character"
)
async def create_character(
    character_create: CharacterCreate,
    character_repository: CharacterRepository = Depends(get_character_repository)
):
    """
    Create a new character.
    
    This endpoint creates a new character with the provided data.
    
    Args:
        character_create: The character data
        character_repository: The character repository
        
    Returns:
        The created character
    """
    # Create a new character in the repository
    character = await character_repository.create_character(character_create)
    
    # Return the created character
    return character

@router.put(
    "/character/{character_id}", 
    response_model=Character, 
    status_code=status.HTTP_200_OK,
    summary="Update a character",
    description="Update an existing character with the provided data. Returns 404 if the character is not found.",
    response_description="The updated character"
)
async def update_character(
    character_update: CharacterUpdate,
    character_id: str = Path(
        ..., 
        description="The ID of the character to update",
        example="CHAR:a1b2c3d4",
        regex=r"^CHAR:[a-zA-Z0-9]{8}$"
    ),
    character_repository: CharacterRepository = Depends(get_character_repository)
):
    """
    Update a character.
    
    This endpoint updates an existing character with the provided data.
    If the character is not found, a 404 error is returned.
    
    Args:
        character_update: The updated character data
        character_id: The character ID
        character_repository: The character repository
        
    Returns:
        The updated character
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Update the character in the repository
    character = await character_repository.update_character(character_id, character_update)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Character not found"
        )
    
    # Return the updated character
    return character

@router.delete(
    "/character/{character_id}", 
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete a character",
    description="Delete a character by its unique identifier. Returns 404 if the character is not found."
)
async def delete_character(
    character_id: str = Path(
        ..., 
        description="The ID of the character to delete",
        example="CHAR:a1b2c3d4",
        regex=r"^CHAR:[a-zA-Z0-9]{8}$"
    ),
    character_repository: CharacterRepository = Depends(get_character_repository)
):
    """
    Delete a character.
    
    This endpoint deletes a character by its unique identifier.
    If the character is not found, a 404 error is returned.
    
    Args:
        character_id: The character ID
        character_repository: The character repository
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Delete the character from the repository
    result = await character_repository.delete_character(character_id)
    
    # Check if the character exists
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Character not found"
        )
    
    # Return no content
    return None
