"""
Enhanced models with documentation.
This defines the structure of character data with detailed documentation.
"""
from pydantic import BaseModel, Field
import uuid
from typing import Dict, Optional

class CharacterBase(BaseModel):
    """Base model for character data."""
    name: str = Field(
        ..., 
        description="Character name",
        example="Aragorn",
        min_length=1,
        max_length=50
    )
    class_type: str = Field(
        ..., 
        description="Character class (e.g., Warrior, Mage)",
        example="Ranger",
        min_length=1,
        max_length=30
    )
    level: int = Field(
        ..., 
        description="Character level", 
        ge=1, 
        le=100,
        example=10
    )
    race: str = Field(
        ..., 
        description="Character race (e.g., Human, Elf)",
        example="Human",
        min_length=1,
        max_length=30
    )

class CharacterCreate(CharacterBase):
    """
    Model for creating a new character.
    
    This model is used when creating a new character in the database.
    It includes all the required fields for a character except the ID,
    which will be generated automatically.
    """
    pass

class CharacterUpdate(CharacterBase):
    """
    Model for updating a character.
    
    This model is used when updating an existing character in the database.
    It includes all the fields that can be updated for a character.
    """
    pass

class Character(CharacterBase):
    """
    Model representing an RPG character.
    
    This model represents a complete character entity with all its properties.
    It includes the character ID and all other character attributes.
    """
    character_id: str = Field(
        ..., 
        description="Unique identifier for the character",
        example="CHAR:a1b2c3d4",
        regex=r"^CHAR:[a-zA-Z0-9]{8}$"
    )
    
    class Config:
        """Pydantic model configuration."""
        schema_extra = {
            "example": {
                "character_id": "CHAR:a1b2c3d4",
                "name": "Aragorn",
                "class_type": "Ranger",
                "level": 10,
                "race": "Human"
            }
        }
    
    @classmethod
    def create(cls, character_create: CharacterCreate) -> "Character":
        """
        Create a new character from a CharacterCreate model.
        
        Args:
            character_create: The character data
            
        Returns:
            A new Character instance with a generated ID
        """
        return cls(
            character_id=f"CHAR:{uuid.uuid4().hex[:8]}",
            **character_create.dict()
        )
