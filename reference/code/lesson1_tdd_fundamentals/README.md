# Lesson 1: TDD Fundamentals

This directory contains materials for Lesson 1 of the TDD course, which introduces the fundamentals of Test-Driven Development.

## Contents

- `tdd_principles.md`: Overview of TDD principles and the RED-GREEN-REFACTOR cycle
- `example_test.py`: A simple example of a test using pytest
- `example_implementation.py`: Implementation that passes the test

## Learning Objectives

By the end of this lesson, you should understand:
- The principles of Test-Driven Development
- The RED-GREEN-REFACTOR cycle
- How to write a simple test using pytest
- How to implement code to pass a test
