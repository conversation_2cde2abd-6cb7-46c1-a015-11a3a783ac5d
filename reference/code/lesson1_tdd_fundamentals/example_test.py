"""
Example test file demonstrating a simple test using pytest.
This shows the RED phase of the TDD cycle.
"""

def test_calculate_character_level():
    """Test that the calculate_character_level function returns the correct level."""
    from example_implementation import calculate_character_level
    
    # Test case 1: Experience points for level 1
    assert calculate_character_level(0) == 1
    
    # Test case 2: Experience points for level 2
    assert calculate_character_level(1000) == 2
    
    # Test case 3: Experience points for level 5
    assert calculate_character_level(10000) == 5
    
    # Test case 4: Experience points for level 10
    assert calculate_character_level(50000) == 10
