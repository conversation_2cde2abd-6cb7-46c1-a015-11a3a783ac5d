"""
Example implementation file that passes the test in example_test.py.
This shows the GREEN phase of the TDD cycle.
"""

def calculate_character_level(experience_points):
    """
    Calculate a character's level based on experience points.
    
    Args:
        experience_points: The character's experience points
        
    Returns:
        The character's level
    """
    if experience_points < 1000:
        return 1
    elif experience_points < 3000:
        return 2
    elif experience_points < 6000:
        return 3
    elif experience_points < 10000:
        return 4
    elif experience_points < 15000:
        return 5
    elif experience_points < 21000:
        return 6
    elif experience_points < 28000:
        return 7
    elif experience_points < 36000:
        return 8
    elif experience_points < 45000:
        return 9
    else:
        return 10
