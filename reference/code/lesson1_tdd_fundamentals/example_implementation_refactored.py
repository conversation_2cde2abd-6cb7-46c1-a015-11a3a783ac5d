"""
Refactored implementation file that passes the test in example_test.py.
This shows the REFACTOR phase of the TDD cycle.
"""

def calculate_character_level(experience_points):
    """
    Calculate a character's level based on experience points.
    
    Args:
        experience_points: The character's experience points
        
    Returns:
        The character's level
    """
    # Level thresholds - the experience points needed for each level
    level_thresholds = [
        0,      # Level 1
        1000,   # Level 2
        3000,   # Level 3
        6000,   # Level 4
        10000,  # Level 5
        15000,  # Level 6
        21000,  # Level 7
        28000,  # Level 8
        36000,  # Level 9
        45000   # Level 10
    ]
    
    # Find the highest level the character has reached
    for level, threshold in enumerate(level_thresholds, 1):
        if experience_points < threshold:
            return level - 1
    
    # If experience points exceed the highest threshold, return the maximum level
    return len(level_thresholds)
