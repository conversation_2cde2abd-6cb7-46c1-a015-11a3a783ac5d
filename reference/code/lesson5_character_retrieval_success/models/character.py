"""
Character model definition.
This defines the structure of character data in our API.
"""
from pydantic import BaseModel, Field
import uuid
from typing import Optional

class CharacterBase(BaseModel):
    """Base model for character data."""
    name: str = Field(..., description="Character name")
    class_type: str = Field(..., description="Character class (e.g., <PERSON>, Mage)")
    level: int = Field(..., description="Character level", ge=1)
    race: str = Field(..., description="Character race (e.g., Human, Elf)")

class CharacterCreate(CharacterBase):
    """Model for creating a new character."""
    pass

class Character(CharacterBase):
    """Model representing an RPG character."""
    character_id: str = Field(..., description="Unique identifier for the character")
    
    @classmethod
    def create(cls, character_create: CharacterCreate) -> "Character":
        """Create a new character from a CharacterCreate model."""
        return cls(
            character_id=f"CHAR:{uuid.uuid4().hex[:8]}",
            **character_create.dict()
        )
