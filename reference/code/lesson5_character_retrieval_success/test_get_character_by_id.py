"""
Test for retrieving a character by ID with success scenario.
This demonstrates the RED phase of the TDD cycle.
"""
import pytest
from fastapi.testclient import TestClient

from rpg_character_db.main import app
from rpg_character_db.db import db

client = TestClient(app)

def test_get_character_by_id_returns_404_for_non_existent_character():
    """Test that getting a non-existent character returns a 404 status code."""
    # Arrange
    character_id = "CHAR:NON_EXISTENT"
    
    # Act
    response = client.get(f"/character/{character_id}")
    
    # Assert
    assert response.status_code == 404
    assert response.json() == {"detail": "Character not found"}

def test_get_character_by_id_returns_422_for_invalid_character_id_format():
    """Test that getting a character with an invalid ID format returns a 422 status code."""
    # Arrange
    invalid_character_id = "INVALID_FORMAT"
    
    # Act
    response = client.get(f"/character/{invalid_character_id}")
    
    # Assert
    assert response.status_code == 422
    assert "detail" in response.json()
    assert "message" in response.json()["detail"]
    assert "expected_pattern" in response.json()["detail"]
    assert "received" in response.json()["detail"]

def test_get_character_by_id_returns_200_for_existing_character():
    """Test that getting an existing character returns a 200 status code and the character data."""
    # Arrange
    character_id = "CHAR:TEST123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human"
    }
    
    # Act
    response = client.get(f"/character/{character_id}")
    
    # Assert
    assert response.status_code == 200
    character = response.json()
    assert character["character_id"] == character_id
    assert character["name"] == "Test Character"
    assert character["class_type"] == "Warrior"
    assert character["level"] == 5
    assert character["race"] == "Human"
