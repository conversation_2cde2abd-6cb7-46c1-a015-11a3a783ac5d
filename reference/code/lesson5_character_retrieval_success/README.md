# Lesson 5: Character Retrieval (Success)

This directory contains materials for Lesson 5 of the TDD course, which focuses on implementing successful character retrieval.

## Contents

- `README.md`: This file
- `test_get_character_by_id.py`: Test for retrieving a character by ID with success scenario
- `models/character.py`: Character model definition
- `api/endpoints/character_retrieval.py`: Endpoint implementation for character retrieval
- `db.py`: In-memory database with sample data

## Learning Objectives

By the end of this lesson, you should be able to:
- Write tests for successful API responses
- Implement database access in FastAPI endpoints
- Populate test data for API testing
- Follow the RED-GREEN-REFACTOR cycle for success scenarios
