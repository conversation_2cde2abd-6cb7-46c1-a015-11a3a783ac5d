"""
Endpoint implementation for character retrieval with validation.
This demonstrates the GREEN phase of the TDD cycle.
"""
from fastapi import APIRouter, HTTPException, Path

from rpg_character_db.models.character import Character
from rpg_character_db.db import db
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter()

@router.get("/character/{character_id}", response_model=Character)
async def get_character_by_id(
    character_id: str = Path(..., description="The ID of the character to retrieve")
):
    """
    Get a character by ID.
    
    Args:
        character_id: The character ID
        
    Returns:
        The character if found
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Check if the character exists in the database
    if character_id not in db:
        raise HTTPException(status_code=404, detail="Character not found")
    
    # Return the character
    return db[character_id]
