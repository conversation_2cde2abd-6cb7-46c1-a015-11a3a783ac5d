# RPG Character Database Project Structure

This document outlines the structure of the RPG Character Database project that we'll be building throughout this course.

## Project Overview

The RPG Character Database is a RESTful API for managing RPG character data, including attributes and skills. It will be built using FastAPI and follow Test-Driven Development principles.

## Directory Structure

```
rpg_character_db/
├── rpg_character_db/         # Main package
│   ├── __init__.py           # Package initialization
│   ├── main.py               # Application entry point
│   ├── models/               # Pydantic models
│   │   ├── __init__.py
│   │   ├── character.py      # Character models
│   │   ├── attribute.py      # Attribute models
│   │   └── skill.py          # Skill models (added later)
│   ├── api/                  # API endpoints
│   │   ├── __init__.py
│   │   ├── router.py         # Main router
│   │   └── endpoints/        # Endpoint modules
│   │       ├── __init__.py
│   │       ├── character_retrieval.py
│   │       ├── character_creation.py
│   │       └── character_update.py
│   ├── db.py                 # In-memory database (replaced later)
│   ├── repositories/         # Repository pattern (added later)
│   │   ├── __init__.py
│   │   ├── interfaces/       # Repository interfaces
│   │   └── implementations/  # Repository implementations
│   ├── dependencies/         # Dependency injection (added later)
│   │   └── __init__.py
│   ├── database/             # Database integration (added later)
│   │   ├── __init__.py
│   │   ├── core.py
│   │   └── models/           # SQLAlchemy models
│   └── utils/                # Utility functions (added later)
│       └── validation.py
├── tests/                    # Test directory
│   ├── __init__.py
│   ├── conftest.py           # Test fixtures
│   ├── test_get_character.py
│   ├── test_create_character.py
│   └── test_update_character.py
├── requirements.txt          # Project dependencies
└── README.md                 # Project documentation
```

## Evolution of the Project

Throughout the course, we'll evolve this project:

1. Start with a simple in-memory database
2. Add more endpoints for character management
3. Implement the repository pattern
4. Add dependency injection
5. Integrate with SQLAlchemy for database persistence
6. Enhance API documentation
7. Add API versioning and deployment configuration

This incremental approach allows us to follow TDD principles while building a complete API.
