# Setup Instructions for RPG Character Database Project

This document provides step-by-step instructions for setting up the RPG Character Database project.

## Prerequisites

- Python 3.8 or higher
- pip (Python package installer)
- Git (optional, for version control)

## Setup Steps

### 1. Create a Virtual Environment

```bash
# Create a new directory for the project
mkdir rpg_character_db
cd rpg_character_db

# Create a virtual environment
python -m venv venv

# Activate the virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### 2. Install Dependencies

```bash
# Install the required packages
pip install -r requirements.txt
```

### 3. Create Project Structure

```bash
# Create the main package directory
mkdir -p rpg_character_db/models rpg_character_db/api/endpoints tests

# Create necessary files
touch rpg_character_db/__init__.py
touch rpg_character_db/models/__init__.py
touch rpg_character_db/api/__init__.py
touch rpg_character_db/api/endpoints/__init__.py
touch rpg_character_db/main.py
touch rpg_character_db/db.py
touch tests/__init__.py
touch tests/conftest.py
```

### 4. Create Basic FastAPI Application

Create a basic FastAPI application in `rpg_character_db/main.py`:

```python
from fastapi import FastAPI

app = FastAPI(title="RPG Character Database API")

@app.get("/")
async def root():
    return {"message": "Welcome to the RPG Character Database API"}
```

### 5. Create In-Memory Database

Create an in-memory database in `rpg_character_db/db.py`:

```python
"""
In-memory database for storing character data.
This will be replaced with a proper database later.
"""

# Our in-memory database is just a dictionary
db = {}
```

### 6. Run the Application

```bash
# Run the application with uvicorn
uvicorn rpg_character_db.main:app --reload
```

The application should now be running at http://localhost:8000.

### 7. Verify the Setup

- Open your browser and navigate to http://localhost:8000
- You should see a JSON response: `{"message": "Welcome to the RPG Character Database API"}`
- Navigate to http://localhost:8000/docs to see the Swagger UI documentation

## Next Steps

Now that you have set up the project, you're ready to start implementing the API using Test-Driven Development. In the next lesson, we'll write our first test and implement the corresponding functionality.
