# Lesson 2: Project Setup

This directory contains materials for Lesson 2 of the TDD course, which focuses on setting up the RPG Character Database project.

## Contents

- `README.md`: This file
- `requirements.txt`: Project dependencies
- `project_structure.md`: Overview of the project structure
- `setup_instructions.md`: Step-by-step instructions for setting up the project

## Learning Objectives

By the end of this lesson, you should be able to:
- Set up a Python project with FastAPI
- Configure pytest for testing
- Understand the project structure
- Create a basic FastAPI application
