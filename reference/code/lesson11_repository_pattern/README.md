# Lesson 11: Repository Pattern

This directory contains materials for Lesson 11 of the TDD course, which focuses on implementing the repository pattern to abstract data access.

## Contents

- `README.md`: This file
- `repositories/interfaces/character_repository.py`: Interface for character repository
- `repositories/implementations/in_memory_character_repository.py`: In-memory implementation of character repository
- `test_character_repository.py`: Tests for character repository implementation
- `models/character.py`: Character model definition

## Learning Objectives

By the end of this lesson, you should be able to:
- Understand the repository pattern and its benefits
- Create interfaces for data access
- Implement the repository pattern for data access
- Write tests for repository implementations
- Decouple business logic from data access
- Follow the RED-GREEN-REFACTOR cycle for architectural patterns
