"""
Tests for character repository implementation.
This demonstrates the RED phase of the TDD cycle.
"""
import pytest
import uuid
from typing import AsyncGenerator

from rpg_character_db.repositories.interfaces.character_repository import CharacterRepository
from rpg_character_db.repositories.implementations.in_memory_character_repository import InMemoryCharacterRepository
from rpg_character_db.models.character import Character, CharacterCreate, CharacterUpdate

@pytest.fixture
async def character_repository() -> AsyncGenerator[CharacterRepository, None]:
    """Create a character repository for testing."""
    repo = InMemoryCharacterRepository()
    yield repo

@pytest.fixture
async def sample_character(character_repository: CharacterRepository) -> Character:
    """Create a sample character in the repository."""
    character_create = CharacterCreate(
        name="Test Character",
        class_type="Warrior",
        level=5,
        race="Human"
    )
    return await character_repository.create_character(character_create)

@pytest.mark.asyncio
async def test_create_character(character_repository: CharacterRepository):
    """Test creating a character."""
    # Arrange
    character_create = CharacterCreate(
        name="New Character",
        class_type="Mage",
        level=1,
        race="Elf"
    )
    
    # Act
    character = await character_repository.create_character(character_create)
    
    # Assert
    assert character.character_id is not None
    assert character.character_id.startswith("CHAR:")
    assert character.name == character_create.name
    assert character.class_type == character_create.class_type
    assert character.level == character_create.level
    assert character.race == character_create.race

@pytest.mark.asyncio
async def test_get_character_by_id(character_repository: CharacterRepository, sample_character: Character):
    """Test getting a character by ID."""
    # Act
    character = await character_repository.get_character_by_id(sample_character.character_id)
    
    # Assert
    assert character is not None
    assert character.character_id == sample_character.character_id
    assert character.name == sample_character.name
    assert character.class_type == sample_character.class_type
    assert character.level == sample_character.level
    assert character.race == sample_character.race

@pytest.mark.asyncio
async def test_get_character_by_id_not_found(character_repository: CharacterRepository):
    """Test getting a non-existent character by ID."""
    # Arrange
    non_existent_id = f"CHAR:{uuid.uuid4().hex[:8]}"
    
    # Act
    character = await character_repository.get_character_by_id(non_existent_id)
    
    # Assert
    assert character is None

@pytest.mark.asyncio
async def test_list_characters(character_repository: CharacterRepository, sample_character: Character):
    """Test listing all characters."""
    # Arrange
    # Create a second character
    character_create = CharacterCreate(
        name="Second Character",
        class_type="Rogue",
        level=3,
        race="Dwarf"
    )
    second_character = await character_repository.create_character(character_create)
    
    # Act
    characters = await character_repository.list_characters()
    
    # Assert
    assert len(characters) == 2
    character_ids = [c.character_id for c in characters]
    assert sample_character.character_id in character_ids
    assert second_character.character_id in character_ids

@pytest.mark.asyncio
async def test_update_character(character_repository: CharacterRepository, sample_character: Character):
    """Test updating a character."""
    # Arrange
    character_update = CharacterUpdate(
        name="Updated Character",
        class_type="Paladin",
        level=6,
        race="Human"
    )
    
    # Act
    updated_character = await character_repository.update_character(
        sample_character.character_id, character_update
    )
    
    # Assert
    assert updated_character is not None
    assert updated_character.character_id == sample_character.character_id
    assert updated_character.name == character_update.name
    assert updated_character.class_type == character_update.class_type
    assert updated_character.level == character_update.level
    assert updated_character.race == character_update.race
    
    # Verify the character was actually updated in the repository
    character = await character_repository.get_character_by_id(sample_character.character_id)
    assert character is not None
    assert character.name == character_update.name
    assert character.class_type == character_update.class_type
    assert character.level == character_update.level
    assert character.race == character_update.race

@pytest.mark.asyncio
async def test_update_character_not_found(character_repository: CharacterRepository):
    """Test updating a non-existent character."""
    # Arrange
    non_existent_id = f"CHAR:{uuid.uuid4().hex[:8]}"
    character_update = CharacterUpdate(
        name="Updated Character",
        class_type="Paladin",
        level=6,
        race="Human"
    )
    
    # Act
    updated_character = await character_repository.update_character(
        non_existent_id, character_update
    )
    
    # Assert
    assert updated_character is None

@pytest.mark.asyncio
async def test_delete_character(character_repository: CharacterRepository, sample_character: Character):
    """Test deleting a character."""
    # Act
    result = await character_repository.delete_character(sample_character.character_id)
    
    # Assert
    assert result is True
    character = await character_repository.get_character_by_id(sample_character.character_id)
    assert character is None

@pytest.mark.asyncio
async def test_delete_character_not_found(character_repository: CharacterRepository):
    """Test deleting a non-existent character."""
    # Arrange
    non_existent_id = f"CHAR:{uuid.uuid4().hex[:8]}"
    
    # Act
    result = await character_repository.delete_character(non_existent_id)
    
    # Assert
    assert result is False
