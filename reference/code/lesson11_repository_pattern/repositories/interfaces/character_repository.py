"""
Interface for character repository.
This defines the contract that all character repository implementations must follow.
"""
from abc import ABC, abstractmethod
from typing import List, Optional

from rpg_character_db.models.character import Character, CharacterCreate, CharacterUpdate

class CharacterRepository(ABC):
    """Interface for character repository."""
    
    @abstractmethod
    async def get_character_by_id(self, character_id: str) -> Optional[Character]:
        """
        Get a character by ID.
        
        Args:
            character_id: The character ID
            
        Returns:
            The character if found, None otherwise
        """
        pass
    
    @abstractmethod
    async def list_characters(self) -> List[Character]:
        """
        Get a list of all characters.
        
        Returns:
            A list of all characters
        """
        pass
    
    @abstractmethod
    async def create_character(self, character_create: CharacterCreate) -> Character:
        """
        Create a new character.
        
        Args:
            character_create: The character data
            
        Returns:
            The created character
        """
        pass
    
    @abstractmethod
    async def update_character(self, character_id: str, character_update: CharacterUpdate) -> Optional[Character]:
        """
        Update a character.
        
        Args:
            character_id: The character ID
            character_update: The updated character data
            
        Returns:
            The updated character if found, None otherwise
        """
        pass
    
    @abstractmethod
    async def delete_character(self, character_id: str) -> bool:
        """
        Delete a character.
        
        Args:
            character_id: The character ID
            
        Returns:
            True if the character was deleted, False otherwise
        """
        pass
