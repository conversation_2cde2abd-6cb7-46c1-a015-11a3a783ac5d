"""
In-memory implementation of character repository.
This demonstrates the GREEN phase of the TDD cycle.
"""
from typing import Dict, List, Optional

from rpg_character_db.repositories.interfaces.character_repository import CharacterRepository
from rpg_character_db.models.character import Character, CharacterCreate, CharacterUpdate

class InMemoryCharacterRepository(CharacterRepository):
    """In-memory implementation of character repository."""
    
    def __init__(self):
        """Initialize the repository with an empty database."""
        self.db: Dict[str, Dict] = {}
    
    async def get_character_by_id(self, character_id: str) -> Optional[Character]:
        """
        Get a character by ID.
        
        Args:
            character_id: The character ID
            
        Returns:
            The character if found, None otherwise
        """
        if character_id not in self.db:
            return None
        
        return Character(**self.db[character_id])
    
    async def list_characters(self) -> List[Character]:
        """
        Get a list of all characters.
        
        Returns:
            A list of all characters
        """
        return [Character(**character_data) for character_data in self.db.values()]
    
    async def create_character(self, character_create: CharacterCreate) -> Character:
        """
        Create a new character.
        
        Args:
            character_create: The character data
            
        Returns:
            The created character
        """
        # Create a new character
        character = Character.create(character_create)
        
        # Add to database
        self.db[character.character_id] = character.dict()
        
        return character
    
    async def update_character(self, character_id: str, character_update: CharacterUpdate) -> Optional[Character]:
        """
        Update a character.
        
        Args:
            character_id: The character ID
            character_update: The updated character data
            
        Returns:
            The updated character if found, None otherwise
        """
        # Check if the character exists
        if character_id not in self.db:
            return None
        
        # Update the character
        updated_character = Character(
            character_id=character_id,
            **character_update.dict()
        )
        
        # Save to database
        self.db[character_id] = updated_character.dict()
        
        return updated_character
    
    async def delete_character(self, character_id: str) -> bool:
        """
        Delete a character.
        
        Args:
            character_id: The character ID
            
        Returns:
            True if the character was deleted, False otherwise
        """
        # Check if the character exists
        if character_id not in self.db:
            return False
        
        # Delete the character
        del self.db[character_id]
        
        return True
