"""
Our first test for retrieving a character by ID.
This demonstrates the RED phase of the TDD cycle.
"""
import pytest
from fastapi.testclient import TestClient

from rpg_character_db.main import app

client = TestClient(app)

def test_get_character_by_id_returns_404_for_non_existent_character():
    """Test that getting a non-existent character returns a 404 status code."""
    # Arrange
    character_id = "CHAR:NON_EXISTENT"
    
    # Act
    response = client.get(f"/character/{character_id}")
    
    # Assert
    assert response.status_code == 404
    assert response.json() == {"detail": "Character not found"}
