# Lesson 3: First Test

This directory contains materials for Lesson 3 of the TDD course, which focuses on writing our first test for the RPG Character Database API.

## Contents

- `README.md`: This file
- `test_get_character_by_id.py`: Our first test for retrieving a character by ID
- `main.py`: Initial implementation of the FastAPI application
- `models/character.py`: Character model definition
- `api/endpoints/character_retrieval.py`: Endpoint implementation for character retrieval

## Learning Objectives

By the end of this lesson, you should be able to:
- Write a test for a FastAPI endpoint using pytest
- Understand how to test HTTP responses
- Implement a basic endpoint to make the test pass
- Follow the RED-GREEN-REFACTOR cycle in a FastAPI project
