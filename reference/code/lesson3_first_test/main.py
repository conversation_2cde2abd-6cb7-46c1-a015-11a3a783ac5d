"""
Initial implementation of the FastAPI application.
This will be expanded as we add more endpoints.
"""
from fastapi import FastAPI

from rpg_character_db.api.endpoints import character_retrieval

app = FastAPI(title="RPG Character Database API")

@app.get("/")
async def root():
    """
    Get a welcome message.
    
    Returns:
        A welcome message
    """
    return {"message": "Welcome to the RPG Character Database API"}

# Include the character retrieval router
app.include_router(character_retrieval.router)
