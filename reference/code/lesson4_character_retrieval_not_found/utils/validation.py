"""
Validation utilities for character IDs.
This provides functions for validating character ID formats.
"""
import re
from fastapi import HTTPException

def validate_character_id(character_id: str) -> None:
    """
    Validate that a character ID has the correct format.
    
    Args:
        character_id: The character ID to validate
        
    Raises:
        HTTPException: If the character ID has an invalid format
    """
    # Character IDs should have the format "CHAR:<id>"
    pattern = r"^CHAR:[a-zA-Z0-9]+$"
    
    if not re.match(pattern, character_id):
        raise HTTPException(
            status_code=422,
            detail={
                "message": "Invalid character ID format",
                "expected_pattern": pattern,
                "received": character_id
            }
        )
