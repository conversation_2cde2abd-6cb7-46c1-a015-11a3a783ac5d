# Lesson 4: Character Retrieval (Not Found)

This directory contains materials for Lesson 4 of the TDD course, which focuses on implementing character retrieval with proper error handling for non-existent characters.

## Contents

- `README.md`: This file
- `test_get_character_by_id.py`: Test for retrieving a character by ID with not found scenario
- `models/character.py`: Character model definition
- `api/endpoints/character_retrieval.py`: Endpoint implementation for character retrieval
- `utils/validation.py`: Validation utilities for character IDs

## Learning Objectives

By the end of this lesson, you should be able to:
- Write tests for error handling in API endpoints
- Implement proper error responses in FastAPI
- Validate input parameters
- Follow the RED-GREEN-REFACTOR cycle for error handling scenarios
