"""
Refactored test for creating a character using fixtures.
This demonstrates how to use pytest fixtures to improve test organization.
"""
import pytest

def test_create_character_returns_201(client, clear_db):
    """Test that creating a character returns a 201 status code."""
    # Arrange
    character_data = {
        "name": "New Character",
        "class_type": "Mage",
        "level": 1,
        "race": "Elf"
    }
    
    # Act
    response = client.post("/character", json=character_data)
    
    # Assert
    assert response.status_code == 201

def test_create_character_returns_created_character(client, clear_db):
    """Test that creating a character returns the created character with an ID."""
    # Arrange
    character_data = {
        "name": "New Character",
        "class_type": "Mage",
        "level": 1,
        "race": "Elf"
    }
    
    # Act
    response = client.post("/character", json=character_data)
    
    # Assert
    character = response.json()
    assert "character_id" in character
    assert character["character_id"].startswith("CHAR:")
    assert character["name"] == character_data["name"]
    assert character["class_type"] == character_data["class_type"]
    assert character["level"] == character_data["level"]
    assert character["race"] == character_data["race"]

def test_create_character_adds_to_database(client, clear_db):
    """Test that creating a character adds it to the database."""
    # Arrange
    character_data = {
        "name": "New Character",
        "class_type": "Mage",
        "level": 1,
        "race": "Elf"
    }
    
    # Act
    response = client.post("/character", json=character_data)
    
    # Assert
    from rpg_character_db.db import db
    character = response.json()
    assert character["character_id"] in db

def test_create_character_with_invalid_data_returns_422(client, clear_db):
    """Test that creating a character with invalid data returns a 422 status code."""
    # Arrange - Missing required fields
    character_data = {
        "name": "New Character",
        "class_type": "Mage"
        # Missing level and race
    }
    
    # Act
    response = client.post("/character", json=character_data)
    
    # Assert
    assert response.status_code == 422
    assert "detail" in response.json()

def test_create_character_with_invalid_level_returns_422(client, clear_db):
    """Test that creating a character with an invalid level returns a 422 status code."""
    # Arrange - Level less than 1
    character_data = {
        "name": "New Character",
        "class_type": "Mage",
        "level": 0,  # Invalid level
        "race": "Elf"
    }
    
    # Act
    response = client.post("/character", json=character_data)
    
    # Assert
    assert response.status_code == 422
    assert "detail" in response.json()
