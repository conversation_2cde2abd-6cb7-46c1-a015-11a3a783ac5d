# Lesson 8: Test Fixtures

This directory contains materials for Lesson 8 of the TDD course, which focuses on implementing test fixtures to improve test organization and reduce duplication.

## Contents

- `README.md`: This file
- `conftest.py`: Test fixtures for character testing
- `test_get_character.py`: Refactored test using fixtures
- `test_create_character.py`: Refactored test using fixtures
- `test_update_character.py`: Refactored test using fixtures

## Learning Objectives

By the end of this lesson, you should be able to:
- Create and use pytest fixtures
- Organize test code to reduce duplication
- Implement test setup and teardown
- Create test data factories
- Improve test maintainability
