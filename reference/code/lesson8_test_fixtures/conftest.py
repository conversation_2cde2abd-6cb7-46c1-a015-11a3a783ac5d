"""
Test fixtures for character testing.
This file contains pytest fixtures that can be used across multiple test files.
"""
import pytest
from fastapi.testclient import TestClient

from rpg_character_db.main import app
from rpg_character_db.db import db
from rpg_character_db.models.character import Character, CharacterCreate

@pytest.fixture
def client():
    """Create a test client for the FastAPI application."""
    return TestClient(app)

@pytest.fixture
def clear_db():
    """Clear the database before and after each test."""
    # Clear the database before the test
    db.clear()
    
    # Run the test
    yield
    
    # Clear the database after the test
    db.clear()

@pytest.fixture
def existing_character(clear_db):
    """Create a test character in the database."""
    character_id = "CHAR:TEST123"
    character_data = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human"
    }
    db[character_id] = character_data
    
    # Return the character data
    return Character(**character_data)

@pytest.fixture
def character_factory():
    """Factory fixture to create test characters."""
    def _create_character(
        character_id=None,
        name="Test Character",
        class_type="Warrior",
        level=5,
        race="Human"
    ):
        """Create a test character with the given attributes."""
        if character_id is None:
            # Create a new character through the model
            character_create = CharacterCreate(
                name=name,
                class_type=class_type,
                level=level,
                race=race
            )
            character = Character.create(character_create)
            character_id = character.character_id
        else:
            # Create a character with a specific ID
            character = Character(
                character_id=character_id,
                name=name,
                class_type=class_type,
                level=level,
                race=race
            )
        
        # Add to database
        db[character_id] = character.dict()
        
        return character
    
    return _create_character

@pytest.fixture
def multiple_characters(character_factory, clear_db):
    """Create multiple test characters."""
    characters = []
    for i in range(3):
        characters.append(character_factory(
            name=f"Character {i}",
            class_type=["Warrior", "Mage", "Rogue"][i % 3],
            level=i + 1,
            race=["Human", "Elf", "Dwarf"][i % 3]
        ))
    return characters
