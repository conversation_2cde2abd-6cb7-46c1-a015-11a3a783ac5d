"""
Refactored test for updating a character using fixtures.
This demonstrates how to use pytest fixtures to improve test organization.
"""
import pytest

def test_update_character_returns_200(client, existing_character):
    """Test that updating a character returns a 200 status code."""
    # Arrange
    update_data = {
        "name": "Updated Character",
        "class_type": "Paladin",
        "level": 6,
        "race": "Human"
    }
    
    # Act
    response = client.put(f"/character/{existing_character.character_id}", json=update_data)
    
    # Assert
    assert response.status_code == 200

def test_update_character_returns_updated_character(client, existing_character):
    """Test that updating a character returns the updated character data."""
    # Arrange
    update_data = {
        "name": "Updated Character",
        "class_type": "Paladin",
        "level": 6,
        "race": "Human"
    }
    
    # Act
    response = client.put(f"/character/{existing_character.character_id}", json=update_data)
    
    # Assert
    character = response.json()
    assert character["character_id"] == existing_character.character_id
    assert character["name"] == update_data["name"]
    assert character["class_type"] == update_data["class_type"]
    assert character["level"] == update_data["level"]
    assert character["race"] == update_data["race"]

def test_update_character_updates_database(client, existing_character):
    """Test that updating a character updates it in the database."""
    # Arrange
    update_data = {
        "name": "Updated Character",
        "class_type": "Paladin",
        "level": 6,
        "race": "Human"
    }
    
    # Act
    response = client.put(f"/character/{existing_character.character_id}", json=update_data)
    
    # Assert
    from rpg_character_db.db import db
    assert existing_character.character_id in db
    assert db[existing_character.character_id]["name"] == update_data["name"]
    assert db[existing_character.character_id]["class_type"] == update_data["class_type"]
    assert db[existing_character.character_id]["level"] == update_data["level"]
    assert db[existing_character.character_id]["race"] == update_data["race"]

def test_update_non_existent_character_returns_404(client, clear_db):
    """Test that updating a non-existent character returns a 404 status code."""
    # Arrange
    character_id = "CHAR:NON_EXISTENT"
    update_data = {
        "name": "Updated Character",
        "class_type": "Paladin",
        "level": 6,
        "race": "Human"
    }
    
    # Act
    response = client.put(f"/character/{character_id}", json=update_data)
    
    # Assert
    assert response.status_code == 404
    assert response.json() == {"detail": "Character not found"}

def test_update_character_with_invalid_data_returns_422(client, existing_character):
    """Test that updating a character with invalid data returns a 422 status code."""
    # Arrange
    # Invalid level (less than 1)
    update_data = {
        "name": "Updated Character",
        "class_type": "Paladin",
        "level": 0,
        "race": "Human"
    }
    
    # Act
    response = client.put(f"/character/{existing_character.character_id}", json=update_data)
    
    # Assert
    assert response.status_code == 422
    assert "detail" in response.json()

def test_update_character_with_invalid_id_format_returns_422(client):
    """Test that updating a character with an invalid ID format returns a 422 status code."""
    # Arrange
    invalid_character_id = "INVALID_FORMAT"
    update_data = {
        "name": "Updated Character",
        "class_type": "Paladin",
        "level": 6,
        "race": "Human"
    }
    
    # Act
    response = client.put(f"/character/{invalid_character_id}", json=update_data)
    
    # Assert
    assert response.status_code == 422
    assert "detail" in response.json()
