"""
Refactored test for retrieving a character by ID using fixtures.
This demonstrates how to use pytest fixtures to improve test organization.
"""
import pytest

def test_get_character_by_id_returns_404_for_non_existent_character(client):
    """Test that getting a non-existent character returns a 404 status code."""
    # Arrange
    character_id = "CHAR:NON_EXISTENT"
    
    # Act
    response = client.get(f"/character/{character_id}")
    
    # Assert
    assert response.status_code == 404
    assert response.json() == {"detail": "Character not found"}

def test_get_character_by_id_returns_422_for_invalid_character_id_format(client):
    """Test that getting a character with an invalid ID format returns a 422 status code."""
    # Arrange
    invalid_character_id = "INVALID_FORMAT"
    
    # Act
    response = client.get(f"/character/{invalid_character_id}")
    
    # Assert
    assert response.status_code == 422
    assert "detail" in response.json()
    assert "message" in response.json()["detail"]
    assert "expected_pattern" in response.json()["detail"]
    assert "received" in response.json()["detail"]

def test_get_character_by_id_returns_200_for_existing_character(client, existing_character):
    """Test that getting an existing character returns a 200 status code and the character data."""
    # Act
    response = client.get(f"/character/{existing_character.character_id}")
    
    # Assert
    assert response.status_code == 200
    character = response.json()
    assert character["character_id"] == existing_character.character_id
    assert character["name"] == existing_character.name
    assert character["class_type"] == existing_character.class_type
    assert character["level"] == existing_character.level
    assert character["race"] == existing_character.race
