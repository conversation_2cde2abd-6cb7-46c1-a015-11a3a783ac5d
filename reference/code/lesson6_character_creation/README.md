# Lesson 6: Character Creation

This directory contains materials for Lesson 6 of the TDD course, which focuses on implementing character creation functionality.

## Contents

- `README.md`: This file
- `test_create_character.py`: Test for creating a new character
- `models/character.py`: Character model definition
- `api/endpoints/character_creation.py`: Endpoint implementation for character creation
- `db.py`: In-memory database

## Learning Objectives

By the end of this lesson, you should be able to:
- Write tests for POST endpoints in FastAPI
- Implement request body validation using Pydantic models
- Create endpoints that modify data
- Follow the RED-GREEN-REFACTOR cycle for creation operations
