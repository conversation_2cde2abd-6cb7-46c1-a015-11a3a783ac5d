"""
Test for creating a new character.
This demonstrates the RED phase of the TDD cycle.
"""
import pytest
from fastapi.testclient import TestClient

from rpg_character_db.main import app
from rpg_character_db.db import db

client = TestClient(app)

def test_create_character_returns_201():
    """Test that creating a character returns a 201 status code."""
    # Arrange
    character_data = {
        "name": "New Character",
        "class_type": "Mage",
        "level": 1,
        "race": "Elf"
    }
    
    # Act
    response = client.post("/character", json=character_data)
    
    # Assert
    assert response.status_code == 201

def test_create_character_returns_created_character():
    """Test that creating a character returns the created character with an ID."""
    # Arrange
    character_data = {
        "name": "New Character",
        "class_type": "Mage",
        "level": 1,
        "race": "Elf"
    }
    
    # Act
    response = client.post("/character", json=character_data)
    
    # Assert
    character = response.json()
    assert "character_id" in character
    assert character["character_id"].startswith("CHAR:")
    assert character["name"] == character_data["name"]
    assert character["class_type"] == character_data["class_type"]
    assert character["level"] == character_data["level"]
    assert character["race"] == character_data["race"]

def test_create_character_adds_to_database():
    """Test that creating a character adds it to the database."""
    # Arrange
    character_data = {
        "name": "New Character",
        "class_type": "Mage",
        "level": 1,
        "race": "Elf"
    }
    initial_db_size = len(db)
    
    # Act
    response = client.post("/character", json=character_data)
    
    # Assert
    assert len(db) == initial_db_size + 1
    character = response.json()
    assert character["character_id"] in db

def test_create_character_with_invalid_data_returns_422():
    """Test that creating a character with invalid data returns a 422 status code."""
    # Arrange - Missing required fields
    character_data = {
        "name": "New Character",
        "class_type": "Mage"
        # Missing level and race
    }
    
    # Act
    response = client.post("/character", json=character_data)
    
    # Assert
    assert response.status_code == 422
    assert "detail" in response.json()

def test_create_character_with_invalid_level_returns_422():
    """Test that creating a character with an invalid level returns a 422 status code."""
    # Arrange - Level less than 1
    character_data = {
        "name": "New Character",
        "class_type": "Mage",
        "level": 0,  # Invalid level
        "race": "Elf"
    }
    
    # Act
    response = client.post("/character", json=character_data)
    
    # Assert
    assert response.status_code == 422
    assert "detail" in response.json()
