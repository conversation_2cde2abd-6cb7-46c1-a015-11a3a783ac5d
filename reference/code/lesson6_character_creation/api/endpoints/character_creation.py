"""
Endpoint implementation for character creation.
This demonstrates the GREEN phase of the TDD cycle.
"""
from fastapi import APIRouter, status

from rpg_character_db.models.character import Character, CharacterCreate
from rpg_character_db.db import db

router = APIRouter()

@router.post("/character", response_model=Character, status_code=status.HTTP_201_CREATED)
async def create_character(character_create: CharacterCreate):
    """
    Create a new character.
    
    Args:
        character_create: The character data
        
    Returns:
        The created character
    """
    # Create a new character
    character = Character.create(character_create)
    
    # Add to database
    db[character.character_id] = character.dict()
    
    return character
