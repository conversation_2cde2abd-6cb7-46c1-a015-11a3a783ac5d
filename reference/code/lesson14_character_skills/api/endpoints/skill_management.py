"""
Endpoint implementation for skill management.
This demonstrates the GREEN phase of the TDD cycle.
"""
from fastapi import APIRouter, HTTPException, Path, status
from typing import List

from rpg_character_db.models.skill import Skill, SkillCreate, SkillUpdate
from rpg_character_db.db import db
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter(tags=["skills"])

@router.post("/character/{character_id}/skill", response_model=Skill, status_code=status.HTTP_201_CREATED)
async def add_skill_to_character(
    skill_create: SkillCreate,
    character_id: str = Path(..., description="The ID of the character to add the skill to")
):
    """
    Add a skill to a character.
    
    Args:
        skill_create: The skill data
        character_id: The character ID
        
    Returns:
        The created skill
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Check if the character exists in the database
    if character_id not in db:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Character not found"
        )
    
    # Create a new skill
    skill = Skill.create(skill_create)
    
    # Initialize skills dictionary if it doesn't exist
    if "skills" not in db[character_id]:
        db[character_id]["skills"] = {}
    
    # Add skill to character
    db[character_id]["skills"][skill.skill_id] = skill.dict()
    
    return skill

@router.get("/character/{character_id}/skills", response_model=List[Skill], status_code=status.HTTP_200_OK)
async def get_character_skills(
    character_id: str = Path(..., description="The ID of the character to get skills for")
):
    """
    Get all skills for a character.
    
    Args:
        character_id: The character ID
        
    Returns:
        A list of all skills for the character
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Check if the character exists in the database
    if character_id not in db:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Character not found"
        )
    
    # Get skills from character
    skills = db[character_id].get("skills", {})
    
    # Return skills as a list
    return list(skills.values())

@router.get("/character/{character_id}/skill/{skill_id}", response_model=Skill, status_code=status.HTTP_200_OK)
async def get_character_skill_by_id(
    character_id: str = Path(..., description="The ID of the character"),
    skill_id: str = Path(..., description="The ID of the skill to retrieve")
):
    """
    Get a specific skill for a character.
    
    Args:
        character_id: The character ID
        skill_id: The skill ID
        
    Returns:
        The skill if found
        
    Raises:
        HTTPException: If the character or skill is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Check if the character exists in the database
    if character_id not in db:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Character not found"
        )
    
    # Check if the character has skills
    if "skills" not in db[character_id]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Skill not found"
        )
    
    # Check if the skill exists
    if skill_id not in db[character_id]["skills"]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Skill not found"
        )
    
    # Return the skill
    return db[character_id]["skills"][skill_id]

@router.put("/character/{character_id}/skill/{skill_id}", response_model=Skill, status_code=status.HTTP_200_OK)
async def update_character_skill(
    skill_update: SkillUpdate,
    character_id: str = Path(..., description="The ID of the character"),
    skill_id: str = Path(..., description="The ID of the skill to update")
):
    """
    Update a skill for a character.
    
    Args:
        skill_update: The updated skill data
        character_id: The character ID
        skill_id: The skill ID
        
    Returns:
        The updated skill
        
    Raises:
        HTTPException: If the character or skill is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Check if the character exists in the database
    if character_id not in db:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Character not found"
        )
    
    # Check if the character has skills
    if "skills" not in db[character_id]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Skill not found"
        )
    
    # Check if the skill exists
    if skill_id not in db[character_id]["skills"]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Skill not found"
        )
    
    # Update the skill
    updated_skill = Skill(
        skill_id=skill_id,
        **skill_update.dict()
    )
    
    # Save to database
    db[character_id]["skills"][skill_id] = updated_skill.dict()
    
    # Return the updated skill
    return updated_skill
