# Lesson 14: Character Skills

This directory contains materials for Lesson 14 of the TDD course, which focuses on implementing character skills functionality.

## Contents

- `README.md`: This file
- `test_character_skills.py`: Tests for character skills
- `models/character.py`: Updated character model with skills
- `models/skill.py`: Skill model definition
- `api/endpoints/skill_management.py`: Endpoint implementation for skill management

## Learning Objectives

By the end of this lesson, you should be able to:
- Implement additional related entities in a REST API
- Write tests for nested resources with multiple levels
- Design and implement models for complex relationships
- Handle validation for related entities
- Follow the RED-GREEN-REFACTOR cycle for complex data structures
