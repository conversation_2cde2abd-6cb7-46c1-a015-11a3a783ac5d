"""
Tests for character skills.
This demonstrates the RED phase of the TDD cycle.
"""
import pytest
from fastapi.testclient import TestClient

from rpg_character_db.main import app
from rpg_character_db.db import db

client = TestClient(app)

def test_add_skill_to_character_returns_201():
    """Test that adding a skill to a character returns a 201 status code."""
    # Arrange
    character_id = "CHAR:TEST123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human",
        "skills": {}
    }
    
    skill_data = {
        "name": "Swordsmanship",
        "level": 3,
        "description": "Ability to fight with swords"
    }
    
    # Act
    response = client.post(f"/character/{character_id}/skill", json=skill_data)
    
    # Assert
    assert response.status_code == 201

def test_add_skill_to_character_returns_skill():
    """Test that adding a skill to a character returns the skill data."""
    # Arrange
    character_id = "CHAR:TEST123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human",
        "skills": {}
    }
    
    skill_data = {
        "name": "Swordsmanship",
        "level": 3,
        "description": "Ability to fight with swords"
    }
    
    # Act
    response = client.post(f"/character/{character_id}/skill", json=skill_data)
    
    # Assert
    skill = response.json()
    assert "skill_id" in skill
    assert skill["name"] == skill_data["name"]
    assert skill["level"] == skill_data["level"]
    assert skill["description"] == skill_data["description"]

def test_add_skill_to_character_adds_to_database():
    """Test that adding a skill to a character adds it to the database."""
    # Arrange
    character_id = "CHAR:TEST123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human",
        "skills": {}
    }
    
    skill_data = {
        "name": "Swordsmanship",
        "level": 3,
        "description": "Ability to fight with swords"
    }
    
    # Act
    response = client.post(f"/character/{character_id}/skill", json=skill_data)
    
    # Assert
    skill = response.json()
    assert "skills" in db[character_id]
    assert skill["skill_id"] in db[character_id]["skills"]

def test_get_character_skills_returns_200():
    """Test that getting a character's skills returns a 200 status code."""
    # Arrange
    character_id = "CHAR:TEST123"
    skill_id = "SKILL:SWD123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human",
        "skills": {
            skill_id: {
                "skill_id": skill_id,
                "name": "Swordsmanship",
                "level": 3,
                "description": "Ability to fight with swords"
            }
        }
    }
    
    # Act
    response = client.get(f"/character/{character_id}/skills")
    
    # Assert
    assert response.status_code == 200

def test_get_character_skills_returns_skills_list():
    """Test that getting a character's skills returns a list of skills."""
    # Arrange
    character_id = "CHAR:TEST123"
    skill_id = "SKILL:SWD123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human",
        "skills": {
            skill_id: {
                "skill_id": skill_id,
                "name": "Swordsmanship",
                "level": 3,
                "description": "Ability to fight with swords"
            }
        }
    }
    
    # Act
    response = client.get(f"/character/{character_id}/skills")
    
    # Assert
    skills = response.json()
    assert isinstance(skills, list)
    assert len(skills) == 1
    assert skills[0]["skill_id"] == skill_id
    assert skills[0]["name"] == "Swordsmanship"
    assert skills[0]["level"] == 3
    assert skills[0]["description"] == "Ability to fight with swords"

def test_get_character_skill_by_id_returns_200():
    """Test that getting a specific skill returns a 200 status code."""
    # Arrange
    character_id = "CHAR:TEST123"
    skill_id = "SKILL:SWD123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human",
        "skills": {
            skill_id: {
                "skill_id": skill_id,
                "name": "Swordsmanship",
                "level": 3,
                "description": "Ability to fight with swords"
            }
        }
    }
    
    # Act
    response = client.get(f"/character/{character_id}/skill/{skill_id}")
    
    # Assert
    assert response.status_code == 200

def test_get_character_skill_by_id_returns_skill():
    """Test that getting a specific skill returns the skill data."""
    # Arrange
    character_id = "CHAR:TEST123"
    skill_id = "SKILL:SWD123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human",
        "skills": {
            skill_id: {
                "skill_id": skill_id,
                "name": "Swordsmanship",
                "level": 3,
                "description": "Ability to fight with swords"
            }
        }
    }
    
    # Act
    response = client.get(f"/character/{character_id}/skill/{skill_id}")
    
    # Assert
    skill = response.json()
    assert skill["skill_id"] == skill_id
    assert skill["name"] == "Swordsmanship"
    assert skill["level"] == 3
    assert skill["description"] == "Ability to fight with swords"

def test_update_character_skill_returns_200():
    """Test that updating a skill returns a 200 status code."""
    # Arrange
    character_id = "CHAR:TEST123"
    skill_id = "SKILL:SWD123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human",
        "skills": {
            skill_id: {
                "skill_id": skill_id,
                "name": "Swordsmanship",
                "level": 3,
                "description": "Ability to fight with swords"
            }
        }
    }
    
    update_data = {
        "name": "Advanced Swordsmanship",
        "level": 5,
        "description": "Advanced ability to fight with swords"
    }
    
    # Act
    response = client.put(f"/character/{character_id}/skill/{skill_id}", json=update_data)
    
    # Assert
    assert response.status_code == 200
