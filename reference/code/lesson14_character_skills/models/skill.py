"""
Skill model definition.
This defines the structure of character skills in our API.
"""
from pydantic import BaseModel, Field
import uuid
from typing import Optional

class SkillBase(BaseModel):
    """Base model for skill data."""
    name: str = Field(..., description="Skill name (e.g., Swordsmanship, Fireball)")
    level: int = Field(..., description="Skill level", ge=1, le=10)
    description: str = Field(..., description="Skill description")

class SkillCreate(SkillBase):
    """Model for creating a new skill."""
    pass

class SkillUpdate(SkillBase):
    """Model for updating a skill."""
    pass

class Skill(SkillBase):
    """Model representing a character skill."""
    skill_id: str = Field(..., description="Unique identifier for the skill")
    
    @classmethod
    def create(cls, skill_create: SkillCreate) -> "Skill":
        """Create a new skill from a SkillCreate model."""
        return cls(
            skill_id=f"SKILL:{uuid.uuid4().hex[:6]}",
            **skill_create.dict()
        )
