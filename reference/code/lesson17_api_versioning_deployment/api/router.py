"""
Router with version prefixes.
This demonstrates the GREEN phase of the TDD cycle.
"""
from fastapi import APIRouter

from rpg_character_db.api.endpoints import character_endpoints, skill_endpoints

# Create API router
api_router = APIRouter()

# Include versioned endpoints
api_router.include_router(
    character_endpoints.router,
    prefix="/v1",
    tags=["v1"]
)

# Include v2 endpoints (could have different implementations)
api_router.include_router(
    character_endpoints.router,
    prefix="/v2",
    tags=["v2"]
)

# Include skill endpoints only in v2
api_router.include_router(
    skill_endpoints.router,
    prefix="/v2",
    tags=["v2"]
)
