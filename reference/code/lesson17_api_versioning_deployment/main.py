"""
Updated main application with versioning.
This demonstrates the GREEN phase of the TDD cycle.
"""
from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware

from rpg_character_db.api.router import api_router
from rpg_character_db.dependencies.settings import get_settings, Settings
from rpg_character_db.models.database import create_tables

# Create FastAPI application with metadata
app = FastAPI(
    title="RPG Character Database API",
    description="A RESTful API for managing RPG character data",
    version="1.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root(settings: Settings = Depends(get_settings)):
    """
    Get a welcome message with API version.
    
    Returns:
        A welcome message with API version
    """
    return {
        "message": "Welcome to the RPG Character Database API",
        "version": settings.api_version,
        "documentation": "/docs"
    }

# Include the API router with version prefix
app.include_router(api_router)

# Create database tables on startup
@app.on_event("startup")
async def startup_event():
    """Create database tables on startup."""
    create_tables()
