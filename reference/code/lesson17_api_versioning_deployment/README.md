# Lesson 17: API Versioning and Deployment

This directory contains materials for Lesson 17 of the TDD course, which focuses on implementing API versioning and preparing for deployment.

## Contents

- `README.md`: This file
- `main.py`: Updated main application with versioning
- `api/router.py`: Router with version prefixes
- `deployment/docker-compose.yml`: Docker Compose configuration
- `deployment/Dockerfile`: Dockerfile for containerization
- `deployment/README.md`: Deployment instructions

## Learning Objectives

By the end of this lesson, you should be able to:
- Understand API versioning strategies
- Implement versioning in FastAPI
- Prepare an application for deployment
- Create Docker containers for your API
- Follow the RED-GREEN-REFACTOR cycle when implementing deployment features
