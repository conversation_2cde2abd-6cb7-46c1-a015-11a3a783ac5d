# Deployment Instructions

This document provides instructions for deploying the RPG Character Database API.

## Prerequisites

- Docker and Docker Compose installed
- Git repository cloned

## Local Development Deployment

1. Navigate to the project root directory:
   ```
   cd /path/to/rpg_character_db
   ```

2. Build and start the Docker containers:
   ```
   docker-compose -f deployment/docker-compose.yml up -d
   ```

3. Access the API at http://localhost:8000

4. Access the API documentation at http://localhost:8000/docs

5. Stop the containers when finished:
   ```
   docker-compose -f deployment/docker-compose.yml down
   ```

## Production Deployment

For production deployment, follow these additional steps:

1. Update the `docker-compose.yml` file:
   - Remove the volume mount
   - Change the command to remove `--reload`
   - Set appropriate environment variables

2. Create a `.env` file with production settings:
   ```
   RPG_REPOSITORY_TYPE=sql
   RPG_DEBUG=false
   RPG_API_VERSION=v1
   ```

3. Build the production image:
   ```
   docker build -t rpg-character-db:prod -f deployment/Dockerfile .
   ```

4. Deploy to your production environment using Docker Compose or Kubernetes.

## API Versioning

The API supports versioning through URL prefixes:

- `/v1/` - Version 1 endpoints
- `/v2/` - Version 2 endpoints (includes additional features)

Clients should specify the version in the URL to ensure compatibility.
