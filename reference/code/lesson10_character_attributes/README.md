# Lesson 10: Character Attributes

This directory contains materials for Lesson 10 of the TDD course, which focuses on implementing character attributes functionality.

## Contents

- `README.md`: This file
- `test_character_attributes.py`: Tests for character attributes
- `models/character.py`: Updated character model with attributes
- `models/attribute.py`: Attribute model definition
- `api/endpoints/attribute_management.py`: Endpoint implementation for attribute management

## Learning Objectives

By the end of this lesson, you should be able to:
- Implement relationships between entities in a REST API
- Write tests for nested resources
- Design and implement models for related entities
- Handle validation for related entities
- Follow the RED-GREEN-REFACTOR cycle for complex data structures
