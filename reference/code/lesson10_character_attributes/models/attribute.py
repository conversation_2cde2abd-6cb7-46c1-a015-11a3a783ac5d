"""
Attribute model definition.
This defines the structure of character attributes in our API.
"""
from pydantic import BaseModel, Field
import uuid
from typing import Optional

class AttributeBase(BaseModel):
    """Base model for attribute data."""
    name: str = Field(..., description="Attribute name (e.g., Strength, Dexterity)")
    value: int = Field(..., description="Attribute value", ge=1, le=20)
    modifier: int = Field(..., description="Attribute modifier")

class AttributeCreate(AttributeBase):
    """Model for creating a new attribute."""
    pass

class AttributeUpdate(AttributeBase):
    """Model for updating an attribute."""
    pass

class Attribute(AttributeBase):
    """Model representing a character attribute."""
    attribute_id: str = Field(..., description="Unique identifier for the attribute")
    
    @classmethod
    def create(cls, attribute_create: AttributeCreate) -> "Attribute":
        """Create a new attribute from an AttributeCreate model."""
        return cls(
            attribute_id=f"ATTR:{uuid.uuid4().hex[:6]}",
            **attribute_create.dict()
        )
