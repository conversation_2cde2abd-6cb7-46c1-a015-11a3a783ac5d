"""
Endpoint implementation for attribute management.
This demonstrates the GREEN phase of the TDD cycle.
"""
from fastapi import APIRouter, HTTPException, Path, status
from typing import List

from rpg_character_db.models.attribute import Attribute, AttributeCreate
from rpg_character_db.db import db
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter(tags=["attributes"])

@router.post("/character/{character_id}/attribute", response_model=Attribute, status_code=status.HTTP_201_CREATED)
async def add_attribute_to_character(
    attribute_create: AttributeCreate,
    character_id: str = Path(..., description="The ID of the character to add the attribute to")
):
    """
    Add an attribute to a character.
    
    Args:
        attribute_create: The attribute data
        character_id: The character ID
        
    Returns:
        The created attribute
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Check if the character exists in the database
    if character_id not in db:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Character not found"
        )
    
    # Create a new attribute
    attribute = Attribute.create(attribute_create)
    
    # Initialize attributes dictionary if it doesn't exist
    if "attributes" not in db[character_id]:
        db[character_id]["attributes"] = {}
    
    # Add attribute to character
    db[character_id]["attributes"][attribute.attribute_id] = attribute.dict()
    
    return attribute

@router.get("/character/{character_id}/attributes", response_model=List[Attribute], status_code=status.HTTP_200_OK)
async def get_character_attributes(
    character_id: str = Path(..., description="The ID of the character to get attributes for")
):
    """
    Get all attributes for a character.
    
    Args:
        character_id: The character ID
        
    Returns:
        A list of all attributes for the character
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Check if the character exists in the database
    if character_id not in db:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Character not found"
        )
    
    # Get attributes from character
    attributes = db[character_id].get("attributes", {})
    
    # Return attributes as a list
    return list(attributes.values())

@router.get("/character/{character_id}/attribute/{attribute_id}", response_model=Attribute, status_code=status.HTTP_200_OK)
async def get_character_attribute_by_id(
    character_id: str = Path(..., description="The ID of the character"),
    attribute_id: str = Path(..., description="The ID of the attribute to retrieve")
):
    """
    Get a specific attribute for a character.
    
    Args:
        character_id: The character ID
        attribute_id: The attribute ID
        
    Returns:
        The attribute if found
        
    Raises:
        HTTPException: If the character or attribute is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Check if the character exists in the database
    if character_id not in db:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Character not found"
        )
    
    # Check if the character has attributes
    if "attributes" not in db[character_id]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Attribute not found"
        )
    
    # Check if the attribute exists
    if attribute_id not in db[character_id]["attributes"]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Attribute not found"
        )
    
    # Return the attribute
    return db[character_id]["attributes"][attribute_id]
