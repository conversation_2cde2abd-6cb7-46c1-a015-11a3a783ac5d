"""
Tests for character attributes.
This demonstrates the RED phase of the TDD cycle.
"""
import pytest
from fastapi.testclient import TestClient

from rpg_character_db.main import app
from rpg_character_db.db import db

client = TestClient(app)

def test_add_attribute_to_character_returns_201():
    """Test that adding an attribute to a character returns a 201 status code."""
    # Arrange
    character_id = "CHAR:TEST123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human",
        "attributes": {}
    }
    
    attribute_data = {
        "name": "Strength",
        "value": 16,
        "modifier": 3
    }
    
    # Act
    response = client.post(f"/character/{character_id}/attribute", json=attribute_data)
    
    # Assert
    assert response.status_code == 201

def test_add_attribute_to_character_returns_attribute():
    """Test that adding an attribute to a character returns the attribute data."""
    # Arrange
    character_id = "CHAR:TEST123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human",
        "attributes": {}
    }
    
    attribute_data = {
        "name": "Strength",
        "value": 16,
        "modifier": 3
    }
    
    # Act
    response = client.post(f"/character/{character_id}/attribute", json=attribute_data)
    
    # Assert
    attribute = response.json()
    assert "attribute_id" in attribute
    assert attribute["name"] == attribute_data["name"]
    assert attribute["value"] == attribute_data["value"]
    assert attribute["modifier"] == attribute_data["modifier"]

def test_add_attribute_to_character_adds_to_database():
    """Test that adding an attribute to a character adds it to the database."""
    # Arrange
    character_id = "CHAR:TEST123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human",
        "attributes": {}
    }
    
    attribute_data = {
        "name": "Strength",
        "value": 16,
        "modifier": 3
    }
    
    # Act
    response = client.post(f"/character/{character_id}/attribute", json=attribute_data)
    
    # Assert
    attribute = response.json()
    assert "attributes" in db[character_id]
    assert attribute["attribute_id"] in db[character_id]["attributes"]

def test_get_character_attributes_returns_200():
    """Test that getting a character's attributes returns a 200 status code."""
    # Arrange
    character_id = "CHAR:TEST123"
    attribute_id = "ATTR:STR123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human",
        "attributes": {
            attribute_id: {
                "attribute_id": attribute_id,
                "name": "Strength",
                "value": 16,
                "modifier": 3
            }
        }
    }
    
    # Act
    response = client.get(f"/character/{character_id}/attributes")
    
    # Assert
    assert response.status_code == 200

def test_get_character_attributes_returns_attributes_list():
    """Test that getting a character's attributes returns a list of attributes."""
    # Arrange
    character_id = "CHAR:TEST123"
    attribute_id = "ATTR:STR123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human",
        "attributes": {
            attribute_id: {
                "attribute_id": attribute_id,
                "name": "Strength",
                "value": 16,
                "modifier": 3
            }
        }
    }
    
    # Act
    response = client.get(f"/character/{character_id}/attributes")
    
    # Assert
    attributes = response.json()
    assert isinstance(attributes, list)
    assert len(attributes) == 1
    assert attributes[0]["attribute_id"] == attribute_id
    assert attributes[0]["name"] == "Strength"
    assert attributes[0]["value"] == 16
    assert attributes[0]["modifier"] == 3

def test_get_character_attribute_by_id_returns_200():
    """Test that getting a specific attribute returns a 200 status code."""
    # Arrange
    character_id = "CHAR:TEST123"
    attribute_id = "ATTR:STR123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human",
        "attributes": {
            attribute_id: {
                "attribute_id": attribute_id,
                "name": "Strength",
                "value": 16,
                "modifier": 3
            }
        }
    }
    
    # Act
    response = client.get(f"/character/{character_id}/attribute/{attribute_id}")
    
    # Assert
    assert response.status_code == 200

def test_get_character_attribute_by_id_returns_attribute():
    """Test that getting a specific attribute returns the attribute data."""
    # Arrange
    character_id = "CHAR:TEST123"
    attribute_id = "ATTR:STR123"
    db[character_id] = {
        "character_id": character_id,
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 5,
        "race": "Human",
        "attributes": {
            attribute_id: {
                "attribute_id": attribute_id,
                "name": "Strength",
                "value": 16,
                "modifier": 3
            }
        }
    }
    
    # Act
    response = client.get(f"/character/{character_id}/attribute/{attribute_id}")
    
    # Assert
    attribute = response.json()
    assert attribute["attribute_id"] == attribute_id
    assert attribute["name"] == "Strength"
    assert attribute["value"] == 16
    assert attribute["modifier"] == 3
