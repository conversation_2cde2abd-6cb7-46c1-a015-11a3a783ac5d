"""
Enhanced dependency injection for repositories.
This provides functions for getting repository instances with more flexibility.
"""
from typing import Callable, Dict, Optional
from functools import lru_cache

from fastapi import Depends

from rpg_character_db.repositories.interfaces.character_repository import CharacterRepository
from rpg_character_db.repositories.implementations.in_memory_character_repository import InMemoryCharacterRepository
from rpg_character_db.dependencies.settings import get_settings, Settings

# Repository factory type
RepositoryFactory = Callable[[], CharacterRepository]

# Repository factories dictionary
repository_factories: Dict[str, RepositoryFactory] = {
    "in_memory": lambda: InMemoryCharacterRepository(),
    # Other repository implementations can be added here
}

@lru_cache()
def get_repository_factory(repo_type: str) -> RepositoryFactory:
    """
    Get a repository factory by type.
    
    Args:
        repo_type: The repository type
        
    Returns:
        A repository factory function
    
    Raises:
        ValueError: If the repository type is not supported
    """
    if repo_type not in repository_factories:
        raise ValueError(f"Unsupported repository type: {repo_type}")
    
    return repository_factories[repo_type]

def get_character_repository(
    settings: Settings = Depends(get_settings)
) -> CharacterRepository:
    """
    Get a character repository instance based on settings.
    
    Args:
        settings: The application settings
        
    Returns:
        A character repository instance
    """
    # Get the repository factory
    factory = get_repository_factory(settings.repository_type)
    
    # Create and return the repository
    return factory()

# For testing purposes, we can store a singleton instance
_test_character_repository: Optional[CharacterRepository] = None

def get_test_character_repository() -> CharacterRepository:
    """
    Get a test character repository instance.
    This is used for testing to ensure the same repository instance is used.
    
    Returns:
        A character repository instance
    """
    global _test_character_repository
    
    if _test_character_repository is None:
        _test_character_repository = InMemoryCharacterRepository()
    
    return _test_character_repository
