"""
Application settings dependency.
This provides functions for getting application settings.
"""
from pydantic import BaseSettings
from functools import lru_cache

class Settings(BaseSettings):
    """Application settings."""
    repository_type: str = "in_memory"
    debug: bool = False
    api_version: str = "v1"
    
    class Config:
        env_prefix = "RPG_"  # Environment variables prefix

@lru_cache()
def get_settings() -> Settings:
    """
    Get application settings.
    This is cached to avoid loading settings multiple times.
    
    Returns:
        Application settings
    """
    return Settings()
