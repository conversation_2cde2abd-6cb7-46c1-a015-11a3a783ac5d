"""
Endpoints using enhanced dependency injection.
This demonstrates the GREEN phase of the TDD cycle.
"""
from fastapi import APIRouter, HTTPException, Path, Depends, status
from typing import List

from rpg_character_db.models.character import Character, CharacterCreate, CharacterUpdate
from rpg_character_db.repositories.interfaces.character_repository import CharacterRepository
from rpg_character_db.dependencies.repositories import get_character_repository
from rpg_character_db.dependencies.settings import get_settings, Settings
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter(tags=["characters"])

@router.get("/character/{character_id}", response_model=Character, status_code=status.HTTP_200_OK)
async def get_character_by_id(
    character_id: str = Path(..., description="The ID of the character to retrieve"),
    character_repository: CharacterRepository = Depends(get_character_repository),
    settings: Settings = Depends(get_settings)
):
    """
    Get a character by ID.
    
    Args:
        character_id: The character ID
        character_repository: The character repository
        settings: Application settings
        
    Returns:
        The character if found
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character from the repository
    character = await character_repository.get_character_by_id(character_id)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Character not found"
        )
    
    # Return the character
    return character

@router.get("/characters", response_model=List[Character], status_code=status.HTTP_200_OK)
async def list_characters(
    character_repository: CharacterRepository = Depends(get_character_repository),
    settings: Settings = Depends(get_settings)
):
    """
    Get a list of all characters.
    
    Args:
        character_repository: The character repository
        settings: Application settings
        
    Returns:
        A list of all characters
    """
    # Get all characters from the repository
    characters = await character_repository.list_characters()
    
    # Return the characters
    return characters

@router.post("/character", response_model=Character, status_code=status.HTTP_201_CREATED)
async def create_character(
    character_create: CharacterCreate,
    character_repository: CharacterRepository = Depends(get_character_repository),
    settings: Settings = Depends(get_settings)
):
    """
    Create a new character.
    
    Args:
        character_create: The character data
        character_repository: The character repository
        settings: Application settings
        
    Returns:
        The created character
    """
    # Create a new character in the repository
    character = await character_repository.create_character(character_create)
    
    # Return the created character
    return character

@router.put("/character/{character_id}", response_model=Character, status_code=status.HTTP_200_OK)
async def update_character(
    character_update: CharacterUpdate,
    character_id: str = Path(..., description="The ID of the character to update"),
    character_repository: CharacterRepository = Depends(get_character_repository),
    settings: Settings = Depends(get_settings)
):
    """
    Update a character.
    
    Args:
        character_update: The updated character data
        character_id: The character ID
        character_repository: The character repository
        settings: Application settings
        
    Returns:
        The updated character
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Update the character in the repository
    character = await character_repository.update_character(character_id, character_update)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Character not found"
        )
    
    # Return the updated character
    return character

@router.delete("/character/{character_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_character(
    character_id: str = Path(..., description="The ID of the character to delete"),
    character_repository: CharacterRepository = Depends(get_character_repository),
    settings: Settings = Depends(get_settings)
):
    """
    Delete a character.
    
    Args:
        character_id: The character ID
        character_repository: The character repository
        settings: Application settings
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Delete the character from the repository
    result = await character_repository.delete_character(character_id)
    
    # Check if the character exists
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Character not found"
        )
    
    # Return no content
    return None
