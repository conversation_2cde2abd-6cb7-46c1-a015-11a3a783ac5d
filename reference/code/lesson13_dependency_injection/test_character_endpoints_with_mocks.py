"""
Tests using dependency overrides.
This demonstrates the RED phase of the TDD cycle with dependency injection.
"""
import pytest
from unittest.mock import AsyncMock, patch
from fastapi.testclient import TestClient
from fastapi import status

from rpg_character_db.main import app
from rpg_character_db.models.character import Character, CharacterCreate, CharacterUpdate
from rpg_character_db.repositories.interfaces.character_repository import CharacterRepository
from rpg_character_db.dependencies.settings import Settings

# Create a test client
client = TestClient(app)

class TestSettings(Settings):
    """Test settings for dependency override."""
    repository_type: str = "test"
    debug: bool = True
    api_version: str = "test"

@pytest.fixture
def mock_character_repository():
    """Create a mock character repository and override the dependency."""
    # Create a mock repository
    mock_repo = AsyncMock(spec=CharacterRepository)
    
    # Override the dependency
    def get_test_repo():
        return mock_repo
    
    # Apply the override
    app.dependency_overrides[get_settings] = lambda: TestSettings()
    app.dependency_overrides[get_character_repository] = get_test_repo
    
    yield mock_repo
    
    # Clean up
    app.dependency_overrides = {}

def test_get_character_by_id_returns_200(mock_character_repository):
    """Test that getting an existing character returns a 200 status code."""
    # Arrange
    character_id = "CHAR:TEST123"
    mock_character = Character(
        character_id=character_id,
        name="Test Character",
        class_type="Warrior",
        level=5,
        race="Human"
    )
    mock_character_repository.get_character_by_id.return_value = mock_character
    
    # Act
    response = client.get(f"/character/{character_id}")
    
    # Assert
    assert response.status_code == status.HTTP_200_OK
    mock_character_repository.get_character_by_id.assert_called_once_with(character_id)

def test_get_character_by_id_returns_character(mock_character_repository):
    """Test that getting an existing character returns the character data."""
    # Arrange
    character_id = "CHAR:TEST123"
    mock_character = Character(
        character_id=character_id,
        name="Test Character",
        class_type="Warrior",
        level=5,
        race="Human"
    )
    mock_character_repository.get_character_by_id.return_value = mock_character
    
    # Act
    response = client.get(f"/character/{character_id}")
    
    # Assert
    character = response.json()
    assert character["character_id"] == character_id
    assert character["name"] == mock_character.name
    assert character["class_type"] == mock_character.class_type
    assert character["level"] == mock_character.level
    assert character["race"] == mock_character.race

def test_get_character_by_id_returns_404(mock_character_repository):
    """Test that getting a non-existent character returns a 404 status code."""
    # Arrange
    character_id = "CHAR:NON_EXISTENT"
    mock_character_repository.get_character_by_id.return_value = None
    
    # Act
    response = client.get(f"/character/{character_id}")
    
    # Assert
    assert response.status_code == status.HTTP_404_NOT_FOUND
    mock_character_repository.get_character_by_id.assert_called_once_with(character_id)

def test_list_characters_returns_200(mock_character_repository):
    """Test that listing characters returns a 200 status code."""
    # Arrange
    mock_characters = [
        Character(
            character_id="CHAR:TEST123",
            name="Test Character",
            class_type="Warrior",
            level=5,
            race="Human"
        ),
        Character(
            character_id="CHAR:TEST456",
            name="Another Character",
            class_type="Mage",
            level=3,
            race="Elf"
        )
    ]
    mock_character_repository.list_characters.return_value = mock_characters
    
    # Act
    response = client.get("/characters")
    
    # Assert
    assert response.status_code == status.HTTP_200_OK
    mock_character_repository.list_characters.assert_called_once()

def test_create_character_returns_201(mock_character_repository):
    """Test that creating a character returns a 201 status code."""
    # Arrange
    character_data = {
        "name": "New Character",
        "class_type": "Mage",
        "level": 1,
        "race": "Elf"
    }
    mock_character = Character(
        character_id="CHAR:NEW123",
        **character_data
    )
    mock_character_repository.create_character.return_value = mock_character
    
    # Act
    response = client.post("/character", json=character_data)
    
    # Assert
    assert response.status_code == status.HTTP_201_CREATED
    mock_character_repository.create_character.assert_called_once()

def test_update_character_returns_200(mock_character_repository):
    """Test that updating a character returns a 200 status code."""
    # Arrange
    character_id = "CHAR:TEST123"
    update_data = {
        "name": "Updated Character",
        "class_type": "Paladin",
        "level": 6,
        "race": "Human"
    }
    mock_character = Character(
        character_id=character_id,
        **update_data
    )
    mock_character_repository.update_character.return_value = mock_character
    
    # Act
    response = client.put(f"/character/{character_id}", json=update_data)
    
    # Assert
    assert response.status_code == status.HTTP_200_OK
    mock_character_repository.update_character.assert_called_once()

def test_delete_character_returns_204(mock_character_repository):
    """Test that deleting a character returns a 204 status code."""
    # Arrange
    character_id = "CHAR:TEST123"
    mock_character_repository.delete_character.return_value = True
    
    # Act
    response = client.delete(f"/character/{character_id}")
    
    # Assert
    assert response.status_code == status.HTTP_204_NO_CONTENT
    mock_character_repository.delete_character.assert_called_once_with(character_id)
