# Lesson 13: Dependency Injection

This directory contains materials for Lesson 13 of the TDD course, which focuses on implementing dependency injection for better testability and flexibility.

## Contents

- `README.md`: This file
- `dependencies/repositories.py`: Enhanced dependency injection for repositories
- `dependencies/settings.py`: Application settings dependency
- `api/endpoints/character_endpoints.py`: Endpoints using enhanced dependency injection
- `test_character_endpoints_with_mocks.py`: Tests using dependency overrides

## Learning Objectives

By the end of this lesson, you should be able to:
- Understand dependency injection principles
- Implement dependency injection in FastAPI
- Override dependencies in tests
- Create flexible and testable components
- Follow the RED-GREEN-REFACTOR cycle when implementing dependency injection
