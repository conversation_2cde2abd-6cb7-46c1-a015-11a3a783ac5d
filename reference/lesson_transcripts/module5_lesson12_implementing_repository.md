# Module 5, Lesson 12: Implementing Character Repository

## Introduction

Welcome back to our TDD course! In our previous lesson, we discussed the Repository Pattern and its benefits for building maintainable and testable applications. Today, we'll put that knowledge into practice by refactoring our application to better leverage the repository pattern.

We'll focus on implementing a proper character repository with interfaces and dependency injection. As always, we'll follow the RED-GREEN-REFACTOR cycle, making sure our tests continue to pass as we refactor our code.

## Creating Repository Interfaces

Let's start by creating interfaces for our repositories. This will define the contract that all implementations must follow:

```bash
# Create a directory for interfaces if it doesn't exist
mkdir -p rpg_character_db/repositories/interfaces
touch rpg_character_db/repositories/interfaces/__init__.py
touch rpg_character_db/repositories/interfaces/character_repository.py
```

Now, let's define the interface for our character repository:

```python
# rpg_character_db/repositories/interfaces/character_repository.py
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any

from rpg_character_db.models.character import Character, CharacterCreate

class ICharacterRepository(ABC):
    """Interface for character repository."""
    
    @abstractmethod
    def get(self, character_id: str) -> Optional[Character]:
        """
        Get a character by ID.
        
        Args:
            character_id: The character ID
            
        Returns:
            The character if found, None otherwise
        """
        pass
    
    @abstractmethod
    def create(self, character_create: CharacterCreate) -> Character:
        """
        Create a new character.
        
        Args:
            character_create: The character data
            
        Returns:
            The created character
        """
        pass
    
    @abstractmethod
    def update(self, character_id: str, character_update: Dict[str, Any]) -> Optional[Character]:
        """
        Update a character.
        
        Args:
            character_id: The character ID
            character_update: The character data to update
            
        Returns:
            The updated character if found, None otherwise
        """
        pass
    
    @abstractmethod
    def partial_update(self, character_id: str, character_update: Dict[str, Any]) -> Optional[Character]:
        """
        Partially update a character.
        
        Args:
            character_id: The character ID
            character_update: The character data to update
            
        Returns:
            The updated character if found, None otherwise
        """
        pass
    
    @abstractmethod
    def list(self) -> List[Character]:
        """
        List all characters.
        
        Returns:
            A list of all characters
        """
        pass
    
    @abstractmethod
    def delete(self, character_id: str) -> bool:
        """
        Delete a character.
        
        Args:
            character_id: The character ID
            
        Returns:
            True if the character was deleted, False otherwise
        """
        pass
```

## Writing Tests for the Repository Implementation

Before we implement the repository, let's write tests for it. This follows our TDD approach of writing tests first:

```bash
# Create a test file for the character repository
touch tests/test_character_repository.py
```

```python
# tests/test_character_repository.py
import pytest
from typing import Dict, Any

from rpg_character_db.models.character import Character, CharacterCreate
from rpg_character_db.repositories.implementations.character_repository import InMemoryCharacterRepository

@pytest.fixture
def mock_db():
    """Create a mock database."""
    return {}

@pytest.fixture
def repository(mock_db):
    """Create a repository with a mock database."""
    return InMemoryCharacterRepository(mock_db)

def test_create_character(repository, mock_db):
    """Test creating a character."""
    # Create a character
    character_create = CharacterCreate(
        name="Test Character",
        class_type="Warrior",
        level=1,
        race="Human"
    )
    character = repository.create(character_create)
    
    # Check that the character was created correctly
    assert character.name == "Test Character"
    assert character.class_type == "Warrior"
    assert character.level == 1
    assert character.race == "Human"
    assert character.character_id.startswith("CHAR:")
    
    # Check that the character was added to the database
    assert character.character_id in mock_db
    assert mock_db[character.character_id]["name"] == "Test Character"

def test_get_character(repository, mock_db):
    """Test getting a character."""
    # Add a character to the database
    character_data = {
        "character_id": "CHAR:TEST",
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 1,
        "race": "Human"
    }
    mock_db["CHAR:TEST"] = character_data
    
    # Get the character
    character = repository.get("CHAR:TEST")
    
    # Check that the character was retrieved correctly
    assert character is not None
    assert character.character_id == "CHAR:TEST"
    assert character.name == "Test Character"
    assert character.class_type == "Warrior"
    assert character.level == 1
    assert character.race == "Human"

def test_get_non_existent_character(repository):
    """Test getting a non-existent character."""
    character = repository.get("CHAR:NON_EXISTENT")
    assert character is None

def test_update_character(repository, mock_db):
    """Test updating a character."""
    # Add a character to the database
    character_data = {
        "character_id": "CHAR:TEST",
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 1,
        "race": "Human"
    }
    mock_db["CHAR:TEST"] = character_data
    
    # Update the character
    update_data = {
        "name": "Updated Character",
        "class_type": "Mage",
        "level": 2,
        "race": "Elf"
    }
    updated_character = repository.update("CHAR:TEST", update_data)
    
    # Check that the character was updated correctly
    assert updated_character is not None
    assert updated_character.character_id == "CHAR:TEST"
    assert updated_character.name == "Updated Character"
    assert updated_character.class_type == "Mage"
    assert updated_character.level == 2
    assert updated_character.race == "Elf"
    
    # Check that the database was updated
    assert mock_db["CHAR:TEST"]["name"] == "Updated Character"
    assert mock_db["CHAR:TEST"]["class_type"] == "Mage"
    assert mock_db["CHAR:TEST"]["level"] == 2
    assert mock_db["CHAR:TEST"]["race"] == "Elf"

def test_update_non_existent_character(repository):
    """Test updating a non-existent character."""
    update_data = {
        "name": "Updated Character",
        "class_type": "Mage",
        "level": 2,
        "race": "Elf"
    }
    updated_character = repository.update("CHAR:NON_EXISTENT", update_data)
    assert updated_character is None

def test_partial_update_character(repository, mock_db):
    """Test partially updating a character."""
    # Add a character to the database
    character_data = {
        "character_id": "CHAR:TEST",
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 1,
        "race": "Human"
    }
    mock_db["CHAR:TEST"] = character_data
    
    # Partially update the character
    update_data = {
        "name": "Updated Character",
        "level": 2
    }
    updated_character = repository.partial_update("CHAR:TEST", update_data)
    
    # Check that the character was updated correctly
    assert updated_character is not None
    assert updated_character.character_id == "CHAR:TEST"
    assert updated_character.name == "Updated Character"
    assert updated_character.class_type == "Warrior"  # Unchanged
    assert updated_character.level == 2
    assert updated_character.race == "Human"  # Unchanged
    
    # Check that the database was updated
    assert mock_db["CHAR:TEST"]["name"] == "Updated Character"
    assert mock_db["CHAR:TEST"]["class_type"] == "Warrior"
    assert mock_db["CHAR:TEST"]["level"] == 2
    assert mock_db["CHAR:TEST"]["race"] == "Human"

def test_partial_update_non_existent_character(repository):
    """Test partially updating a non-existent character."""
    update_data = {
        "name": "Updated Character",
        "level": 2
    }
    updated_character = repository.partial_update("CHAR:NON_EXISTENT", update_data)
    assert updated_character is None

def test_list_characters(repository, mock_db):
    """Test listing characters."""
    # Add characters to the database
    character_data1 = {
        "character_id": "CHAR:TEST1",
        "name": "Test Character 1",
        "class_type": "Warrior",
        "level": 1,
        "race": "Human"
    }
    character_data2 = {
        "character_id": "CHAR:TEST2",
        "name": "Test Character 2",
        "class_type": "Mage",
        "level": 2,
        "race": "Elf"
    }
    mock_db["CHAR:TEST1"] = character_data1
    mock_db["CHAR:TEST2"] = character_data2
    
    # Add a non-character entry to the database
    mock_db["NOT_A_CHARACTER"] = {"some": "data"}
    
    # List characters
    characters = repository.list()
    
    # Check that only characters were returned
    assert len(characters) == 2
    character_ids = [c.character_id for c in characters]
    assert "CHAR:TEST1" in character_ids
    assert "CHAR:TEST2" in character_ids

def test_delete_character(repository, mock_db):
    """Test deleting a character."""
    # Add a character to the database
    character_data = {
        "character_id": "CHAR:TEST",
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 1,
        "race": "Human"
    }
    mock_db["CHAR:TEST"] = character_data
    
    # Delete the character
    result = repository.delete("CHAR:TEST")
    
    # Check that the character was deleted
    assert result is True
    assert "CHAR:TEST" not in mock_db

def test_delete_non_existent_character(repository):
    """Test deleting a non-existent character."""
    result = repository.delete("CHAR:NON_EXISTENT")
    assert result is False
```

## Implementing the Repository

Now that we have tests, let's implement the repository:

```bash
# Create a directory for implementations
mkdir -p rpg_character_db/repositories/implementations
touch rpg_character_db/repositories/implementations/__init__.py
touch rpg_character_db/repositories/implementations/character_repository.py
```

```python
# rpg_character_db/repositories/implementations/character_repository.py
from typing import Dict, List, Optional, Any

from rpg_character_db.models.character import Character, CharacterCreate
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository

class InMemoryCharacterRepository(ICharacterRepository):
    """In-memory implementation of character repository."""
    
    def __init__(self, db: Dict[str, Dict[str, Any]]):
        """
        Initialize the repository.
        
        Args:
            db: The database to use
        """
        self.db = db
    
    def get(self, character_id: str) -> Optional[Character]:
        """
        Get a character by ID.
        
        Args:
            character_id: The character ID
            
        Returns:
            The character if found, None otherwise
        """
        if character_id not in self.db:
            return None
        
        return Character(**self.db[character_id])
    
    def create(self, character_create: CharacterCreate) -> Character:
        """
        Create a new character.
        
        Args:
            character_create: The character data
            
        Returns:
            The created character
        """
        character = Character.create(character_create)
        self.db[character.character_id] = character.dict()
        return character
    
    def update(self, character_id: str, character_update: Dict[str, Any]) -> Optional[Character]:
        """
        Update a character.
        
        Args:
            character_id: The character ID
            character_update: The character data to update
            
        Returns:
            The updated character if found, None otherwise
        """
        if character_id not in self.db:
            return None
        
        # Get the current character data
        character_data = self.db[character_id]
        
        # Update the character data
        character_data.update(character_update)
        
        # Store the updated character
        self.db[character_id] = character_data
        
        return Character(**character_data)
    
    def partial_update(self, character_id: str, character_update: Dict[str, Any]) -> Optional[Character]:
        """
        Partially update a character.
        
        Args:
            character_id: The character ID
            character_update: The character data to update
            
        Returns:
            The updated character if found, None otherwise
        """
        if character_id not in self.db:
            return None
        
        # Get the current character data
        character_data = self.db[character_id].copy()
        
        # Update only the specified fields
        for key, value in character_update.items():
            if key in character_data:
                character_data[key] = value
        
        # Store the updated character
        self.db[character_id] = character_data
        
        return Character(**character_data)
    
    def list(self) -> List[Character]:
        """
        List all characters.
        
        Returns:
            A list of all characters
        """
        return [
            Character(**data)
            for character_id, data in self.db.items()
            if character_id.startswith("CHAR:")
        ]
    
    def delete(self, character_id: str) -> bool:
        """
        Delete a character.
        
        Args:
            character_id: The character ID
            
        Returns:
            True if the character was deleted, False otherwise
        """
        if character_id not in self.db:
            return False
        
        del self.db[character_id]
        return True
```

## Running the Repository Tests (RED)

Let's run our tests to see if our implementation works:

```bash
pytest tests/test_character_repository.py -v
```

We might encounter some failures because we haven't set up the dependency injection yet. Let's fix that.

## Setting Up Dependency Injection

Now, let's set up dependency injection for our repositories:

```bash
# Create a file for dependencies
touch rpg_character_db/dependencies.py
```

```python
# rpg_character_db/dependencies.py
from fastapi import Depends

from rpg_character_db.db import db
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.repositories.implementations.character_repository import InMemoryCharacterRepository

def get_db():
    """Get the database."""
    return db

def get_character_repository(db=Depends(get_db)) -> ICharacterRepository:
    """Get the character repository."""
    return InMemoryCharacterRepository(db)
```

## Updating API Endpoints to Use the Repository

Now, let's update our API endpoints to use the repository through dependency injection:

```python
# rpg_character_db/api/endpoints/character_retrieval.py
from fastapi import APIRouter, Depends, HTTPException, Path

from rpg_character_db.models.character import Character
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.dependencies import get_character_repository
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter(tags=["characters"])

@router.get("/character/{character_id}", response_model=Character)
async def get_character_by_id(
    character_id: str = Path(..., description="The ID of the character to retrieve"),
    character_repository: ICharacterRepository = Depends(get_character_repository)
):
    """
    Get a character by ID.
    
    Args:
        character_id: The character ID
        character_repository: The character repository
        
    Returns:
        The character if found
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character from the repository
    character = character_repository.get(character_id)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return character
```

Let's update the other endpoints as well:

```python
# rpg_character_db/api/endpoints/character_creation.py
from fastapi import APIRouter, Depends, status

from rpg_character_db.models.character import Character, CharacterCreate
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.dependencies import get_character_repository

router = APIRouter(tags=["characters"])

@router.post("/character", response_model=Character, status_code=status.HTTP_201_CREATED)
async def create_character(
    character_create: CharacterCreate,
    character_repository: ICharacterRepository = Depends(get_character_repository)
):
    """
    Create a new character.
    
    Args:
        character_create: The character data
        character_repository: The character repository
        
    Returns:
        The created character
    """
    # Create a new character using the repository
    return character_repository.create(character_create)
```

```python
# rpg_character_db/api/endpoints/character_update.py
from fastapi import APIRouter, Depends, HTTPException, Path, Body

from rpg_character_db.models.character import Character, CharacterUpdate, CharacterPartialUpdate
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.dependencies import get_character_repository
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter(tags=["characters"])

@router.put("/character/{character_id}", response_model=Character)
async def update_character(
    character_id: str = Path(..., description="The ID of the character to update"),
    character_update: CharacterUpdate = Body(..., description="The updated character data"),
    character_repository: ICharacterRepository = Depends(get_character_repository)
):
    """
    Update a character.
    
    Args:
        character_id: The character ID
        character_update: The updated character data
        character_repository: The character repository
        
    Returns:
        The updated character
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Update the character
    updated_character = character_repository.update(character_id, character_update.dict())
    
    # Check if the character exists
    if updated_character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return updated_character

@router.patch("/character/{character_id}", response_model=Character)
async def partial_update_character(
    character_id: str = Path(..., description="The ID of the character to update"),
    character_update: CharacterPartialUpdate = Body(..., description="The character data to update"),
    character_repository: ICharacterRepository = Depends(get_character_repository)
):
    """
    Partially update a character.
    
    Args:
        character_id: The character ID
        character_update: The character data to update
        character_repository: The character repository
        
    Returns:
        The updated character
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Remove None values from the update data
    update_data = {k: v for k, v in character_update.dict().items() if v is not None}
    
    # Check if there's anything to update
    if not update_data:
        raise HTTPException(
            status_code=422,
            detail="No valid fields to update"
        )
    
    # Partially update the character
    updated_character = character_repository.partial_update(character_id, update_data)
    
    # Check if the character exists
    if updated_character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return updated_character
```

```python
# rpg_character_db/api/endpoints/character_listing.py
from fastapi import APIRouter, Depends, Query
from typing import List, Optional

from rpg_character_db.models.character import Character
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.dependencies import get_character_repository

router = APIRouter(tags=["characters"])

@router.get("/characters", response_model=List[Character])
async def list_characters(
    class_type: Optional[str] = Query(None, description="Filter characters by class"),
    character_repository: ICharacterRepository = Depends(get_character_repository)
):
    """
    List all characters.
    
    Args:
        class_type: Optional filter for character class
        character_repository: The character repository
        
    Returns:
        A list of characters
    """
    characters = character_repository.list()
    
    # Apply filters if provided
    if class_type:
        characters = [c for c in characters if c.class_type == class_type]
    
    return characters
```

## Updating Tests to Use the Repository

Now, let's update our API tests to use the repository through dependency injection:

```python
# tests/conftest.py
import pytest
from fastapi.testclient import TestClient
from unittest.mock import MagicMock

from rpg_character_db.main import app
from rpg_character_db.db import db
from rpg_character_db.models.character import Character, CharacterCreate
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.dependencies import get_character_repository
from rpg_character_db.repositories.implementations.character_repository import InMemoryCharacterRepository

@pytest.fixture
def client():
    """Create a test client for the API."""
    return TestClient(app)

@pytest.fixture(autouse=True)
def clear_db():
    """Clear the database before and after each test."""
    db.clear()
    yield
    db.clear()

@pytest.fixture
def character_factory():
    """Factory fixture to create test characters."""
    def _create_character(
        character_id=None,
        name="Test Character",
        class_type="Warrior",
        level=1,
        race="Human"
    ):
        """Create a test character with the given attributes."""
        if character_id is None:
            # Create a new character through the repository
            character_create = CharacterCreate(
                name=name,
                class_type=class_type,
                level=level,
                race=race
            )
            repository = InMemoryCharacterRepository(db)
            return repository.create(character_create)
        else:
            # Create a character with a specific ID
            character = Character(
                character_id=character_id,
                name=name,
                class_type=class_type,
                level=level,
                race=race
            )
            db[character.character_id] = character.dict()
            return character
    
    return _create_character

@pytest.fixture
def existing_character(character_factory):
    """Create a test character with a fixed ID."""
    return character_factory(character_id="CHAR:TEST")

@pytest.fixture
def multiple_characters(character_factory):
    """Create multiple test characters."""
    characters = []
    for i in range(3):
        characters.append(character_factory(
            name=f"Character {i}",
            class_type=["Warrior", "Mage", "Rogue"][i % 3],
            level=i + 1,
            race=["Human", "Elf", "Dwarf"][i % 3]
        ))
    return characters

@pytest.fixture
def mock_character_repository():
    """Create a mock character repository."""
    repository = MagicMock(spec=ICharacterRepository)
    
    # Configure the mock
    repository.get.return_value = Character(
        character_id="CHAR:TEST",
        name="Test Character",
        class_type="Warrior",
        level=1,
        race="Human"
    )
    
    return repository

@pytest.fixture
def client_with_mock_repository(mock_character_repository):
    """Create a test client with a mock character repository."""
    app.dependency_overrides[get_character_repository] = lambda: mock_character_repository
    yield TestClient(app)
    app.dependency_overrides.clear()
```

## Running All Tests (GREEN)

Let's run all our tests to make sure everything works:

```bash
pytest -v
```

All tests should pass! We've successfully refactored our application to use the repository pattern with dependency injection.

## Refactoring

Our implementation is working, but there's room for improvement. Let's refactor to make our code more maintainable:

1. We should create a base repository interface to avoid duplicating common methods
2. We should improve error handling in the repository

Let's create a base repository interface:

```python
# rpg_character_db/repositories/interfaces/base_repository.py
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, TypeVar, Generic

T = TypeVar('T')

class IBaseRepository(Generic[T], ABC):
    """Base interface for repositories."""
    
    @abstractmethod
    def get(self, id: str) -> Optional[T]:
        """
        Get an entity by ID.
        
        Args:
            id: The entity ID
            
        Returns:
            The entity if found, None otherwise
        """
        pass
    
    @abstractmethod
    def list(self) -> List[T]:
        """
        List all entities.
        
        Returns:
            A list of all entities
        """
        pass
    
    @abstractmethod
    def delete(self, id: str) -> bool:
        """
        Delete an entity.
        
        Args:
            id: The entity ID
            
        Returns:
            True if the entity was deleted, False otherwise
        """
        pass
```

Now, let's update our character repository interface to inherit from the base interface:

```python
# rpg_character_db/repositories/interfaces/character_repository.py
from typing import Dict, Optional, Any

from rpg_character_db.models.character import Character, CharacterCreate
from rpg_character_db.repositories.interfaces.base_repository import IBaseRepository

class ICharacterRepository(IBaseRepository[Character]):
    """Interface for character repository."""
    
    def create(self, character_create: CharacterCreate) -> Character:
        """
        Create a new character.
        
        Args:
            character_create: The character data
            
        Returns:
            The created character
        """
        pass
    
    def update(self, character_id: str, character_update: Dict[str, Any]) -> Optional[Character]:
        """
        Update a character.
        
        Args:
            character_id: The character ID
            character_update: The character data to update
            
        Returns:
            The updated character if found, None otherwise
        """
        pass
    
    def partial_update(self, character_id: str, character_update: Dict[str, Any]) -> Optional[Character]:
        """
        Partially update a character.
        
        Args:
            character_id: The character ID
            character_update: The character data to update
            
        Returns:
            The updated character if found, None otherwise
        """
        pass
```

## Committing Our Changes

Let's commit our changes to Git:

```bash
git add .
git commit -m "[REFACTOR] Implement repository pattern with dependency injection"
```

## Conclusion

In this lesson, we've:
1. Created interfaces for our repositories
2. Implemented the repository pattern with dependency injection
3. Updated our API endpoints to use the repository
4. Updated our tests to work with the repository
5. Refactored our code for better maintainability

We've successfully applied the repository pattern to our application, making it more maintainable, testable, and ready for future changes like switching to a real database.

In the next lesson, we'll implement character skills management, which will allow us to assign skills to characters based on their level.

Are there any questions about what we've covered today?

Thank you for your attention, and I'll see you in the next lesson!
