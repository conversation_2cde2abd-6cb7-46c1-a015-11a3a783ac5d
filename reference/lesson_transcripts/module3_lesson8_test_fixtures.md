# Module 3, Lesson 8: Test Fixtures and Organization

## Introduction

Welcome back to our TDD course! In our previous lessons, we've implemented the core functionality of our RPG Character Database API, including character retrieval, creation, and updates. As our application grows, it's important to keep our tests organized and maintainable.

Today, we'll focus on improving our test organization by creating reusable test fixtures and moving common test setup to a central location. This will make our tests more concise, readable, and easier to maintain.

## Understanding Test Fixtures in pytest

Before we dive into refactoring our tests, let's discuss what fixtures are in pytest and why they're useful.

Fixtures in pytest are functions that provide a fixed baseline for your tests. They can do setup work before your test runs and cleanup work after your test is done. Some key benefits of fixtures include:

1. **Reusability**: You can define a fixture once and use it in multiple tests
2. **Modularity**: Fixtures can be composed of other fixtures
3. **Scalability**: Fixtures can be scoped to different levels (function, class, module, session)
4. **Cleanup**: Fixtures can handle cleanup automatically

Fixtures are defined using the `@pytest.fixture` decorator, and tests can request fixtures by including them as parameters.

## Identifying Common Test Setup

Let's look at our existing tests and identify common setup patterns that we can extract into fixtures:

1. **Database Cleanup**: We're clearing the database before and after each test
2. **Test Client**: We're creating a TestClient instance in each test file
3. **Existing Character**: We're creating test characters in multiple tests

Let's create a central `conftest.py` file to define these fixtures:

```bash
# Create a conftest.py file in the tests directory
touch tests/conftest.py
```

## Creating Common Fixtures

Now, let's define our common fixtures in the `conftest.py` file:

```python
# tests/conftest.py
import pytest
from fastapi.testclient import TestClient

from rpg_character_db.main import app
from rpg_character_db.db import db
from rpg_character_db.models.character import Character, CharacterCreate
from rpg_character_db.repositories.character_repository import CharacterRepository

@pytest.fixture
def client():
    """Create a test client for the API."""
    return TestClient(app)

@pytest.fixture(autouse=True)
def clear_db():
    """Clear the database before and after each test."""
    db.clear()
    yield
    db.clear()

@pytest.fixture
def character_factory():
    """Factory fixture to create test characters."""
    def _create_character(
        character_id=None,
        name="Test Character",
        class_type="Warrior",
        level=1,
        race="Human"
    ):
        """Create a test character with the given attributes."""
        if character_id is None:
            # Create a new character through the repository
            character_create = CharacterCreate(
                name=name,
                class_type=class_type,
                level=level,
                race=race
            )
            return CharacterRepository.create(character_create)
        else:
            # Create a character with a specific ID
            character = Character(
                character_id=character_id,
                name=name,
                class_type=class_type,
                level=level,
                race=race
            )
            db[character.character_id] = character.dict()
            return character
    
    return _create_character

@pytest.fixture
def existing_character(character_factory):
    """Create a test character with a fixed ID."""
    return character_factory(character_id="CHAR:TEST")

@pytest.fixture
def multiple_characters(character_factory):
    """Create multiple test characters."""
    characters = []
    for i in range(3):
        characters.append(character_factory(
            name=f"Character {i}",
            class_type=["Warrior", "Mage", "Rogue"][i % 3],
            level=i + 1,
            race=["Human", "Elf", "Dwarf"][i % 3]
        ))
    return characters
```

In this `conftest.py` file, we've defined several useful fixtures:

1. `client`: Creates a TestClient instance for making requests to our API
2. `clear_db`: Clears the database before and after each test (with `autouse=True` so it runs automatically)
3. `character_factory`: A factory function for creating test characters with customizable attributes
4. `existing_character`: Creates a test character with a fixed ID
5. `multiple_characters`: Creates multiple test characters for testing list endpoints

## Refactoring Our Tests

Now that we have our common fixtures defined, let's refactor our existing tests to use them. Let's start with the character retrieval tests:

```python
# tests/test_get_character_by_id.py
def test_get_non_existent_character_returns_404(client):
    """Test that requesting a non-existent character returns a 404 status code."""
    response = client.get("/character/CHAR:NON_EXISTENT")
    assert response.status_code == 404
    assert response.json() == {"detail": "Character not found"}

def test_get_invalid_character_id_format_returns_422(client):
    """Test that requesting a character with an invalid ID format returns a 422 status code."""
    response = client.get("/character/invalid-format")
    assert response.status_code == 422
    assert "detail" in response.json()
    assert "message" in response.json()["detail"]
    assert response.json()["detail"]["message"] == "Invalid character ID format"

def test_get_existing_character_returns_200(client, existing_character):
    """Test that requesting an existing character returns a 200 status code."""
    response = client.get(f"/character/{existing_character.character_id}")
    assert response.status_code == 200

def test_get_existing_character_returns_character_data(client, existing_character):
    """Test that requesting an existing character returns the correct character data."""
    response = client.get(f"/character/{existing_character.character_id}")
    assert response.json() == existing_character.dict()
```

Notice how much cleaner and more concise our tests are now. We've:
1. Removed the imports for TestClient, app, db, and Character
2. Removed the clear_db fixture definition
3. Used the client fixture instead of creating our own
4. Used the existing_character fixture instead of creating our own

Let's also refactor the character creation tests:

```python
# tests/test_create_character.py
def test_create_character_returns_201(client):
    """Test that creating a character returns a 201 status code."""
    character_data = {
        "name": "New Character",
        "class_type": "Paladin",
        "level": 5,
        "race": "Dwarf"
    }
    
    response = client.post("/character", json=character_data)
    assert response.status_code == 201

def test_create_character_returns_created_character(client):
    """Test that creating a character returns the created character with an ID."""
    character_data = {
        "name": "New Character",
        "class_type": "Paladin",
        "level": 5,
        "race": "Dwarf"
    }
    
    response = client.post("/character", json=character_data)
    created_character = response.json()
    
    # Check that the response includes all the data we sent plus a character_id
    assert "character_id" in created_character
    assert created_character["name"] == character_data["name"]
    assert created_character["class_type"] == character_data["class_type"]
    assert created_character["level"] == character_data["level"]
    assert created_character["race"] == character_data["race"]
    
    # Check that the character_id follows our expected format
    assert created_character["character_id"].startswith("CHAR:")

def test_create_character_stores_in_database(client):
    """Test that creating a character stores it in the database."""
    character_data = {
        "name": "New Character",
        "class_type": "Paladin",
        "level": 5,
        "race": "Dwarf"
    }
    
    response = client.post("/character", json=character_data)
    created_character = response.json()
    
    # Check that we can retrieve the character
    get_response = client.get(f"/character/{created_character['character_id']}")
    assert get_response.status_code == 200
    assert get_response.json() == created_character

def test_create_character_with_invalid_data_returns_422(client):
    """Test that creating a character with invalid data returns a 422 status code."""
    # Missing required fields
    character_data = {
        "name": "New Character",
        # Missing class_type
        "level": 5,
        "race": "Dwarf"
    }
    
    response = client.post("/character", json=character_data)
    assert response.status_code == 422
    
    # Invalid level (less than 1)
    character_data = {
        "name": "New Character",
        "class_type": "Paladin",
        "level": 0,  # Invalid level
        "race": "Dwarf"
    }
    
    response = client.post("/character", json=character_data)
    assert response.status_code == 422
```

And finally, let's refactor the character update tests:

```python
# tests/test_update_character.py
def test_update_non_existent_character_returns_404(client):
    """Test that updating a non-existent character returns a 404 status code."""
    update_data = {
        "name": "Updated Name",
        "class_type": "Mage",
        "level": 2,
        "race": "Elf"
    }
    
    response = client.put("/character/CHAR:NON_EXISTENT", json=update_data)
    assert response.status_code == 404
    assert response.json() == {"detail": "Character not found"}

def test_update_character_with_invalid_id_format_returns_422(client):
    """Test that updating a character with an invalid ID format returns a 422 status code."""
    update_data = {
        "name": "Updated Name",
        "class_type": "Mage",
        "level": 2,
        "race": "Elf"
    }
    
    response = client.put("/character/invalid-format", json=update_data)
    assert response.status_code == 422
    assert "detail" in response.json()
    assert "message" in response.json()["detail"]
    assert response.json()["detail"]["message"] == "Invalid character ID format"

def test_update_character_returns_200(client, existing_character):
    """Test that updating a character returns a 200 status code."""
    update_data = {
        "name": "Updated Name",
        "class_type": "Mage",
        "level": 2,
        "race": "Elf"
    }
    
    response = client.put(f"/character/{existing_character.character_id}", json=update_data)
    assert response.status_code == 200

def test_update_character_returns_updated_character(client, existing_character):
    """Test that updating a character returns the updated character."""
    update_data = {
        "name": "Updated Name",
        "class_type": "Mage",
        "level": 2,
        "race": "Elf"
    }
    
    response = client.put(f"/character/{existing_character.character_id}", json=update_data)
    updated_character = response.json()
    
    # Check that the response includes the updated data
    assert updated_character["character_id"] == existing_character.character_id
    assert updated_character["name"] == update_data["name"]
    assert updated_character["class_type"] == update_data["class_type"]
    assert updated_character["level"] == update_data["level"]
    assert updated_character["race"] == update_data["race"]

def test_partial_update_character(client, existing_character):
    """Test that partially updating a character only updates the specified fields."""
    # Only update the name and level
    update_data = {
        "name": "Updated Name",
        "level": 2
    }
    
    response = client.patch(f"/character/{existing_character.character_id}", json=update_data)
    updated_character = response.json()
    
    # Check that only the specified fields were updated
    assert updated_character["character_id"] == existing_character.character_id
    assert updated_character["name"] == update_data["name"]
    assert updated_character["class_type"] == existing_character.class_type
    assert updated_character["level"] == update_data["level"]
    assert updated_character["race"] == existing_character.race
```

## Adding Tests for Character Listing

Now that we have a fixture for creating multiple characters, let's add tests for listing characters:

```bash
# Create a new test file for character listing
touch tests/test_list_characters.py
```

```python
# tests/test_list_characters.py
def test_list_characters_returns_200(client):
    """Test that listing characters returns a 200 status code."""
    response = client.get("/characters")
    assert response.status_code == 200

def test_list_characters_returns_empty_list_when_no_characters(client):
    """Test that listing characters returns an empty list when there are no characters."""
    response = client.get("/characters")
    assert response.json() == []

def test_list_characters_returns_all_characters(client, multiple_characters):
    """Test that listing characters returns all characters."""
    response = client.get("/characters")
    characters = response.json()
    
    # Check that we got the right number of characters
    assert len(characters) == len(multiple_characters)
    
    # Check that all characters are in the response
    character_ids = [c["character_id"] for c in characters]
    for character in multiple_characters:
        assert character.character_id in character_ids

def test_list_characters_with_class_filter(client, multiple_characters):
    """Test that listing characters with a class filter returns only characters of that class."""
    # Get a character class from our test data
    test_class = multiple_characters[0].class_type
    
    response = client.get(f"/characters?class_type={test_class}")
    characters = response.json()
    
    # Check that all returned characters have the requested class
    for character in characters:
        assert character["class_type"] == test_class
```

## Implementing the Character Listing Endpoint

Now that we have tests for character listing, let's implement the endpoint:

```python
# rpg_character_db/api/character_routes.py
from fastapi import APIRouter, HTTPException, Path, status, Body, Query
import re
from typing import List, Optional

from rpg_character_db.models.character import Character, CharacterCreate, CharacterUpdate, CharacterPartialUpdate
from rpg_character_db.repositories.character_repository import CharacterRepository

router = APIRouter(prefix="/character", tags=["characters"])

# ... existing code ...

@router.get("/", response_model=List[Character])
async def list_characters(
    class_type: Optional[str] = Query(None, description="Filter characters by class")
):
    """
    List all characters.
    
    Args:
        class_type: Optional filter for character class
        
    Returns:
        A list of characters
    """
    characters = CharacterRepository.list()
    
    # Apply filters if provided
    if class_type:
        characters = [c for c in characters if c.class_type == class_type]
    
    return characters
```

Wait, there's a problem here. Our endpoint is defined at `/character/`, but our tests are using `/characters`. Let's update our endpoint to match the tests:

```python
# rpg_character_db/api/character_routes.py
# ... existing code ...

# Update the router prefix
router = APIRouter(tags=["characters"])

# ... existing code ...

@router.get("/character/{character_id}", response_model=Character)
async def get_character_by_id(
    character_id: str = Path(..., description="The ID of the character to retrieve")
):
    # ... existing code ...

@router.post("/character", response_model=Character, status_code=status.HTTP_201_CREATED)
async def create_character(character_create: CharacterCreate):
    # ... existing code ...

@router.put("/character/{character_id}", response_model=Character)
async def update_character(
    character_id: str = Path(..., description="The ID of the character to update"),
    character_update: CharacterUpdate = Body(..., description="The updated character data")
):
    # ... existing code ...

@router.patch("/character/{character_id}", response_model=Character)
async def partial_update_character(
    character_id: str = Path(..., description="The ID of the character to update"),
    character_update: CharacterPartialUpdate = Body(..., description="The character data to update")
):
    # ... existing code ...

@router.get("/characters", response_model=List[Character])
async def list_characters(
    class_type: Optional[str] = Query(None, description="Filter characters by class")
):
    """
    List all characters.
    
    Args:
        class_type: Optional filter for character class
        
    Returns:
        A list of characters
    """
    characters = CharacterRepository.list()
    
    # Apply filters if provided
    if class_type:
        characters = [c for c in characters if c.class_type == class_type]
    
    return characters
```

We've updated our router to use a tag instead of a prefix, and we've updated all our endpoint paths to include `/character` or `/characters` as appropriate.

## Running the Tests

Let's run our tests to see if our refactoring and new endpoint implementation work:

```bash
pytest -v
```

All our tests should pass! We've successfully refactored our tests to use common fixtures and implemented a new endpoint for listing characters.

## Organizing Tests by Feature

Another way to improve test organization is to group tests by feature or endpoint. We've already started doing this by creating separate test files for different endpoints:

- `test_get_character_by_id.py`: Tests for retrieving characters
- `test_create_character.py`: Tests for creating characters
- `test_update_character.py`: Tests for updating characters
- `test_list_characters.py`: Tests for listing characters

This organization makes it easy to find tests for a specific feature and keeps related tests together.

## Committing Our Changes

Let's commit our changes to Git:

```bash
git add .
git commit -m "[REFACTOR] Improve test organization with fixtures"
```

## Conclusion

In this lesson, we've:
1. Created reusable test fixtures in a central `conftest.py` file
2. Refactored our existing tests to use these fixtures
3. Added tests and implementation for a new character listing endpoint
4. Improved our test organization by grouping tests by feature

These improvements make our tests more maintainable, readable, and concise. As our application grows, good test organization becomes increasingly important.

In the next lesson, we'll continue our refactoring by separating our API code into modules, making our codebase more modular and maintainable.

Are there any questions about what we've covered today?

Thank you for your attention, and I'll see you in the next lesson!
