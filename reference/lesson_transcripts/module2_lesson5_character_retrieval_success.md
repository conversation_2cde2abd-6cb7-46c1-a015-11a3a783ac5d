# Module 2, Lesson 5: Character Retrieval - Success

## Introduction

Welcome back to our TDD course! In our previous lesson, we implemented character ID validation and handled the "not found" scenario for character retrieval. Today, we'll take the next step by implementing character storage and successful retrieval of existing characters.

This lesson will introduce an in-memory database to store character data and expand our API to return character information when it exists. As always, we'll follow the RED-GREEN-REFACTOR cycle to drive our development.

## Creating an In-Memory Database

Before we write our tests, let's think about how we'll store character data. For now, we'll use a simple in-memory dictionary as our database. Later in the course, we'll refactor to use a proper database with SQLAlchemy.

Let's create a new module for our in-memory database:

```bash
# Create a new file for our database
touch rpg_character_db/db.py
```

We'll start with a very simple implementation:

```python
# rpg_character_db/db.py
"""
In-memory database for storing character data.
This will be replaced with a proper database later.
"""

# Our in-memory database is just a dictionary
db = {}

# Initialize with some sample data for development
db["CHAR:1"] = {
    "character_id": "CHAR:1",
    "name": "Aragorn",
    "class_type": "Ranger",
    "level": 10,
    "race": "Human"
}

db["CHAR:2"] = {
    "character_id": "CHAR:2",
    "name": "Gandalf",
    "class_type": "Wizard",
    "level": 20,
    "race": "Maia"
}
```

This simple module defines a dictionary `db` that will store our character data. We've initialized it with two sample characters for development purposes.

## Writing Tests for Successful Character Retrieval

Now, let's write tests for retrieving existing characters. We'll add these tests to our existing test file:

```python
# tests/test_get_character_by_id.py
from fastapi.testclient import TestClient

from rpg_character_db.main import app
from rpg_character_db.db import db  # Import our in-memory database

client = TestClient(app)

# Existing tests...

def test_get_existing_character_returns_200():
    """Test that requesting an existing character returns a 200 status code."""
    # Ensure the character exists in our test database
    db["CHAR:TEST"] = {
        "character_id": "CHAR:TEST",
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 1,
        "race": "Human"
    }
    
    response = client.get("/character/CHAR:TEST")
    assert response.status_code == 200

def test_get_existing_character_returns_character_data():
    """Test that requesting an existing character returns the correct character data."""
    # Ensure the character exists in our test database
    test_character = {
        "character_id": "CHAR:TEST",
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 1,
        "race": "Human"
    }
    db["CHAR:TEST"] = test_character
    
    response = client.get("/character/CHAR:TEST")
    assert response.json() == test_character
```

In these tests, we're:
1. Adding a test character to our in-memory database
2. Making a GET request to retrieve that character
3. Asserting that the response status code is 200 (OK)
4. Asserting that the response JSON matches the character data we added

## Running the Tests (RED)

Let's run our tests to see them fail:

```bash
pytest tests/test_get_character_by_id.py -v
```

Our new tests should fail because our current implementation always returns a 404 error, regardless of whether the character exists or not.

## Updating the Endpoint Implementation (GREEN)

Now, let's update our endpoint to check if the character exists in our database and return it if it does:

```python
# rpg_character_db/api/character_routes.py
from fastapi import APIRouter, HTTPException, Path
import re

from rpg_character_db.db import db  # Import our in-memory database

router = APIRouter(prefix="/character", tags=["characters"])

# Define a regex pattern for valid character IDs
CHAR_ID_PATTERN = r"^CHAR:[a-zA-Z0-9]+$"

def validate_character_id(character_id: str) -> None:
    """
    Validate that a character ID matches the expected format.
    
    Args:
        character_id: The character ID to validate
        
    Raises:
        HTTPException: If the character ID is invalid
    """
    if not re.match(CHAR_ID_PATTERN, character_id):
        raise HTTPException(
            status_code=422,
            detail={
                "message": "Invalid character ID format",
                "expected_pattern": CHAR_ID_PATTERN,
                "received": character_id
            }
        )

@router.get("/{character_id}")
async def get_character_by_id(
    character_id: str = Path(..., description="The ID of the character to retrieve")
):
    # Validate character ID format
    validate_character_id(character_id)
    
    # Check if the character exists in our database
    if character_id not in db:
        raise HTTPException(status_code=404, detail="Character not found")
    
    # Return the character data
    return db[character_id]
```

In this updated implementation, we:
1. Import our in-memory database
2. Check if the requested character ID exists in the database
3. Return a 404 error if it doesn't
4. Return the character data if it does

## Running the Tests Again (GREEN)

Let's run our tests again to see if they pass:

```bash
pytest tests/test_get_character_by_id.py -v
```

All our tests should now pass! We've successfully implemented character retrieval for both existing and non-existent characters.

## Refactoring

Our implementation is working, but there's room for improvement. Let's refactor to make our code more maintainable and follow better practices:

1. We should define a Pydantic model for our character data to ensure consistent structure and validation
2. We should clean up our test database between tests to avoid test interdependence

Let's start by creating a Pydantic model for our character:

```bash
# Create a new file for our models
touch rpg_character_db/models/character.py
```

```python
# rpg_character_db/models/character.py
from pydantic import BaseModel, Field

class Character(BaseModel):
    """Model representing an RPG character."""
    character_id: str = Field(..., description="Unique identifier for the character")
    name: str = Field(..., description="Character name")
    class_type: str = Field(..., description="Character class (e.g., Warrior, Mage)")
    level: int = Field(..., description="Character level", ge=1)
    race: str = Field(..., description="Character race (e.g., Human, Elf)")
```

Now, let's update our character routes to use this model:

```python
# rpg_character_db/api/character_routes.py
from fastapi import APIRouter, HTTPException, Path
import re

from rpg_character_db.db import db
from rpg_character_db.models.character import Character

router = APIRouter(prefix="/character", tags=["characters"])

# Define a regex pattern for valid character IDs
CHAR_ID_PATTERN = r"^CHAR:[a-zA-Z0-9]+$"

def validate_character_id(character_id: str) -> None:
    """
    Validate that a character ID matches the expected format.
    
    Args:
        character_id: The character ID to validate
        
    Raises:
        HTTPException: If the character ID is invalid
    """
    if not re.match(CHAR_ID_PATTERN, character_id):
        raise HTTPException(
            status_code=422,
            detail={
                "message": "Invalid character ID format",
                "expected_pattern": CHAR_ID_PATTERN,
                "received": character_id
            }
        )

@router.get("/{character_id}", response_model=Character)
async def get_character_by_id(
    character_id: str = Path(..., description="The ID of the character to retrieve")
):
    # Validate character ID format
    validate_character_id(character_id)
    
    # Check if the character exists in our database
    if character_id not in db:
        raise HTTPException(status_code=404, detail="Character not found")
    
    # Return the character data
    return Character(**db[character_id])
```

We've made several improvements:
1. We're using our Character Pydantic model as the response model for the endpoint
2. We're converting the dictionary from our database to a Character model before returning it
3. We're taking advantage of FastAPI's automatic validation and documentation features

Now, let's update our tests to clean up the database between tests:

```python
# tests/test_get_character_by_id.py
import pytest
from fastapi.testclient import TestClient

from rpg_character_db.main import app
from rpg_character_db.db import db
from rpg_character_db.models.character import Character

client = TestClient(app)

@pytest.fixture(autouse=True)
def clear_db():
    """Clear the database before each test."""
    db.clear()
    yield
    db.clear()

def test_get_non_existent_character_returns_404():
    """Test that requesting a non-existent character returns a 404 status code."""
    response = client.get("/character/CHAR:NON_EXISTENT")
    assert response.status_code == 404
    assert response.json() == {"detail": "Character not found"}

def test_get_invalid_character_id_format_returns_422():
    """Test that requesting a character with an invalid ID format returns a 422 status code."""
    response = client.get("/character/invalid-format")
    assert response.status_code == 422
    assert "detail" in response.json()
    assert "message" in response.json()["detail"]
    assert response.json()["detail"]["message"] == "Invalid character ID format"

def test_get_existing_character_returns_200():
    """Test that requesting an existing character returns a 200 status code."""
    # Create a test character
    test_character = Character(
        character_id="CHAR:TEST",
        name="Test Character",
        class_type="Warrior",
        level=1,
        race="Human"
    )
    db[test_character.character_id] = test_character.dict()
    
    response = client.get("/character/CHAR:TEST")
    assert response.status_code == 200

def test_get_existing_character_returns_character_data():
    """Test that requesting an existing character returns the correct character data."""
    # Create a test character
    test_character = Character(
        character_id="CHAR:TEST",
        name="Test Character",
        class_type="Warrior",
        level=1,
        race="Human"
    )
    db[test_character.character_id] = test_character.dict()
    
    response = client.get("/character/CHAR:TEST")
    assert response.json() == test_character.dict()
```

We've made several improvements to our tests:
1. We've added a fixture that clears the database before and after each test
2. We're using our Character Pydantic model to create test data
3. We've updated our assertions to match the expected response format

## Running the Tests After Refactoring

Let's run our tests one more time to ensure our refactoring didn't break anything:

```bash
pytest tests/test_get_character_by_id.py -v
```

All tests should still pass, confirming that our refactoring was successful.

## Committing Our Changes

Let's commit our changes to Git:

```bash
git add .
git commit -m "[RED/GREEN/REFACTOR] Implement character retrieval for existing characters"
```

## Conclusion

In this lesson, we've:
1. Created an in-memory database to store character data
2. Implemented character retrieval for existing characters
3. Defined a Pydantic model for our character data
4. Improved our tests with fixtures and better organization

We've continued to follow the RED-GREEN-REFACTOR cycle, letting our tests drive the development of our API.

In the next lesson, we'll implement character creation, which will allow clients to add new characters to our database.

Are there any questions about what we've covered today?

Thank you for your attention, and I'll see you in the next lesson!
