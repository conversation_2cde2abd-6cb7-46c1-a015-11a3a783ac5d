# Module 5, Lesson 11: Introducing the Repository Pattern

## Introduction

Welcome back to our TDD course! In our previous lessons, we've built a functional RPG Character Database API with character and attribute management. Today, we'll take a step back and discuss an important design pattern that we've been using: the Repository Pattern.

Understanding the repository pattern is crucial for building maintainable and testable applications. In this lesson, we'll explore what the repository pattern is, why it's beneficial, and how it relates to the Dependency Inversion Principle. We'll also plan our approach for further refactoring our application to better leverage this pattern.

## Understanding the Repository Pattern

The Repository Pattern is a design pattern that separates the logic that retrieves data from the underlying storage mechanism. It acts as an abstraction layer between your business logic and your data access layer.

In simpler terms, a repository is a class that encapsulates the logic required to access data sources. It centralizes data access functionality, providing better maintainability and decoupling the application from specific data access technologies.

### Key Components of the Repository Pattern

1. **Repository Interface**: Defines the methods for data access without specifying how the data is stored or retrieved.
2. **Repository Implementation**: Implements the interface, containing the specific logic for data access.
3. **Domain Models**: The business entities that the repository works with.

### Benefits of the Repository Pattern

The Repository Pattern offers several benefits:

1. **Separation of Concerns**: It separates business logic from data access logic.
2. **Testability**: It makes your code easier to test by allowing you to mock the repository in unit tests.
3. **Maintainability**: Changes to data access logic are isolated to the repository implementation.
4. **Flexibility**: You can switch between different data storage mechanisms without affecting the rest of your application.

## The Dependency Inversion Principle

The Repository Pattern is closely related to the Dependency Inversion Principle (DIP), which is one of the SOLID principles of object-oriented design. The DIP states:

1. High-level modules should not depend on low-level modules. Both should depend on abstractions.
2. Abstractions should not depend on details. Details should depend on abstractions.

In the context of our application, this means:

1. Our API endpoints (high-level modules) should not depend directly on the database (low-level module).
2. Instead, both should depend on an abstraction (the repository interface).
3. The repository interface should not depend on the specific database implementation.

By following the DIP, we make our code more modular, testable, and maintainable.

## Current Implementation vs. Ideal Implementation

Let's look at how we're currently implementing the repository pattern in our application and how we could improve it.

### Current Implementation

Currently, we have repository classes like `CharacterRepository` and `AttributeRepository` that directly access our in-memory database:

```python
class CharacterRepository:
    """Repository for character data."""
    
    @classmethod
    def get(cls, character_id: str) -> Optional[Character]:
        if character_id not in db:
            return None
        
        return Character(**db[character_id])
    
    # Other methods...
```

While this is a step in the right direction, there are a few issues:

1. The repository is tightly coupled to our in-memory database.
2. We're not using dependency injection to provide the database to the repository.
3. We don't have an interface defining the repository contract.

### Ideal Implementation

A more ideal implementation would:

1. Define an interface for the repository.
2. Create an implementation that depends on an injected data source.
3. Use dependency injection to provide the data source to the repository.

Here's what this might look like:

```python
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any

class ICharacterRepository(ABC):
    """Interface for character repository."""
    
    @abstractmethod
    def get(self, character_id: str) -> Optional[Character]:
        """Get a character by ID."""
        pass
    
    @abstractmethod
    def create(self, character_create: CharacterCreate) -> Character:
        """Create a new character."""
        pass
    
    # Other methods...

class InMemoryCharacterRepository(ICharacterRepository):
    """In-memory implementation of character repository."""
    
    def __init__(self, db: Dict[str, Dict[str, Any]]):
        self.db = db
    
    def get(self, character_id: str) -> Optional[Character]:
        if character_id not in self.db:
            return None
        
        return Character(**self.db[character_id])
    
    # Other methods...
```

## Planning Our Refactoring Approach

Now that we understand the repository pattern and its benefits, let's plan how we'll refactor our application to better leverage this pattern:

1. **Define Repository Interfaces**: Create interfaces for our repositories to define the contract they must fulfill.
2. **Create Repository Implementations**: Implement these interfaces with our current in-memory database.
3. **Use Dependency Injection**: Modify our API endpoints to use dependency injection to get repository instances.
4. **Update Tests**: Update our tests to use mock repositories or test-specific implementations.

This refactoring will make our code more maintainable, testable, and ready for future changes like switching to a real database.

## Example: Refactoring the Character Repository

Let's look at how we might refactor the `CharacterRepository`:

```python
# rpg_character_db/repositories/interfaces.py
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any

from rpg_character_db.models.character import Character, CharacterCreate

class ICharacterRepository(ABC):
    """Interface for character repository."""
    
    @abstractmethod
    def get(self, character_id: str) -> Optional[Character]:
        """
        Get a character by ID.
        
        Args:
            character_id: The character ID
            
        Returns:
            The character if found, None otherwise
        """
        pass
    
    @abstractmethod
    def create(self, character_create: CharacterCreate) -> Character:
        """
        Create a new character.
        
        Args:
            character_create: The character data
            
        Returns:
            The created character
        """
        pass
    
    @abstractmethod
    def update(self, character_id: str, character_update: Dict[str, Any]) -> Optional[Character]:
        """
        Update a character.
        
        Args:
            character_id: The character ID
            character_update: The character data to update
            
        Returns:
            The updated character if found, None otherwise
        """
        pass
    
    @abstractmethod
    def partial_update(self, character_id: str, character_update: Dict[str, Any]) -> Optional[Character]:
        """
        Partially update a character.
        
        Args:
            character_id: The character ID
            character_update: The character data to update
            
        Returns:
            The updated character if found, None otherwise
        """
        pass
    
    @abstractmethod
    def list(self) -> List[Character]:
        """
        List all characters.
        
        Returns:
            A list of all characters
        """
        pass
    
    @abstractmethod
    def delete(self, character_id: str) -> bool:
        """
        Delete a character.
        
        Args:
            character_id: The character ID
            
        Returns:
            True if the character was deleted, False otherwise
        """
        pass

# rpg_character_db/repositories/character_repository.py
from typing import Dict, List, Optional, Any

from rpg_character_db.db import db
from rpg_character_db.models.character import Character, CharacterCreate
from rpg_character_db.repositories.interfaces import ICharacterRepository

class InMemoryCharacterRepository(ICharacterRepository):
    """In-memory implementation of character repository."""
    
    def __init__(self, db: Dict[str, Dict[str, Any]]):
        self.db = db
    
    def get(self, character_id: str) -> Optional[Character]:
        """
        Get a character by ID.
        
        Args:
            character_id: The character ID
            
        Returns:
            The character if found, None otherwise
        """
        if character_id not in self.db:
            return None
        
        return Character(**self.db[character_id])
    
    def create(self, character_create: CharacterCreate) -> Character:
        """
        Create a new character.
        
        Args:
            character_create: The character data
            
        Returns:
            The created character
        """
        character = Character.create(character_create)
        self.db[character.character_id] = character.dict()
        return character
    
    def update(self, character_id: str, character_update: Dict[str, Any]) -> Optional[Character]:
        """
        Update a character.
        
        Args:
            character_id: The character ID
            character_update: The character data to update
            
        Returns:
            The updated character if found, None otherwise
        """
        if character_id not in self.db:
            return None
        
        # Get the current character data
        character_data = self.db[character_id]
        
        # Update the character data
        character_data.update(character_update)
        
        # Store the updated character
        self.db[character_id] = character_data
        
        return Character(**character_data)
    
    def partial_update(self, character_id: str, character_update: Dict[str, Any]) -> Optional[Character]:
        """
        Partially update a character.
        
        Args:
            character_id: The character ID
            character_update: The character data to update
            
        Returns:
            The updated character if found, None otherwise
        """
        if character_id not in self.db:
            return None
        
        # Get the current character data
        character_data = self.db[character_id].copy()
        
        # Update only the specified fields
        for key, value in character_update.items():
            if key in character_data:
                character_data[key] = value
        
        # Store the updated character
        self.db[character_id] = character_data
        
        return Character(**character_data)
    
    def list(self) -> List[Character]:
        """
        List all characters.
        
        Returns:
            A list of all characters
        """
        return [Character(**data) for data in self.db.values() if data.get("character_id", "").startswith("CHAR:")]
    
    def delete(self, character_id: str) -> bool:
        """
        Delete a character.
        
        Args:
            character_id: The character ID
            
        Returns:
            True if the character was deleted, False otherwise
        """
        if character_id not in self.db:
            return False
        
        del self.db[character_id]
        return True
```

## Using the Repository in API Endpoints

Once we've refactored our repositories, we'll need to update our API endpoints to use them. Here's how we might do that:

```python
# rpg_character_db/api/endpoints/character_retrieval.py
from fastapi import APIRouter, Depends, HTTPException, Path

from rpg_character_db.models.character import Character
from rpg_character_db.repositories.interfaces import ICharacterRepository
from rpg_character_db.dependencies import get_character_repository
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter(tags=["characters"])

@router.get("/character/{character_id}", response_model=Character)
async def get_character_by_id(
    character_id: str = Path(..., description="The ID of the character to retrieve"),
    character_repository: ICharacterRepository = Depends(get_character_repository)
):
    """
    Get a character by ID.
    
    Args:
        character_id: The character ID
        character_repository: The character repository
        
    Returns:
        The character if found
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character from the repository
    character = character_repository.get(character_id)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return character
```

## Setting Up Dependency Injection

To make this work, we'll need to set up dependency injection for our repositories:

```python
# rpg_character_db/dependencies.py
from fastapi import Depends

from rpg_character_db.db import db
from rpg_character_db.repositories.interfaces import ICharacterRepository, IAttributeRepository
from rpg_character_db.repositories.character_repository import InMemoryCharacterRepository
from rpg_character_db.repositories.attribute_repository import InMemoryAttributeRepository

def get_db():
    """Get the database."""
    return db

def get_character_repository(db=Depends(get_db)) -> ICharacterRepository:
    """Get the character repository."""
    return InMemoryCharacterRepository(db)

def get_attribute_repository(db=Depends(get_db)) -> IAttributeRepository:
    """Get the attribute repository."""
    return InMemoryAttributeRepository(db)
```

## Benefits for Testing

One of the biggest benefits of this refactoring is improved testability. With dependency injection, we can easily provide mock repositories in our tests:

```python
# tests/test_character_retrieval.py
from unittest.mock import MagicMock
import pytest
from fastapi.testclient import TestClient

from rpg_character_db.main import app
from rpg_character_db.models.character import Character
from rpg_character_db.repositories.interfaces import ICharacterRepository
from rpg_character_db.dependencies import get_character_repository

@pytest.fixture
def mock_character_repository():
    """Create a mock character repository."""
    repository = MagicMock(spec=ICharacterRepository)
    
    # Configure the mock
    repository.get.return_value = Character(
        character_id="CHAR:TEST",
        name="Test Character",
        class_type="Warrior",
        level=1,
        race="Human"
    )
    
    return repository

@pytest.fixture
def client(mock_character_repository):
    """Create a test client with a mock character repository."""
    app.dependency_overrides[get_character_repository] = lambda: mock_character_repository
    yield TestClient(app)
    app.dependency_overrides.clear()

def test_get_character_by_id(client, mock_character_repository):
    """Test that getting a character by ID returns the character."""
    response = client.get("/character/CHAR:TEST")
    assert response.status_code == 200
    assert response.json() == {
        "character_id": "CHAR:TEST",
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 1,
        "race": "Human",
        "attributes": {"attributes": {}}
    }
    
    # Verify that the repository was called correctly
    mock_character_repository.get.assert_called_once_with("CHAR:TEST")
```

## Conclusion

In this lesson, we've:
1. Explored the Repository Pattern and its benefits
2. Discussed the Dependency Inversion Principle
3. Planned our approach for refactoring our application
4. Looked at examples of how to implement the repository pattern with interfaces and dependency injection
5. Seen how this refactoring improves testability

Understanding and applying the repository pattern is a crucial step in building maintainable, testable applications. In the next lesson, we'll implement these changes and refactor our application to better leverage the repository pattern.

Are there any questions about what we've covered today?

Thank you for your attention, and I'll see you in the next lesson!
