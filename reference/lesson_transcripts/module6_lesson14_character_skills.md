# Module 6, Lesson 14: Character Skills Management

## Introduction

Welcome back to our TDD course! In our previous lessons, we've implemented the core functionality of our RPG Character Database API, refactored our code to use the repository pattern, and enhanced our dependency injection system. Today, we'll expand our API by implementing character skills management.

In RPG games, characters typically have various skills that they can learn and improve as they level up. We'll implement functionality to assign skills to characters, update skill levels, and remove skills when needed.

As always, we'll follow the RED-GREEN-REFACTOR cycle to drive our development.

## Defining Our Requirements

Before we start writing tests, let's define what we want to achieve:

1. Users should be able to assign skills to a character
2. Users should be able to update skill levels for a character
3. Users should be able to remove skills from a character
4. The system should validate that skill levels are within acceptable ranges
5. The system should validate that characters meet the level requirements for skills

## Creating Models for Skills

Let's start by creating models for our skills:

```bash
# Create a new file for skill models
touch rpg_character_db/models/skill.py
```

```python
# rpg_character_db/models/skill.py
from pydantic import BaseModel, Field, validator
from typing import Dict, Optional

class Skill(BaseModel):
    """Model representing an RPG character skill."""
    skill_id: str = Field(..., description="Unique identifier for the skill")
    name: str = Field(..., description="Skill name")
    description: str = Field(..., description="Skill description")
    required_level: int = Field(..., description="Minimum character level required to learn this skill", ge=1)

class SkillLevel(BaseModel):
    """Model representing a skill level assigned to a character."""
    level: int = Field(..., description="Skill level", ge=1, le=5)
    
    @validator('level')
    def validate_level_range(cls, v):
        """Validate that the skill level is within the acceptable range."""
        if v < 1 or v > 5:
            raise ValueError("Skill level must be between 1 and 5")
        return v

class CharacterSkill(BaseModel):
    """Model representing a skill with its level for a character."""
    skill_id: str = Field(..., description="Unique identifier for the skill")
    name: str = Field(..., description="Skill name")
    description: str = Field(..., description="Skill description")
    required_level: int = Field(..., description="Minimum character level required to learn this skill", ge=1)
    level: int = Field(..., description="Skill level", ge=1, le=5)

class CharacterSkills(BaseModel):
    """Model representing all skills for a character."""
    skills: Dict[str, CharacterSkill] = Field(default_factory=dict)
```

Now, let's update our character model to include skills:

```python
# rpg_character_db/models/character.py
from pydantic import BaseModel, Field, validator
import uuid
from typing import Dict, Optional

from rpg_character_db.models.attribute import CharacterAttributes
from rpg_character_db.models.skill import CharacterSkills

class CharacterBase(BaseModel):
    """Base model for character data."""
    name: str = Field(..., description="Character name")
    class_type: str = Field(..., description="Character class (e.g., Warrior, Mage)")
    level: int = Field(..., description="Character level", ge=1)
    race: str = Field(..., description="Character race (e.g., Human, Elf)")

class CharacterCreate(CharacterBase):
    """Model for creating a new character."""
    pass

class CharacterUpdate(CharacterBase):
    """Model for updating a character."""
    pass

class CharacterPartialUpdate(BaseModel):
    """Model for partially updating a character."""
    name: Optional[str] = Field(None, description="Character name")
    class_type: Optional[str] = Field(None, description="Character class (e.g., Warrior, Mage)")
    level: Optional[int] = Field(None, description="Character level", ge=1)
    race: Optional[str] = Field(None, description="Character race (e.g., Human, Elf)")
    
    @validator('*', pre=True)
    def check_not_empty(cls, v):
        """Validate that at least one field is provided."""
        if v == "":
            return None
        return v
    
    class Config:
        validate_assignment = True

class Character(CharacterBase):
    """Model representing an RPG character."""
    character_id: str = Field(..., description="Unique identifier for the character")
    attributes: CharacterAttributes = Field(default_factory=CharacterAttributes)
    skills: CharacterSkills = Field(default_factory=CharacterSkills)
    
    @classmethod
    def create(cls, character_create: CharacterCreate) -> "Character":
        """Create a new character from a CharacterCreate model."""
        return cls(
            character_id=f"CHAR:{uuid.uuid4().hex[:8]}",
            **character_create.dict()
        )
```

## Initializing Some Skills

Let's create some predefined skills in our database:

```python
# rpg_character_db/db.py
"""
In-memory database for storing character data.
This will be replaced with a proper database later.
"""

# Our in-memory database is just a dictionary
db = {}

# Initialize with some sample attributes
db["ATTR:STR"] = {
    "attribute_id": "ATTR:STR",
    "name": "Strength",
    "description": "Physical power and carrying capacity"
}

# ... other attributes ...

# Initialize with some sample skills
db["SKILL:SLASH"] = {
    "skill_id": "SKILL:SLASH",
    "name": "Slash",
    "description": "A basic melee attack",
    "required_level": 1
}

db["SKILL:FIREBALL"] = {
    "skill_id": "SKILL:FIREBALL",
    "name": "Fireball",
    "description": "A powerful fire-based spell",
    "required_level": 3
}

db["SKILL:HEAL"] = {
    "skill_id": "SKILL:HEAL",
    "name": "Heal",
    "description": "Restore health to a target",
    "required_level": 2
}

db["SKILL:STEALTH"] = {
    "skill_id": "SKILL:STEALTH",
    "name": "Stealth",
    "description": "Move silently and avoid detection",
    "required_level": 2
}

db["SKILL:DUAL_WIELD"] = {
    "skill_id": "SKILL:DUAL_WIELD",
    "name": "Dual Wield",
    "description": "Fight with a weapon in each hand",
    "required_level": 5
}
```

## Creating a Skill Repository

Let's create a repository for managing skills:

```bash
# Create a new file for the skill repository interface
touch rpg_character_db/repositories/interfaces/skill_repository.py

# Create a new file for the skill repository implementation
touch rpg_character_db/repositories/implementations/skill_repository.py
```

```python
# rpg_character_db/repositories/interfaces/skill_repository.py
from typing import List, Optional

from rpg_character_db.models.skill import Skill
from rpg_character_db.repositories.interfaces.base_repository import IBaseRepository

class ISkillRepository(IBaseRepository[Skill]):
    """Interface for skill repository."""
    pass
```

```python
# rpg_character_db/repositories/implementations/skill_repository.py
from typing import Dict, List, Optional, Any

from rpg_character_db.models.skill import Skill
from rpg_character_db.repositories.interfaces.skill_repository import ISkillRepository

class InMemorySkillRepository(ISkillRepository):
    """In-memory implementation of skill repository."""
    
    def __init__(self, db: Dict[str, Dict[str, Any]]):
        """
        Initialize the repository.
        
        Args:
            db: The database to use
        """
        self.db = db
    
    def get(self, skill_id: str) -> Optional[Skill]:
        """
        Get a skill by ID.
        
        Args:
            skill_id: The skill ID
            
        Returns:
            The skill if found, None otherwise
        """
        if skill_id not in self.db:
            return None
        
        return Skill(**self.db[skill_id])
    
    def list(self) -> List[Skill]:
        """
        List all skills.
        
        Returns:
            A list of all skills
        """
        return [
            Skill(**data)
            for skill_id, data in self.db.items()
            if skill_id.startswith("SKILL:")
        ]
    
    def delete(self, skill_id: str) -> bool:
        """
        Delete a skill.
        
        Args:
            skill_id: The skill ID
            
        Returns:
            True if the skill was deleted, False otherwise
        """
        if skill_id not in self.db:
            return False
        
        del self.db[skill_id]
        return True
```

## Updating Dependencies

Let's update our dependencies to include the skill repository:

```python
# rpg_character_db/dependencies/__init__.py
# ... existing code ...

# Skill repository
def _get_skill_repository() -> ISkillRepository:
    """Get the skill repository."""
    return InMemorySkillRepository(db)

# Create dependency with override
skill_repository_dependency = DependencyWithOverride(_get_skill_repository)

# Convenience function for setting the override
def override_skill_repository(override_func):
    """
    Override the skill repository dependency.
    
    Args:
        override_func: A function that returns a skill repository implementation,
                      or None to clear the override
    """
    skill_repository_dependency.set_override(override_func)

# Dependency function for FastAPI
def get_skill_repository() -> ISkillRepository:
    """
    Get the skill repository.
    
    Returns:
        The skill repository
    """
    return skill_repository_dependency()
```

## Writing Tests for Character Skills Management

Now, let's write tests for character skills management:

```bash
# Create a new test file for character skills
touch tests/test_character_skills.py
```

```python
# tests/test_character_skills.py
import pytest
from fastapi.testclient import TestClient

from rpg_character_db.models.skill import Skill, SkillLevel, CharacterSkill

def test_get_character_skills_returns_200(client, existing_character):
    """Test that getting character skills returns a 200 status code."""
    response = client.get(f"/character/{existing_character.character_id}/skills")
    assert response.status_code == 200

def test_get_character_skills_returns_empty_list_initially(client, existing_character):
    """Test that getting character skills returns an empty list initially."""
    response = client.get(f"/character/{existing_character.character_id}/skills")
    assert response.json() == {"skills": {}}

def test_set_character_skill_returns_200(client, existing_character):
    """Test that setting a character skill returns a 200 status code."""
    skill_level = {"level": 1}
    response = client.put(
        f"/character/{existing_character.character_id}/skill/SKILL:SLASH",
        json=skill_level
    )
    assert response.status_code == 200

def test_set_character_skill_returns_updated_character(client, existing_character):
    """Test that setting a character skill returns the updated character."""
    skill_level = {"level": 1}
    response = client.put(
        f"/character/{existing_character.character_id}/skill/SKILL:SLASH",
        json=skill_level
    )
    updated_character = response.json()
    
    # Check that the skill was added
    assert "skills" in updated_character
    assert "skills" in updated_character["skills"]
    assert "SKILL:SLASH" in updated_character["skills"]["skills"]
    assert updated_character["skills"]["skills"]["SKILL:SLASH"]["level"] == 1

def test_get_character_skills_returns_skills_after_setting(client, existing_character):
    """Test that getting character skills returns the skills after setting them."""
    # Set a skill
    skill_level = {"level": 1}
    client.put(
        f"/character/{existing_character.character_id}/skill/SKILL:SLASH",
        json=skill_level
    )
    
    # Get the skills
    response = client.get(f"/character/{existing_character.character_id}/skills")
    skills = response.json()
    
    # Check that the skill is in the response
    assert "skills" in skills
    assert "SKILL:SLASH" in skills["skills"]
    assert skills["skills"]["SKILL:SLASH"]["level"] == 1

def test_set_character_skill_with_invalid_level_returns_422(client, existing_character):
    """Test that setting a character skill with an invalid level returns a 422 status code."""
    # Level too low
    skill_level = {"level": 0}
    response = client.put(
        f"/character/{existing_character.character_id}/skill/SKILL:SLASH",
        json=skill_level
    )
    assert response.status_code == 422
    
    # Level too high
    skill_level = {"level": 6}
    response = client.put(
        f"/character/{existing_character.character_id}/skill/SKILL:SLASH",
        json=skill_level
    )
    assert response.status_code == 422

def test_set_character_skill_with_non_existent_skill_returns_404(client, existing_character):
    """Test that setting a non-existent skill returns a 404 status code."""
    skill_level = {"level": 1}
    response = client.put(
        f"/character/{existing_character.character_id}/skill/SKILL:NON_EXISTENT",
        json=skill_level
    )
    assert response.status_code == 404
    assert response.json() == {"detail": "Skill not found"}

def test_set_character_skill_with_non_existent_character_returns_404(client):
    """Test that setting a skill for a non-existent character returns a 404 status code."""
    skill_level = {"level": 1}
    response = client.put(
        "/character/CHAR:NON_EXISTENT/skill/SKILL:SLASH",
        json=skill_level
    )
    assert response.status_code == 404
    assert response.json() == {"detail": "Character not found"}

def test_set_character_skill_with_insufficient_level_returns_422(client, existing_character):
    """Test that setting a skill for a character with insufficient level returns a 422 status code."""
    # Set character level to 1
    client.put(
        f"/character/{existing_character.character_id}",
        json={
            "name": existing_character.name,
            "class_type": existing_character.class_type,
            "level": 1,
            "race": existing_character.race
        }
    )
    
    # Try to set a skill that requires level 3
    skill_level = {"level": 1}
    response = client.put(
        f"/character/{existing_character.character_id}/skill/SKILL:FIREBALL",
        json=skill_level
    )
    assert response.status_code == 422
    assert "detail" in response.json()
    assert "message" in response.json()["detail"]
    assert "required_level" in response.json()["detail"]
    assert "character_level" in response.json()["detail"]

def test_delete_character_skill_returns_200(client, existing_character):
    """Test that deleting a character skill returns a 200 status code."""
    # First, set a skill
    skill_level = {"level": 1}
    client.put(
        f"/character/{existing_character.character_id}/skill/SKILL:SLASH",
        json=skill_level
    )
    
    # Then delete it
    response = client.delete(
        f"/character/{existing_character.character_id}/skill/SKILL:SLASH"
    )
    assert response.status_code == 200

def test_delete_character_skill_removes_skill(client, existing_character):
    """Test that deleting a character skill removes it from the character."""
    # First, set a skill
    skill_level = {"level": 1}
    client.put(
        f"/character/{existing_character.character_id}/skill/SKILL:SLASH",
        json=skill_level
    )
    
    # Then delete it
    client.delete(
        f"/character/{existing_character.character_id}/skill/SKILL:SLASH"
    )
    
    # Check that it's gone
    response = client.get(f"/character/{existing_character.character_id}/skills")
    skills = response.json()
    
    assert "skills" in skills
    assert "SKILL:SLASH" not in skills["skills"]

def test_delete_non_existent_character_skill_returns_404(client, existing_character):
    """Test that deleting a non-existent character skill returns a 404 status code."""
    response = client.delete(
        f"/character/{existing_character.character_id}/skill/SKILL:NON_EXISTENT"
    )
    assert response.status_code == 404
    assert response.json() == {"detail": "Skill not found"}

def test_delete_skill_from_non_existent_character_returns_404(client):
    """Test that deleting a skill from a non-existent character returns a 404 status code."""
    response = client.delete(
        "/character/CHAR:NON_EXISTENT/skill/SKILL:SLASH"
    )
    assert response.status_code == 404
    assert response.json() == {"detail": "Character not found"}
```

## Running the Tests (RED)

Let's run our tests to see them fail:

```bash
pytest tests/test_character_skills.py -v
```

All our tests should fail because we haven't implemented the character skills endpoints yet.

## Implementing Character Skills Endpoints (GREEN)

Let's create a new file for character skills endpoints:

```bash
# Create a new file for character skills endpoints
touch rpg_character_db/api/endpoints/character_skills.py
```

```python
# rpg_character_db/api/endpoints/character_skills.py
from fastapi import APIRouter, Depends, HTTPException, Path, Body

from rpg_character_db.models.character import Character
from rpg_character_db.models.skill import SkillLevel, CharacterSkills
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.repositories.interfaces.skill_repository import ISkillRepository
from rpg_character_db.dependencies import get_character_repository, get_skill_repository
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter(tags=["character skills"])

def get_character_and_skill(
    character_id: str,
    skill_id: str,
    character_repository: ICharacterRepository,
    skill_repository: ISkillRepository
):
    """
    Get a character and skill by their IDs.
    
    Args:
        character_id: The character ID
        skill_id: The skill ID
        character_repository: The character repository
        skill_repository: The skill repository
        
    Returns:
        A tuple of (character, skill)
        
    Raises:
        HTTPException: If the character or skill is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character
    character = character_repository.get(character_id)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    # Get the skill
    skill = skill_repository.get(skill_id)
    
    # Check if the skill exists
    if skill is None:
        raise HTTPException(status_code=404, detail="Skill not found")
    
    return character, skill

@router.get("/character/{character_id}/skills", response_model=CharacterSkills)
async def get_character_skills(
    character_id: str = Path(..., description="The ID of the character"),
    character_repository: ICharacterRepository = Depends(get_character_repository)
):
    """
    Get all skills for a character.
    
    Args:
        character_id: The character ID
        character_repository: The character repository
        
    Returns:
        The character's skills
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character
    character = character_repository.get(character_id)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return character.skills

@router.put("/character/{character_id}/skill/{skill_id}", response_model=Character)
async def set_character_skill(
    character_id: str = Path(..., description="The ID of the character"),
    skill_id: str = Path(..., description="The ID of the skill"),
    skill_level: SkillLevel = Body(..., description="The skill level"),
    character_repository: ICharacterRepository = Depends(get_character_repository),
    skill_repository: ISkillRepository = Depends(get_skill_repository)
):
    """
    Set a skill level for a character.
    
    Args:
        character_id: The character ID
        skill_id: The skill ID
        skill_level: The skill level
        character_repository: The character repository
        skill_repository: The skill repository
        
    Returns:
        The updated character
        
    Raises:
        HTTPException: If the character or skill is not found, or if the character doesn't meet the level requirement
    """
    # Get the character and skill
    character, skill = get_character_and_skill(
        character_id, skill_id, character_repository, skill_repository
    )
    
    # Check if the character meets the level requirement
    if character.level < skill.required_level:
        raise HTTPException(
            status_code=422,
            detail={
                "message": "Character doesn't meet the level requirement for this skill",
                "required_level": skill.required_level,
                "character_level": character.level
            }
        )
    
    # Create a character skill
    character_skill = {
        "skill_id": skill.skill_id,
        "name": skill.name,
        "description": skill.description,
        "required_level": skill.required_level,
        "level": skill_level.level
    }
    
    # Update the character's skills
    character_dict = character.dict()
    if "skills" not in character_dict:
        character_dict["skills"] = {"skills": {}}
    
    character_dict["skills"]["skills"][skill_id] = character_skill
    
    # Update the character in the database
    updated_character = character_repository.update(character_id, character_dict)
    
    return updated_character

@router.delete("/character/{character_id}/skill/{skill_id}", response_model=Character)
async def delete_character_skill(
    character_id: str = Path(..., description="The ID of the character"),
    skill_id: str = Path(..., description="The ID of the skill"),
    character_repository: ICharacterRepository = Depends(get_character_repository),
    skill_repository: ISkillRepository = Depends(get_skill_repository)
):
    """
    Remove a skill from a character.
    
    Args:
        character_id: The character ID
        skill_id: The skill ID
        character_repository: The character repository
        skill_repository: The skill repository
        
    Returns:
        The updated character
        
    Raises:
        HTTPException: If the character or skill is not found
    """
    # Get the character and skill
    character, skill = get_character_and_skill(
        character_id, skill_id, character_repository, skill_repository
    )
    
    # Check if the character has the skill
    character_dict = character.dict()
    if (
        "skills" not in character_dict
        or "skills" not in character_dict["skills"]
        or skill_id not in character_dict["skills"]["skills"]
    ):
        raise HTTPException(status_code=404, detail="Character does not have this skill")
    
    # Remove the skill from the character
    del character_dict["skills"]["skills"][skill_id]
    
    # Update the character in the database
    updated_character = character_repository.update(character_id, character_dict)
    
    return updated_character
```

Now, let's update our router registry to include the new endpoints:

```python
# rpg_character_db/api/router.py
from fastapi import APIRouter

from rpg_character_db.api.endpoints import (
    character_retrieval,
    character_creation,
    character_update,
    character_listing,
    character_attributes,
    character_skills
)

# Create a main router
api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(character_retrieval.router)
api_router.include_router(character_creation.router)
api_router.include_router(character_update.router)
api_router.include_router(character_listing.router)
api_router.include_router(character_attributes.router)
api_router.include_router(character_skills.router)
```

## Running the Tests Again (GREEN)

Let's run our tests again to see if they pass:

```bash
pytest tests/test_character_skills.py -v
```

All our tests should now pass! We've successfully implemented character skills management.

## Refactoring

Our implementation is working, but there's room for improvement. Let's refactor to make our code more maintainable:

1. We should extract the validation logic for character level requirements to a separate function
2. We should improve error handling for skill operations

Let's update our character skills endpoints:

```python
# rpg_character_db/api/endpoints/character_skills.py
from fastapi import APIRouter, Depends, HTTPException, Path, Body

from rpg_character_db.models.character import Character
from rpg_character_db.models.skill import Skill, SkillLevel, CharacterSkills
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.repositories.interfaces.skill_repository import ISkillRepository
from rpg_character_db.dependencies import get_character_repository, get_skill_repository
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter(tags=["character skills"])

def get_character_and_skill(
    character_id: str,
    skill_id: str,
    character_repository: ICharacterRepository,
    skill_repository: ISkillRepository
):
    """
    Get a character and skill by their IDs.
    
    Args:
        character_id: The character ID
        skill_id: The skill ID
        character_repository: The character repository
        skill_repository: The skill repository
        
    Returns:
        A tuple of (character, skill)
        
    Raises:
        HTTPException: If the character or skill is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character
    character = character_repository.get(character_id)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    # Get the skill
    skill = skill_repository.get(skill_id)
    
    # Check if the skill exists
    if skill is None:
        raise HTTPException(status_code=404, detail="Skill not found")
    
    return character, skill

def validate_character_level_for_skill(character, skill):
    """
    Validate that a character meets the level requirement for a skill.
    
    Args:
        character: The character
        skill: The skill
        
    Raises:
        HTTPException: If the character doesn't meet the level requirement
    """
    if character.level < skill.required_level:
        raise HTTPException(
            status_code=422,
            detail={
                "message": "Character doesn't meet the level requirement for this skill",
                "required_level": skill.required_level,
                "character_level": character.level
            }
        )

@router.get("/character/{character_id}/skills", response_model=CharacterSkills)
async def get_character_skills(
    character_id: str = Path(..., description="The ID of the character"),
    character_repository: ICharacterRepository = Depends(get_character_repository)
):
    """
    Get all skills for a character.
    
    Args:
        character_id: The character ID
        character_repository: The character repository
        
    Returns:
        The character's skills
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character
    character = character_repository.get(character_id)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return character.skills

@router.put("/character/{character_id}/skill/{skill_id}", response_model=Character)
async def set_character_skill(
    character_id: str = Path(..., description="The ID of the character"),
    skill_id: str = Path(..., description="The ID of the skill"),
    skill_level: SkillLevel = Body(..., description="The skill level"),
    character_repository: ICharacterRepository = Depends(get_character_repository),
    skill_repository: ISkillRepository = Depends(get_skill_repository)
):
    """
    Set a skill level for a character.
    
    Args:
        character_id: The character ID
        skill_id: The skill ID
        skill_level: The skill level
        character_repository: The character repository
        skill_repository: The skill repository
        
    Returns:
        The updated character
        
    Raises:
        HTTPException: If the character or skill is not found, or if the character doesn't meet the level requirement
    """
    # Get the character and skill
    character, skill = get_character_and_skill(
        character_id, skill_id, character_repository, skill_repository
    )
    
    # Validate that the character meets the level requirement
    validate_character_level_for_skill(character, skill)
    
    # Create a character skill
    character_skill = {
        "skill_id": skill.skill_id,
        "name": skill.name,
        "description": skill.description,
        "required_level": skill.required_level,
        "level": skill_level.level
    }
    
    # Update the character's skills
    character_dict = character.dict()
    if "skills" not in character_dict:
        character_dict["skills"] = {"skills": {}}
    
    character_dict["skills"]["skills"][skill_id] = character_skill
    
    # Update the character in the database
    updated_character = character_repository.update(character_id, character_dict)
    
    return updated_character

@router.delete("/character/{character_id}/skill/{skill_id}", response_model=Character)
async def delete_character_skill(
    character_id: str = Path(..., description="The ID of the character"),
    skill_id: str = Path(..., description="The ID of the skill"),
    character_repository: ICharacterRepository = Depends(get_character_repository),
    skill_repository: ISkillRepository = Depends(get_skill_repository)
):
    """
    Remove a skill from a character.
    
    Args:
        character_id: The character ID
        skill_id: The skill ID
        character_repository: The character repository
        skill_repository: The skill repository
        
    Returns:
        The updated character
        
    Raises:
        HTTPException: If the character or skill is not found
    """
    # Get the character and skill
    character, skill = get_character_and_skill(
        character_id, skill_id, character_repository, skill_repository
    )
    
    # Check if the character has the skill
    character_dict = character.dict()
    if (
        "skills" not in character_dict
        or "skills" not in character_dict["skills"]
        or skill_id not in character_dict["skills"]["skills"]
    ):
        raise HTTPException(status_code=404, detail="Character does not have this skill")
    
    # Remove the skill from the character
    del character_dict["skills"]["skills"][skill_id]
    
    # Update the character in the database
    updated_character = character_repository.update(character_id, character_dict)
    
    return updated_character
```

## Running the Tests After Refactoring

Let's run our tests one more time to ensure our refactoring didn't break anything:

```bash
pytest tests/test_character_skills.py -v
```

All tests should still pass, confirming that our refactoring was successful.

## Committing Our Changes

Let's commit our changes to Git:

```bash
git add .
git commit -m "[RED/GREEN/REFACTOR] Implement character skills management"
```

## Conclusion

In this lesson, we've:
1. Created models for skills and character skills
2. Implemented endpoints for managing character skills
3. Written tests for skill management functionality
4. Refactored our code for better maintainability

We've continued to follow the RED-GREEN-REFACTOR cycle, letting our tests drive the development of our API.

In the next lesson, we'll learn about database integration and how to switch from our in-memory database to a real database.

Are there any questions about what we've covered today?

Thank you for your attention, and I'll see you in the next lesson!
