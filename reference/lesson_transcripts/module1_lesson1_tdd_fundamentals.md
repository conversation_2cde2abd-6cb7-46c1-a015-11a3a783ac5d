# Module 1, Lesson 1: TDD Fundamentals

## Introduction

Hello everyone, and welcome to our course on Test-Driven Development in Python! I'm excited to guide you through this journey of building a robust RPG Character Database API using TDD principles.

Today, we're going to start with the fundamentals of Test-Driven Development, or TDD as it's commonly known. This lesson will lay the groundwork for everything we'll be doing throughout this course.

## What is Test-Driven Development?

Test-Driven Development is a software development approach where you write tests before writing the actual code. This might seem counterintuitive at first - how can you test something that doesn't exist yet? But that's exactly the point!

TDD flips the traditional development process on its head. Instead of writing code and then testing it, we define what our code should do by writing tests first, then we write the minimal code needed to pass those tests.

The core idea is that by thinking about how your code should behave before you write it, you create more focused, purposeful code that meets specific requirements.

## The RED-GREEN-REFACTOR Cycle

The heart of TDD is a simple three-step cycle known as RED-GREEN-REFACTOR:

1. **RED**: Write a failing test that defines what you want your code to do. This test should fail because you haven't implemented the functionality yet.

2. **GREEN**: Write the minimal amount of code needed to make the test pass. Don't worry about elegance or optimization at this stage - just get it working.

3. **REFACTOR**: Now that your test is passing, improve your code without changing its behavior. Clean up, optimize, and make it more maintainable.

Then you repeat this cycle for each new piece of functionality you want to add.

Let me illustrate this with a simple example:

Imagine we want to create a function that adds two numbers. In TDD, we'd start by writing a test:

```python
def test_add_two_numbers():
    result = add(2, 3)
    assert result == 5
```

When we run this test, it will fail (RED) because we haven't defined the `add` function yet.

Next, we write the minimal code to make it pass:

```python
def add(a, b):
    return 5  # Hardcoded just to make the test pass
```

This is deliberately simplistic - we're just trying to make the test pass (GREEN).

Now we refactor to make the code actually do what it's supposed to:

```python
def add(a, b):
    return a + b
```

The test still passes, but now our code is properly implemented.

## Benefits of TDD in Software Development

Why should we use TDD? There are several compelling benefits:

1. **Clearer Requirements**: Writing tests first forces you to clarify what you want your code to do before you start writing it.

2. **Better Design**: TDD naturally leads to more modular, loosely coupled code because testable code tends to be well-structured.

3. **Fewer Bugs**: By constantly testing your code as you develop it, you catch bugs early when they're easier to fix.

4. **Built-in Documentation**: Your tests serve as living documentation of how your code is supposed to work.

5. **Confidence in Changes**: With a comprehensive test suite, you can refactor or extend your code with confidence, knowing that your tests will catch any regressions.

6. **Faster Development (Long-term)**: While TDD might seem slower initially, it often leads to faster development in the long run because you spend less time debugging and fixing issues.

## TDD Best Practices

To get the most out of TDD, here are some best practices to follow:

1. **Keep Tests Small and Focused**: Each test should verify one specific behavior or aspect of your code.

2. **Make Tests Independent**: Tests should not depend on each other or on external systems that might change.

3. **Follow the AAA Pattern**: Arrange (set up the test conditions), Act (perform the action being tested), Assert (verify the expected outcome).

4. **Test Behavior, Not Implementation**: Focus on what your code should do, not how it does it.

5. **Run Tests Frequently**: Ideally after every small change, to catch issues immediately.

6. **Don't Skip Refactoring**: The refactor step is crucial for maintaining code quality.

7. **Maintain Your Test Suite**: Tests are code too and need to be kept clean and up-to-date.

## Challenges when using TDD
1. Starting from code instead of test - it is really dificult at start to write a test when you dont even know how to write real code
2. Planning what we actually want to implement. Often ppl discover requirements while coding while to start from test you need to precisly define what you are expecting from the code
3. We need experience with writing tests

## The biggest TDD mistakes
1. Not using TDD, just writing tests after implementing the feature. There are flew problems with that, let's call it "classic automated testing", approach.
 - we don't know if our tests will fail if we break the feature. Usually, when our code is ready, and we write test for it, we write passing test. So we newer see a failed tests. That's the game changer. You don't know if your test actually testing anything. If you don't see if it is failing. If something is wrong, so that why we need to start from the test to see if the missing feature actually makes the test failed.
 - when you start from code then it is less testable. Writing a good code is hard, and when we do it classic way, often we don't thing about denepndecies in our code, or if we think about it, we tend to ignore some of them. That makes code difficult to test. Then in practice we spend more time on writing tests for a bad code, becasue usually it forces us to pach those unmanaged dependencies. Instead of nice flew lines test we have 100 lies of code that just makes our code executalbe in test env, and I'm not event talking about writing assertions for all those patches and finally the result

2. After writing a test we try to write perfect code from start. Thats huge mistake. One of our goals is to make as much informed decisions as possible. It is not possible to decide where to put your code if you just have first flew lines. Designing project structure, class inheritance etc at that stage is just making random decision. And whats worst, its more dificult to change decision that was once made. We will make that easier with this course, but often it's not about our code, its more about ourselfs.

3. Ppl skip refactoring or doing it too little. For one of my example projects, fully done in TDD, I did 11 green phases, 12 red phases and 34 refactoring ones. The power in TDD lies not in heaving great code coverage, it lies in ability to refactor your code.
You might find if even more precious in a age of AI, becasue we are able to generate tone of code in minutes, but this code is usually unmaintainable on the long run (and I'm talking about months/years scope). TDD alows you to do a lot of refactoring and therfore making you code easy to change in every stage. You can say that project written in TDD is always in a greenfield phase.

## TDD in Our RPG Character Database Project

Throughout this course, we'll be applying TDD principles to build a RESTful API for an RPG Character Database using FastAPI and Python. We'll start with simple tests for basic functionality and gradually build up to a complete, production-ready application.

By the end of the course, you'll have experienced the full TDD workflow and seen how it helps create robust, well-tested code.

## Conclusion

Today we've covered the fundamental concepts of Test-Driven Development:
- What TDD is and why it's valuable
- The RED-GREEN-REFACTOR cycle
- Benefits of using TDD
- Best practices for effective TDD

In our next lesson, we'll set up our development environment and project structure, preparing everything we need to start applying TDD to our RPG Character Database project.

Are there any questions before we move on?

Thank you for your attention, and I look forward to our TDD journey together!
