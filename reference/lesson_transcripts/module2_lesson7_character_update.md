# Module 2, Lesson 7: Character Update

## Introduction

Welcome back to our TDD course! In our previous lessons, we implemented character retrieval and creation functionality. Today, we'll expand our API by implementing character update, which will allow clients to modify existing characters in our database.

As always, we'll follow the RED-GREEN-REFACTOR cycle to drive our development. We'll start by writing tests for character update, then implement the functionality to make those tests pass, and finally refactor our code for better maintainability.

## Writing Tests for Character Update

Let's create a new test file specifically for character update:

```bash
# Create a new test file for character update
touch tests/test_update_character.py
```

Now, let's write tests for our character update endpoint:

```python
# tests/test_update_character.py
import pytest
from fastapi.testclient import TestClient

from rpg_character_db.main import app
from rpg_character_db.db import db
from rpg_character_db.models.character import Character
from rpg_character_db.repositories.character_repository import CharacterRepository

client = TestClient(app)

@pytest.fixture(autouse=True)
def clear_db():
    """Clear the database before each test."""
    db.clear()
    yield
    db.clear()

@pytest.fixture
def existing_character():
    """Create an existing character for testing."""
    character = Character(
        character_id="CHAR:TEST",
        name="Test Character",
        class_type="Warrior",
        level=1,
        race="Human"
    )
    db[character.character_id] = character.dict()
    return character

def test_update_non_existent_character_returns_404():
    """Test that updating a non-existent character returns a 404 status code."""
    update_data = {
        "name": "Updated Name",
        "class_type": "Mage",
        "level": 2,
        "race": "Elf"
    }
    
    response = client.put("/character/CHAR:NON_EXISTENT", json=update_data)
    assert response.status_code == 404
    assert response.json() == {"detail": "Character not found"}

def test_update_character_with_invalid_id_format_returns_422():
    """Test that updating a character with an invalid ID format returns a 422 status code."""
    update_data = {
        "name": "Updated Name",
        "class_type": "Mage",
        "level": 2,
        "race": "Elf"
    }
    
    response = client.put("/character/invalid-format", json=update_data)
    assert response.status_code == 422
    assert "detail" in response.json()
    assert "message" in response.json()["detail"]
    assert response.json()["detail"]["message"] == "Invalid character ID format"

def test_update_character_returns_200(existing_character):
    """Test that updating a character returns a 200 status code."""
    update_data = {
        "name": "Updated Name",
        "class_type": "Mage",
        "level": 2,
        "race": "Elf"
    }
    
    response = client.put(f"/character/{existing_character.character_id}", json=update_data)
    assert response.status_code == 200

def test_update_character_returns_updated_character(existing_character):
    """Test that updating a character returns the updated character."""
    update_data = {
        "name": "Updated Name",
        "class_type": "Mage",
        "level": 2,
        "race": "Elf"
    }
    
    response = client.put(f"/character/{existing_character.character_id}", json=update_data)
    updated_character = response.json()
    
    # Check that the response includes the updated data
    assert updated_character["character_id"] == existing_character.character_id
    assert updated_character["name"] == update_data["name"]
    assert updated_character["class_type"] == update_data["class_type"]
    assert updated_character["level"] == update_data["level"]
    assert updated_character["race"] == update_data["race"]

def test_update_character_updates_in_database(existing_character):
    """Test that updating a character updates it in the database."""
    update_data = {
        "name": "Updated Name",
        "class_type": "Mage",
        "level": 2,
        "race": "Elf"
    }
    
    response = client.put(f"/character/{existing_character.character_id}", json=update_data)
    
    # Check that the character is updated in the database
    updated_character = CharacterRepository.get(existing_character.character_id)
    assert updated_character is not None
    assert updated_character.name == update_data["name"]
    assert updated_character.class_type == update_data["class_type"]
    assert updated_character.level == update_data["level"]
    assert updated_character.race == update_data["race"]

def test_partial_update_character(existing_character):
    """Test that partially updating a character only updates the specified fields."""
    # Only update the name and level
    update_data = {
        "name": "Updated Name",
        "level": 2
    }
    
    response = client.patch(f"/character/{existing_character.character_id}", json=update_data)
    updated_character = response.json()
    
    # Check that only the specified fields were updated
    assert updated_character["character_id"] == existing_character.character_id
    assert updated_character["name"] == update_data["name"]
    assert updated_character["class_type"] == existing_character.class_type
    assert updated_character["level"] == update_data["level"]
    assert updated_character["race"] == existing_character.race
```

In these tests, we're:
1. Testing that updating a non-existent character returns a 404 Not Found status code
2. Testing that updating a character with an invalid ID format returns a 422 Unprocessable Entity status code
3. Testing that updating a character returns a 200 OK status code
4. Testing that the response includes the updated character data
5. Testing that the character is updated in the database
6. Testing partial updates with PATCH (only updating specified fields)

## Running the Tests (RED)

Let's run our tests to see them fail:

```bash
pytest tests/test_update_character.py -v
```

All our tests should fail because we haven't implemented the character update endpoints yet.

## Updating the Character Repository

Before we implement the endpoints, let's add update methods to our character repository:

```python
# rpg_character_db/repositories/character_repository.py
from typing import Dict, List, Optional

from rpg_character_db.db import db
from rpg_character_db.models.character import Character, CharacterCreate

class CharacterRepository:
    """Repository for character data."""
    
    @staticmethod
    def get(character_id: str) -> Optional[Character]:
        """
        Get a character by ID.
        
        Args:
            character_id: The character ID
            
        Returns:
            The character if found, None otherwise
        """
        if character_id not in db:
            return None
        
        return Character(**db[character_id])
    
    @staticmethod
    def create(character_create: CharacterCreate) -> Character:
        """
        Create a new character.
        
        Args:
            character_create: The character data
            
        Returns:
            The created character
        """
        character = Character.create(character_create)
        db[character.character_id] = character.dict()
        return character
    
    @staticmethod
    def update(character_id: str, character_update: Dict) -> Optional[Character]:
        """
        Update a character.
        
        Args:
            character_id: The character ID
            character_update: The character data to update
            
        Returns:
            The updated character if found, None otherwise
        """
        if character_id not in db:
            return None
        
        # Get the current character data
        character_data = db[character_id]
        
        # Update the character data
        character_data.update(character_update)
        
        # Store the updated character
        db[character_id] = character_data
        
        return Character(**character_data)
    
    @staticmethod
    def partial_update(character_id: str, character_update: Dict) -> Optional[Character]:
        """
        Partially update a character.
        
        Args:
            character_id: The character ID
            character_update: The character data to update
            
        Returns:
            The updated character if found, None otherwise
        """
        if character_id not in db:
            return None
        
        # Get the current character data
        character_data = db[character_id].copy()
        
        # Update only the specified fields
        for key, value in character_update.items():
            if key in character_data:
                character_data[key] = value
        
        # Store the updated character
        db[character_id] = character_data
        
        return Character(**character_data)
    
    @staticmethod
    def list() -> List[Character]:
        """
        List all characters.
        
        Returns:
            A list of all characters
        """
        return [Character(**data) for data in db.values()]
```

We've added two new methods:
1. `update`: Completely replaces a character's data with new data
2. `partial_update`: Updates only the specified fields of a character

## Implementing the Character Update Endpoints (GREEN)

Now, let's implement the character update endpoints:

```python
# rpg_character_db/api/character_routes.py
from fastapi import APIRouter, HTTPException, Path, status, Body
import re
from typing import Dict

from rpg_character_db.models.character import Character, CharacterCreate
from rpg_character_db.repositories.character_repository import CharacterRepository

router = APIRouter(prefix="/character", tags=["characters"])

# ... existing code ...

@router.put("/{character_id}", response_model=Character)
async def update_character(
    character_id: str = Path(..., description="The ID of the character to update"),
    character_update: CharacterCreate = Body(..., description="The updated character data")
):
    """
    Update a character.
    
    Args:
        character_id: The character ID
        character_update: The updated character data
        
    Returns:
        The updated character
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Update the character
    updated_character = CharacterRepository.update(character_id, character_update.dict())
    
    # Check if the character exists
    if updated_character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return updated_character

@router.patch("/{character_id}", response_model=Character)
async def partial_update_character(
    character_id: str = Path(..., description="The ID of the character to update"),
    character_update: Dict = Body(..., description="The character data to update")
):
    """
    Partially update a character.
    
    Args:
        character_id: The character ID
        character_update: The character data to update
        
    Returns:
        The updated character
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Partially update the character
    updated_character = CharacterRepository.partial_update(character_id, character_update)
    
    # Check if the character exists
    if updated_character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return updated_character
```

In this implementation, we've:
1. Added a PUT endpoint for complete updates
2. Added a PATCH endpoint for partial updates
3. Used our repository methods to perform the updates
4. Validated the character ID format
5. Returned a 404 error if the character doesn't exist

## Running the Tests Again (GREEN)

Let's run our tests again to see if they pass:

```bash
pytest tests/test_update_character.py -v
```

All our tests should now pass! We've successfully implemented character update functionality.

## Refactoring

Our implementation is working, but there's room for improvement. Let's refactor to make our code more maintainable:

1. We should add validation for partial updates
2. We should improve error handling

Let's update our character models to support partial updates:

```python
# rpg_character_db/models/character.py
from pydantic import BaseModel, Field, validator
import uuid
from typing import Optional

class CharacterBase(BaseModel):
    """Base model for character data."""
    name: str = Field(..., description="Character name")
    class_type: str = Field(..., description="Character class (e.g., Warrior, Mage)")
    level: int = Field(..., description="Character level", ge=1)
    race: str = Field(..., description="Character race (e.g., Human, Elf)")

class CharacterCreate(CharacterBase):
    """Model for creating a new character."""
    pass

class CharacterUpdate(CharacterBase):
    """Model for updating a character."""
    pass

class CharacterPartialUpdate(BaseModel):
    """Model for partially updating a character."""
    name: Optional[str] = Field(None, description="Character name")
    class_type: Optional[str] = Field(None, description="Character class (e.g., Warrior, Mage)")
    level: Optional[int] = Field(None, description="Character level", ge=1)
    race: Optional[str] = Field(None, description="Character race (e.g., Human, Elf)")
    
    @validator('*', pre=True)
    def check_not_empty(cls, v):
        """Validate that at least one field is provided."""
        if v == "":
            return None
        return v
    
    class Config:
        validate_assignment = True

class Character(CharacterBase):
    """Model representing an RPG character."""
    character_id: str = Field(..., description="Unique identifier for the character")
    
    @classmethod
    def create(cls, character_create: CharacterCreate) -> "Character":
        """Create a new character from a CharacterCreate model."""
        return cls(
            character_id=f"CHAR:{uuid.uuid4().hex[:8]}",
            **character_create.dict()
        )
```

We've made several improvements:
1. We've created a `CharacterBase` model with common fields
2. We've created a `CharacterPartialUpdate` model for partial updates
3. We've added validation to ensure that at least one field is provided for partial updates

Now, let's update our character routes to use these models:

```python
# rpg_character_db/api/character_routes.py
from fastapi import APIRouter, HTTPException, Path, status, Body
import re

from rpg_character_db.models.character import Character, CharacterCreate, CharacterUpdate, CharacterPartialUpdate
from rpg_character_db.repositories.character_repository import CharacterRepository

router = APIRouter(prefix="/character", tags=["characters"])

# ... existing code ...

@router.put("/{character_id}", response_model=Character)
async def update_character(
    character_id: str = Path(..., description="The ID of the character to update"),
    character_update: CharacterUpdate = Body(..., description="The updated character data")
):
    """
    Update a character.
    
    Args:
        character_id: The character ID
        character_update: The updated character data
        
    Returns:
        The updated character
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Update the character
    updated_character = CharacterRepository.update(character_id, character_update.dict())
    
    # Check if the character exists
    if updated_character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return updated_character

@router.patch("/{character_id}", response_model=Character)
async def partial_update_character(
    character_id: str = Path(..., description="The ID of the character to update"),
    character_update: CharacterPartialUpdate = Body(..., description="The character data to update")
):
    """
    Partially update a character.
    
    Args:
        character_id: The character ID
        character_update: The character data to update
        
    Returns:
        The updated character
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Remove None values from the update data
    update_data = {k: v for k, v in character_update.dict().items() if v is not None}
    
    # Check if there's anything to update
    if not update_data:
        raise HTTPException(
            status_code=422,
            detail="No valid fields to update"
        )
    
    # Partially update the character
    updated_character = CharacterRepository.partial_update(character_id, update_data)
    
    # Check if the character exists
    if updated_character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return updated_character
```

We've made several improvements:
1. We're using our new models for validation
2. We're removing None values from partial update data
3. We're checking if there's anything to update in partial updates

## Running the Tests After Refactoring

Let's run our tests one more time to ensure our refactoring didn't break anything:

```bash
pytest tests/test_update_character.py -v
```

All tests should still pass, confirming that our refactoring was successful.

## Committing Our Changes

Let's commit our changes to Git:

```bash
git add .
git commit -m "[RED/GREEN/REFACTOR] Implement character update"
```

## Conclusion

In this lesson, we've:
1. Written tests for character update
2. Implemented complete and partial update endpoints
3. Added validation for update data
4. Refactored our code for better maintainability

We've continued to follow the RED-GREEN-REFACTOR cycle, letting our tests drive the development of our API.

In the next lesson, we'll focus on organizing our tests and improving our test fixtures to make our tests more maintainable.

Are there any questions about what we've covered today?

Thank you for your attention, and I'll see you in the next lesson!
