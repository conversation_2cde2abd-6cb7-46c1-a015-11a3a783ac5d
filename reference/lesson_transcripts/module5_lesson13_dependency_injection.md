# Module 5, Lesson 13: Dependency Injection with FastAPI

## Introduction

Welcome back to our TDD course! In our previous lesson, we implemented the repository pattern with basic dependency injection. Today, we'll dive deeper into FastAPI's dependency injection system and how we can leverage it to make our code even more testable and maintainable.

Dependency injection is a powerful technique that allows us to provide dependencies to our code rather than having the code create or find those dependencies itself. This makes our code more modular, testable, and flexible. FastAPI has a built-in dependency injection system that makes this easy to implement.

## Understanding FastAPI's Dependency Injection System

FastAPI's dependency injection system is based on the `Depends` class. When you declare a parameter with `Depends`, FastAPI will:

1. Call the dependency function to get the value
2. Pass that value as the parameter value
3. Cache the result for performance (unless you specify otherwise)

This system allows us to:

- Share logic across multiple endpoints
- Reuse database connections
- Enforce security, permissions, etc.
- Make testing easier by allowing dependency overrides

## Enhancing Our Dependency Injection

Let's enhance our dependency injection system to make it more flexible and testable. We'll create a more sophisticated system that allows us to:

1. Provide different repository implementations based on configuration
2. Override dependencies in tests
3. Use dependency scopes for better performance

Let's start by updating our dependencies module:

```python
# rpg_character_db/dependencies.py
from fastapi import Depends
from typing import Callable, Dict, Any, Optional

from rpg_character_db.db import db
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.repositories.implementations.character_repository import InMemoryCharacterRepository

# Repository factory functions
def get_db():
    """Get the database."""
    return db

def get_character_repository(db=Depends(get_db)) -> ICharacterRepository:
    """Get the character repository."""
    return InMemoryCharacterRepository(db)

# Dependency overrides
_character_repository_override: Optional[Callable[[], ICharacterRepository]] = None

def override_character_repository(override_func: Optional[Callable[[], ICharacterRepository]]):
    """
    Override the character repository dependency.
    
    Args:
        override_func: A function that returns a character repository implementation,
                      or None to clear the override
    """
    global _character_repository_override
    _character_repository_override = override_func

def get_character_repository_with_override(
    repo: ICharacterRepository = Depends(get_character_repository)
) -> ICharacterRepository:
    """
    Get the character repository with override support.
    
    This function checks if there's an override for the character repository.
    If there is, it uses that instead of the default implementation.
    
    Args:
        repo: The default character repository from the regular dependency
        
    Returns:
        The character repository (either the override or the default)
    """
    if _character_repository_override is not None:
        return _character_repository_override()
    return repo
```

Now, let's update our API endpoints to use the enhanced dependency injection:

```python
# rpg_character_db/api/endpoints/character_retrieval.py
from fastapi import APIRouter, Depends, HTTPException, Path

from rpg_character_db.models.character import Character
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.dependencies import get_character_repository_with_override
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter(tags=["characters"])

@router.get("/character/{character_id}", response_model=Character)
async def get_character_by_id(
    character_id: str = Path(..., description="The ID of the character to retrieve"),
    character_repository: ICharacterRepository = Depends(get_character_repository_with_override)
):
    """
    Get a character by ID.
    
    Args:
        character_id: The character ID
        character_repository: The character repository
        
    Returns:
        The character if found
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character from the repository
    character = character_repository.get(character_id)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return character
```

Let's update the other endpoints similarly.

## Writing Tests with Dependency Overrides

Now, let's write tests that use dependency overrides to provide mock repositories:

```python
# tests/test_dependency_injection.py
import pytest
from unittest.mock import MagicMock
from fastapi.testclient import TestClient

from rpg_character_db.main import app
from rpg_character_db.models.character import Character
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.dependencies import override_character_repository

@pytest.fixture
def mock_character_repository():
    """Create a mock character repository."""
    repository = MagicMock(spec=ICharacterRepository)
    
    # Configure the mock
    repository.get.return_value = Character(
        character_id="CHAR:TEST",
        name="Test Character",
        class_type="Warrior",
        level=1,
        race="Human"
    )
    
    return repository

@pytest.fixture
def client_with_mock_repository(mock_character_repository):
    """Create a test client with a mock character repository."""
    # Set up the override
    override_character_repository(lambda: mock_character_repository)
    
    # Create the client
    client = TestClient(app)
    
    # Yield the client for the test to use
    yield client
    
    # Clear the override after the test
    override_character_repository(None)

def test_get_character_by_id_with_mock_repository(client_with_mock_repository, mock_character_repository):
    """Test that getting a character by ID uses the mock repository."""
    # Make the request
    response = client_with_mock_repository.get("/character/CHAR:TEST")
    
    # Check the response
    assert response.status_code == 200
    assert response.json() == {
        "character_id": "CHAR:TEST",
        "name": "Test Character",
        "class_type": "Warrior",
        "level": 1,
        "race": "Human",
        "attributes": {"attributes": {}}
    }
    
    # Verify that the mock repository was called
    mock_character_repository.get.assert_called_once_with("CHAR:TEST")
```

## Using Dependency Injection for Cross-Cutting Concerns

Dependency injection is also useful for cross-cutting concerns like logging, authentication, and request validation. Let's implement a simple logging dependency:

```python
# rpg_character_db/dependencies.py
# ... existing code ...

# Logging dependency
def get_logger():
    """Get a logger."""
    import logging
    return logging.getLogger("rpg_character_db")
```

Now, we can use this logger in our endpoints:

```python
# rpg_character_db/api/endpoints/character_retrieval.py
from fastapi import APIRouter, Depends, HTTPException, Path
import logging

from rpg_character_db.models.character import Character
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.dependencies import get_character_repository_with_override, get_logger
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter(tags=["characters"])

@router.get("/character/{character_id}", response_model=Character)
async def get_character_by_id(
    character_id: str = Path(..., description="The ID of the character to retrieve"),
    character_repository: ICharacterRepository = Depends(get_character_repository_with_override),
    logger: logging.Logger = Depends(get_logger)
):
    """
    Get a character by ID.
    
    Args:
        character_id: The character ID
        character_repository: The character repository
        logger: The logger
        
    Returns:
        The character if found
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    logger.info(f"Getting character with ID: {character_id}")
    
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character from the repository
    character = character_repository.get(character_id)
    
    # Check if the character exists
    if character is None:
        logger.warning(f"Character not found: {character_id}")
        raise HTTPException(status_code=404, detail="Character not found")
    
    logger.info(f"Character found: {character.name}")
    return character
```

## Using Dependency Injection for Request Validation

We can also use dependency injection for request validation. Let's create a dependency for validating character IDs:

```python
# rpg_character_db/dependencies.py
# ... existing code ...

# Validation dependencies
def validate_character_id_dependency(character_id: str = Path(...)):
    """
    Validate a character ID.
    
    Args:
        character_id: The character ID to validate
        
    Returns:
        The validated character ID
        
    Raises:
        HTTPException: If the character ID is invalid
    """
    from rpg_character_db.utils.validation import validate_character_id
    validate_character_id(character_id)
    return character_id
```

Now, we can use this dependency in our endpoints:

```python
# rpg_character_db/api/endpoints/character_retrieval.py
from fastapi import APIRouter, Depends, HTTPException, Path
import logging

from rpg_character_db.models.character import Character
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.dependencies import (
    get_character_repository_with_override,
    get_logger,
    validate_character_id_dependency
)

router = APIRouter(tags=["characters"])

@router.get("/character/{character_id}", response_model=Character)
async def get_character_by_id(
    character_id: str = Depends(validate_character_id_dependency),
    character_repository: ICharacterRepository = Depends(get_character_repository_with_override),
    logger: logging.Logger = Depends(get_logger)
):
    """
    Get a character by ID.
    
    Args:
        character_id: The character ID
        character_repository: The character repository
        logger: The logger
        
    Returns:
        The character if found
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    logger.info(f"Getting character with ID: {character_id}")
    
    # Get the character from the repository
    character = character_repository.get(character_id)
    
    # Check if the character exists
    if character is None:
        logger.warning(f"Character not found: {character_id}")
        raise HTTPException(status_code=404, detail="Character not found")
    
    logger.info(f"Character found: {character.name}")
    return character
```

## Using Dependency Injection for Authentication

Dependency injection is also useful for authentication. Let's implement a simple authentication dependency:

```python
# rpg_character_db/dependencies.py
# ... existing code ...

# Authentication dependencies
def get_current_user(api_key: str = Depends(get_api_key)):
    """
    Get the current user based on the API key.
    
    Args:
        api_key: The API key
        
    Returns:
        The current user
        
    Raises:
        HTTPException: If the API key is invalid
    """
    from fastapi import HTTPException, status
    
    # In a real application, you would validate the API key against a database
    if api_key != "test_api_key":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Return the user
    return {"username": "test_user"}

def get_api_key(api_key: str = Header(..., alias="X-API-Key")):
    """
    Get the API key from the request header.
    
    Args:
        api_key: The API key
        
    Returns:
        The API key
    """
    return api_key
```

Now, we can use this dependency in our endpoints:

```python
# rpg_character_db/api/endpoints/character_creation.py
from fastapi import APIRouter, Depends, status

from rpg_character_db.models.character import Character, CharacterCreate
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.dependencies import get_character_repository_with_override, get_current_user

router = APIRouter(tags=["characters"])

@router.post("/character", response_model=Character, status_code=status.HTTP_201_CREATED)
async def create_character(
    character_create: CharacterCreate,
    character_repository: ICharacterRepository = Depends(get_character_repository_with_override),
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new character.
    
    Args:
        character_create: The character data
        character_repository: The character repository
        current_user: The current user
        
    Returns:
        The created character
    """
    # Create a new character using the repository
    return character_repository.create(character_create)
```

## Running the Tests

Let's run our tests to make sure everything works:

```bash
pytest tests/test_dependency_injection.py -v
```

All tests should pass! We've successfully enhanced our dependency injection system.

## Refactoring

Our implementation is working, but there's room for improvement. Let's refactor to make our code more maintainable:

1. We should create a base class for our dependencies to avoid duplicating code
2. We should improve error handling in our dependencies

Let's create a base class for our dependencies:

```python
# rpg_character_db/dependencies/base.py
from typing import Callable, Optional, TypeVar, Generic

T = TypeVar('T')

class DependencyWithOverride(Generic[T]):
    """Base class for dependencies with override support."""
    
    def __init__(self, default_factory: Callable[[], T]):
        """
        Initialize the dependency.
        
        Args:
            default_factory: A function that returns the default implementation
        """
        self.default_factory = default_factory
        self.override: Optional[Callable[[], T]] = None
    
    def set_override(self, override_func: Optional[Callable[[], T]]):
        """
        Set the override function.
        
        Args:
            override_func: A function that returns an implementation,
                          or None to clear the override
        """
        self.override = override_func
    
    def __call__(self) -> T:
        """
        Get the implementation.
        
        Returns:
            The implementation (either the override or the default)
        """
        if self.override is not None:
            return self.override()
        return self.default_factory()
```

Now, let's update our dependencies module to use this base class:

```python
# rpg_character_db/dependencies/__init__.py
from fastapi import Depends, Header
from typing import Dict, Any

from rpg_character_db.db import db
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.repositories.implementations.character_repository import InMemoryCharacterRepository
from rpg_character_db.dependencies.base import DependencyWithOverride

# Repository factory functions
def get_db():
    """Get the database."""
    return db

def _get_character_repository() -> ICharacterRepository:
    """Get the character repository."""
    return InMemoryCharacterRepository(db)

# Create dependency with override
character_repository_dependency = DependencyWithOverride(_get_character_repository)

# Convenience function for setting the override
def override_character_repository(override_func):
    """
    Override the character repository dependency.
    
    Args:
        override_func: A function that returns a character repository implementation,
                      or None to clear the override
    """
    character_repository_dependency.set_override(override_func)

# Dependency function for FastAPI
def get_character_repository() -> ICharacterRepository:
    """
    Get the character repository.
    
    Returns:
        The character repository
    """
    return character_repository_dependency()

# Validation dependencies
def validate_character_id_dependency(character_id: str = Header(...)):
    """
    Validate a character ID.
    
    Args:
        character_id: The character ID to validate
        
    Returns:
        The validated character ID
        
    Raises:
        HTTPException: If the character ID is invalid
    """
    from rpg_character_db.utils.validation import validate_character_id
    validate_character_id(character_id)
    return character_id

# Logging dependency
def get_logger():
    """Get a logger."""
    import logging
    return logging.getLogger("rpg_character_db")
```

## Committing Our Changes

Let's commit our changes to Git:

```bash
git add .
git commit -m "[REFACTOR] Enhance dependency injection with FastAPI"
```

## Conclusion

In this lesson, we've:
1. Enhanced our dependency injection system with override support
2. Used dependency injection for cross-cutting concerns like logging
3. Used dependency injection for request validation
4. Used dependency injection for authentication
5. Refactored our code for better maintainability

We've successfully leveraged FastAPI's dependency injection system to make our code more modular, testable, and maintainable. This will make it easier to add new features and switch to a real database in the future.

In the next lesson, we'll implement character skills management, which will allow us to assign skills to characters based on their level.

Are there any questions about what we've covered today?

Thank you for your attention, and I'll see you in the next lesson!
