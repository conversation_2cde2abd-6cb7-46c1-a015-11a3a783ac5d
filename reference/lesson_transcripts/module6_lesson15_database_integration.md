# Module 6, Lesson 15: Database Integration with SQLAlchemy

## Introduction

Welcome back to our TDD course! In our previous lessons, we've built a functional RPG Character Database API with character, attribute, and skill management. So far, we've been using an in-memory database for simplicity, but in a real-world application, we'd want to use a persistent database.

Today, we'll learn how to integrate our application with a real database using SQLAlchemy, a popular Python ORM (Object-Relational Mapping) library. We'll follow the same TDD approach, ensuring our tests continue to pass as we make this significant change to our application.

## Understanding SQLAlchemy

SQLAlchemy is a powerful SQL toolkit and ORM that provides a full suite of well-known enterprise-level persistence patterns. It offers two main components:

1. **Core**: A SQL abstraction toolkit that provides a way to interact with databases using Python expressions
2. **ORM**: An object-relational mapper that maps Python classes to database tables

For our application, we'll use both components:
- Core for database connection and schema definition
- ORM for mapping our models to database tables

## Setting Up SQLAlchemy

Let's start by installing SQLAlchemy and a database driver. For simplicity, we'll use SQLite, but the same principles apply to other databases like PostgreSQL or MySQL:

```bash
# Install SQLAlchemy and SQLite driver
pip install sqlalchemy
```

Now, let's create a database module:

```bash
# Create a directory for database modules
mkdir -p rpg_character_db/database
touch rpg_character_db/database/__init__.py
touch rpg_character_db/database/core.py
```

```python
# rpg_character_db/database/core.py
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# Create a SQLite database URL
SQLALCHEMY_DATABASE_URL = "sqlite:///./rpg_character_db.db"

# Create an engine
engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)

# Create a SessionLocal class
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create a Base class for declarative models
Base = declarative_base()

# Function to get a database session
def get_db():
    """
    Get a database session.
    
    Yields:
        A database session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

## Creating SQLAlchemy Models

Now, let's create SQLAlchemy models for our domain entities:

```bash
# Create a directory for SQLAlchemy models
mkdir -p rpg_character_db/database/models
touch rpg_character_db/database/models/__init__.py
touch rpg_character_db/database/models/character.py
touch rpg_character_db/database/models/attribute.py
touch rpg_character_db/database/models/skill.py
```

```python
# rpg_character_db/database/models/character.py
from sqlalchemy import Column, String, Integer, ForeignKey
from sqlalchemy.orm import relationship

from rpg_character_db.database.core import Base

class Character(Base):
    """SQLAlchemy model for characters."""
    __tablename__ = "characters"
    
    character_id = Column(String, primary_key=True, index=True)
    name = Column(String, index=True)
    class_type = Column(String)
    level = Column(Integer)
    race = Column(String)
    
    # Relationships
    attributes = relationship("CharacterAttribute", back_populates="character", cascade="all, delete-orphan")
    skills = relationship("CharacterSkill", back_populates="character", cascade="all, delete-orphan")
```

```python
# rpg_character_db/database/models/attribute.py
from sqlalchemy import Column, String, Integer, ForeignKey
from sqlalchemy.orm import relationship

from rpg_character_db.database.core import Base

class Attribute(Base):
    """SQLAlchemy model for attributes."""
    __tablename__ = "attributes"
    
    attribute_id = Column(String, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(String)

class CharacterAttribute(Base):
    """SQLAlchemy model for character attributes."""
    __tablename__ = "character_attributes"
    
    id = Column(Integer, primary_key=True, index=True)
    character_id = Column(String, ForeignKey("characters.character_id"))
    attribute_id = Column(String, ForeignKey("attributes.attribute_id"))
    value = Column(Integer)
    
    # Relationships
    character = relationship("Character", back_populates="attributes")
    attribute = relationship("Attribute")
```

```python
# rpg_character_db/database/models/skill.py
from sqlalchemy import Column, String, Integer, ForeignKey
from sqlalchemy.orm import relationship

from rpg_character_db.database.core import Base

class Skill(Base):
    """SQLAlchemy model for skills."""
    __tablename__ = "skills"
    
    skill_id = Column(String, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(String)
    required_level = Column(Integer)

class CharacterSkill(Base):
    """SQLAlchemy model for character skills."""
    __tablename__ = "character_skills"
    
    id = Column(Integer, primary_key=True, index=True)
    character_id = Column(String, ForeignKey("characters.character_id"))
    skill_id = Column(String, ForeignKey("skills.skill_id"))
    level = Column(Integer)
    
    # Relationships
    character = relationship("Character", back_populates="skills")
    skill = relationship("Skill")
```

## Creating Database Initialization Script

Let's create a script to initialize the database with some sample data:

```bash
# Create a script for database initialization
touch rpg_character_db/database/init_db.py
```

```python
# rpg_character_db/database/init_db.py
from sqlalchemy.orm import Session

from rpg_character_db.database.core import Base, engine
from rpg_character_db.database.models.character import Character
from rpg_character_db.database.models.attribute import Attribute
from rpg_character_db.database.models.skill import Skill

def init_db():
    """Initialize the database."""
    # Create tables
    Base.metadata.create_all(bind=engine)

def seed_db(db: Session):
    """
    Seed the database with sample data.
    
    Args:
        db: The database session
    """
    # Check if the database is already seeded
    if db.query(Attribute).count() > 0:
        return
    
    # Create attributes
    attributes = [
        Attribute(
            attribute_id="ATTR:STR",
            name="Strength",
            description="Physical power and carrying capacity"
        ),
        Attribute(
            attribute_id="ATTR:DEX",
            name="Dexterity",
            description="Agility, reflexes, and balance"
        ),
        Attribute(
            attribute_id="ATTR:CON",
            name="Constitution",
            description="Health, stamina, and vital force"
        ),
        Attribute(
            attribute_id="ATTR:INT",
            name="Intelligence",
            description="Mental acuity, information recall, analytical skill"
        ),
        Attribute(
            attribute_id="ATTR:WIS",
            name="Wisdom",
            description="Awareness, intuition, and insight"
        ),
        Attribute(
            attribute_id="ATTR:CHA",
            name="Charisma",
            description="Force of personality, persuasiveness, leadership"
        )
    ]
    
    # Create skills
    skills = [
        Skill(
            skill_id="SKILL:SLASH",
            name="Slash",
            description="A basic melee attack",
            required_level=1
        ),
        Skill(
            skill_id="SKILL:FIREBALL",
            name="Fireball",
            description="A powerful fire-based spell",
            required_level=3
        ),
        Skill(
            skill_id="SKILL:HEAL",
            name="Heal",
            description="Restore health to a target",
            required_level=2
        ),
        Skill(
            skill_id="SKILL:STEALTH",
            name="Stealth",
            description="Move silently and avoid detection",
            required_level=2
        ),
        Skill(
            skill_id="SKILL:DUAL_WIELD",
            name="Dual Wield",
            description="Fight with a weapon in each hand",
            required_level=5
        )
    ]
    
    # Add to session
    db.add_all(attributes)
    db.add_all(skills)
    
    # Commit changes
    db.commit()
```

## Implementing SQLAlchemy Repositories

Now, let's implement SQLAlchemy repositories for our domain entities:

```bash
# Create a directory for SQLAlchemy repositories
mkdir -p rpg_character_db/repositories/implementations/sqlalchemy
touch rpg_character_db/repositories/implementations/sqlalchemy/__init__.py
touch rpg_character_db/repositories/implementations/sqlalchemy/character_repository.py
touch rpg_character_db/repositories/implementations/sqlalchemy/attribute_repository.py
touch rpg_character_db/repositories/implementations/sqlalchemy/skill_repository.py
```

```python
# rpg_character_db/repositories/implementations/sqlalchemy/character_repository.py
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session

from rpg_character_db.models.character import Character as CharacterModel, CharacterCreate
from rpg_character_db.models.attribute import CharacterAttribute as CharacterAttributeModel
from rpg_character_db.models.skill import CharacterSkill as CharacterSkillModel
from rpg_character_db.database.models.character import Character as DBCharacter
from rpg_character_db.database.models.attribute import CharacterAttribute as DBCharacterAttribute
from rpg_character_db.database.models.skill import CharacterSkill as DBCharacterSkill
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository

class SQLAlchemyCharacterRepository(ICharacterRepository):
    """SQLAlchemy implementation of character repository."""
    
    def __init__(self, db: Session):
        """
        Initialize the repository.
        
        Args:
            db: The database session
        """
        self.db = db
    
    def get(self, character_id: str) -> Optional[CharacterModel]:
        """
        Get a character by ID.
        
        Args:
            character_id: The character ID
            
        Returns:
            The character if found, None otherwise
        """
        # Query the database
        db_character = self.db.query(DBCharacter).filter(DBCharacter.character_id == character_id).first()
        
        # Return None if not found
        if db_character is None:
            return None
        
        # Convert to domain model
        return self._db_to_domain(db_character)
    
    def create(self, character_create: CharacterCreate) -> CharacterModel:
        """
        Create a new character.
        
        Args:
            character_create: The character data
            
        Returns:
            The created character
        """
        # Create a domain model
        character = CharacterModel.create(character_create)
        
        # Create a database model
        db_character = DBCharacter(
            character_id=character.character_id,
            name=character.name,
            class_type=character.class_type,
            level=character.level,
            race=character.race
        )
        
        # Add to session
        self.db.add(db_character)
        self.db.commit()
        self.db.refresh(db_character)
        
        # Return the domain model
        return character
    
    def update(self, character_id: str, character_update: Dict[str, Any]) -> Optional[CharacterModel]:
        """
        Update a character.
        
        Args:
            character_id: The character ID
            character_update: The character data to update
            
        Returns:
            The updated character if found, None otherwise
        """
        # Query the database
        db_character = self.db.query(DBCharacter).filter(DBCharacter.character_id == character_id).first()
        
        # Return None if not found
        if db_character is None:
            return None
        
        # Update basic fields
        if "name" in character_update:
            db_character.name = character_update["name"]
        if "class_type" in character_update:
            db_character.class_type = character_update["class_type"]
        if "level" in character_update:
            db_character.level = character_update["level"]
        if "race" in character_update:
            db_character.race = character_update["race"]
        
        # Update attributes
        if "attributes" in character_update and "attributes" in character_update["attributes"]:
            # Clear existing attributes
            self.db.query(DBCharacterAttribute).filter(DBCharacterAttribute.character_id == character_id).delete()
            
            # Add new attributes
            for attribute_id, attribute in character_update["attributes"]["attributes"].items():
                db_attribute = DBCharacterAttribute(
                    character_id=character_id,
                    attribute_id=attribute_id,
                    value=attribute["value"]
                )
                self.db.add(db_attribute)
        
        # Update skills
        if "skills" in character_update and "skills" in character_update["skills"]:
            # Clear existing skills
            self.db.query(DBCharacterSkill).filter(DBCharacterSkill.character_id == character_id).delete()
            
            # Add new skills
            for skill_id, skill in character_update["skills"]["skills"].items():
                db_skill = DBCharacterSkill(
                    character_id=character_id,
                    skill_id=skill_id,
                    level=skill["level"]
                )
                self.db.add(db_skill)
        
        # Commit changes
        self.db.commit()
        self.db.refresh(db_character)
        
        # Return the domain model
        return self._db_to_domain(db_character)
    
    def partial_update(self, character_id: str, character_update: Dict[str, Any]) -> Optional[CharacterModel]:
        """
        Partially update a character.
        
        Args:
            character_id: The character ID
            character_update: The character data to update
            
        Returns:
            The updated character if found, None otherwise
        """
        # Query the database
        db_character = self.db.query(DBCharacter).filter(DBCharacter.character_id == character_id).first()
        
        # Return None if not found
        if db_character is None:
            return None
        
        # Update only the specified fields
        for key, value in character_update.items():
            if hasattr(db_character, key):
                setattr(db_character, key, value)
        
        # Commit changes
        self.db.commit()
        self.db.refresh(db_character)
        
        # Return the domain model
        return self._db_to_domain(db_character)
    
    def list(self) -> List[CharacterModel]:
        """
        List all characters.
        
        Returns:
            A list of all characters
        """
        # Query the database
        db_characters = self.db.query(DBCharacter).all()
        
        # Convert to domain models
        return [self._db_to_domain(db_character) for db_character in db_characters]
    
    def delete(self, character_id: str) -> bool:
        """
        Delete a character.
        
        Args:
            character_id: The character ID
            
        Returns:
            True if the character was deleted, False otherwise
        """
        # Query the database
        db_character = self.db.query(DBCharacter).filter(DBCharacter.character_id == character_id).first()
        
        # Return False if not found
        if db_character is None:
            return False
        
        # Delete the character
        self.db.delete(db_character)
        self.db.commit()
        
        return True
    
    def _db_to_domain(self, db_character: DBCharacter) -> CharacterModel:
        """
        Convert a database character to a domain character.
        
        Args:
            db_character: The database character
            
        Returns:
            The domain character
        """
        # Create a domain character
        character = CharacterModel(
            character_id=db_character.character_id,
            name=db_character.name,
            class_type=db_character.class_type,
            level=db_character.level,
            race=db_character.race
        )
        
        # Add attributes
        for db_attribute in db_character.attributes:
            attribute = {
                "attribute_id": db_attribute.attribute_id,
                "name": db_attribute.attribute.name,
                "description": db_attribute.attribute.description,
                "value": db_attribute.value
            }
            character.attributes.attributes[db_attribute.attribute_id] = CharacterAttributeModel(**attribute)
        
        # Add skills
        for db_skill in db_character.skills:
            skill = {
                "skill_id": db_skill.skill_id,
                "name": db_skill.skill.name,
                "description": db_skill.skill.description,
                "required_level": db_skill.skill.required_level,
                "level": db_skill.level
            }
            character.skills.skills[db_skill.skill_id] = CharacterSkillModel(**skill)
        
        return character
```

```python
# rpg_character_db/repositories/implementations/sqlalchemy/attribute_repository.py
from typing import List, Optional
from sqlalchemy.orm import Session

from rpg_character_db.models.attribute import Attribute as AttributeModel
from rpg_character_db.database.models.attribute import Attribute as DBAttribute
from rpg_character_db.repositories.interfaces.attribute_repository import IAttributeRepository

class SQLAlchemyAttributeRepository(IAttributeRepository):
    """SQLAlchemy implementation of attribute repository."""
    
    def __init__(self, db: Session):
        """
        Initialize the repository.
        
        Args:
            db: The database session
        """
        self.db = db
    
    def get(self, attribute_id: str) -> Optional[AttributeModel]:
        """
        Get an attribute by ID.
        
        Args:
            attribute_id: The attribute ID
            
        Returns:
            The attribute if found, None otherwise
        """
        # Query the database
        db_attribute = self.db.query(DBAttribute).filter(DBAttribute.attribute_id == attribute_id).first()
        
        # Return None if not found
        if db_attribute is None:
            return None
        
        # Convert to domain model
        return AttributeModel(
            attribute_id=db_attribute.attribute_id,
            name=db_attribute.name,
            description=db_attribute.description
        )
    
    def list(self) -> List[AttributeModel]:
        """
        List all attributes.
        
        Returns:
            A list of all attributes
        """
        # Query the database
        db_attributes = self.db.query(DBAttribute).all()
        
        # Convert to domain models
        return [
            AttributeModel(
                attribute_id=db_attribute.attribute_id,
                name=db_attribute.name,
                description=db_attribute.description
            )
            for db_attribute in db_attributes
        ]
    
    def delete(self, attribute_id: str) -> bool:
        """
        Delete an attribute.
        
        Args:
            attribute_id: The attribute ID
            
        Returns:
            True if the attribute was deleted, False otherwise
        """
        # Query the database
        db_attribute = self.db.query(DBAttribute).filter(DBAttribute.attribute_id == attribute_id).first()
        
        # Return False if not found
        if db_attribute is None:
            return False
        
        # Delete the attribute
        self.db.delete(db_attribute)
        self.db.commit()
        
        return True
```

```python
# rpg_character_db/repositories/implementations/sqlalchemy/skill_repository.py
from typing import List, Optional
from sqlalchemy.orm import Session

from rpg_character_db.models.skill import Skill as SkillModel
from rpg_character_db.database.models.skill import Skill as DBSkill
from rpg_character_db.repositories.interfaces.skill_repository import ISkillRepository

class SQLAlchemySkillRepository(ISkillRepository):
    """SQLAlchemy implementation of skill repository."""
    
    def __init__(self, db: Session):
        """
        Initialize the repository.
        
        Args:
            db: The database session
        """
        self.db = db
    
    def get(self, skill_id: str) -> Optional[SkillModel]:
        """
        Get a skill by ID.
        
        Args:
            skill_id: The skill ID
            
        Returns:
            The skill if found, None otherwise
        """
        # Query the database
        db_skill = self.db.query(DBSkill).filter(DBSkill.skill_id == skill_id).first()
        
        # Return None if not found
        if db_skill is None:
            return None
        
        # Convert to domain model
        return SkillModel(
            skill_id=db_skill.skill_id,
            name=db_skill.name,
            description=db_skill.description,
            required_level=db_skill.required_level
        )
    
    def list(self) -> List[SkillModel]:
        """
        List all skills.
        
        Returns:
            A list of all skills
        """
        # Query the database
        db_skills = self.db.query(DBSkill).all()
        
        # Convert to domain models
        return [
            SkillModel(
                skill_id=db_skill.skill_id,
                name=db_skill.name,
                description=db_skill.description,
                required_level=db_skill.required_level
            )
            for db_skill in db_skills
        ]
    
    def delete(self, skill_id: str) -> bool:
        """
        Delete a skill.
        
        Args:
            skill_id: The skill ID
            
        Returns:
            True if the skill was deleted, False otherwise
        """
        # Query the database
        db_skill = self.db.query(DBSkill).filter(DBSkill.skill_id == skill_id).first()
        
        # Return False if not found
        if db_skill is None:
            return False
        
        # Delete the skill
        self.db.delete(db_skill)
        self.db.commit()
        
        return True
```

## Updating Dependencies

Now, let's update our dependencies to use the SQLAlchemy repositories:

```python
# rpg_character_db/dependencies/__init__.py
from fastapi import Depends
from sqlalchemy.orm import Session

from rpg_character_db.database.core import get_db as get_sqlalchemy_db
from rpg_character_db.database.init_db import init_db, seed_db
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.repositories.interfaces.attribute_repository import IAttributeRepository
from rpg_character_db.repositories.interfaces.skill_repository import ISkillRepository
from rpg_character_db.repositories.implementations.sqlalchemy.character_repository import SQLAlchemyCharacterRepository
from rpg_character_db.repositories.implementations.sqlalchemy.attribute_repository import SQLAlchemyAttributeRepository
from rpg_character_db.repositories.implementations.sqlalchemy.skill_repository import SQLAlchemySkillRepository
from rpg_character_db.dependencies.base import DependencyWithOverride

# Initialize the database
init_db()

# Repository factory functions
def _get_character_repository(db: Session = Depends(get_sqlalchemy_db)) -> ICharacterRepository:
    """Get the character repository."""
    return SQLAlchemyCharacterRepository(db)

def _get_attribute_repository(db: Session = Depends(get_sqlalchemy_db)) -> IAttributeRepository:
    """Get the attribute repository."""
    return SQLAlchemyAttributeRepository(db)

def _get_skill_repository(db: Session = Depends(get_sqlalchemy_db)) -> ISkillRepository:
    """Get the skill repository."""
    return SQLAlchemySkillRepository(db)

# Create dependencies with override
character_repository_dependency = DependencyWithOverride(_get_character_repository)
attribute_repository_dependency = DependencyWithOverride(_get_attribute_repository)
skill_repository_dependency = DependencyWithOverride(_get_skill_repository)

# Convenience functions for setting overrides
def override_character_repository(override_func):
    """
    Override the character repository dependency.
    
    Args:
        override_func: A function that returns a character repository implementation,
                      or None to clear the override
    """
    character_repository_dependency.set_override(override_func)

def override_attribute_repository(override_func):
    """
    Override the attribute repository dependency.
    
    Args:
        override_func: A function that returns an attribute repository implementation,
                      or None to clear the override
    """
    attribute_repository_dependency.set_override(override_func)

def override_skill_repository(override_func):
    """
    Override the skill repository dependency.
    
    Args:
        override_func: A function that returns a skill repository implementation,
                      or None to clear the override
    """
    skill_repository_dependency.set_override(override_func)

# Dependency functions for FastAPI
def get_character_repository() -> ICharacterRepository:
    """
    Get the character repository.
    
    Returns:
        The character repository
    """
    return character_repository_dependency()

def get_attribute_repository() -> IAttributeRepository:
    """
    Get the attribute repository.
    
    Returns:
        The attribute repository
    """
    return attribute_repository_dependency()

def get_skill_repository() -> ISkillRepository:
    """
    Get the skill repository.
    
    Returns:
        The skill repository
    """
    return skill_repository_dependency()

# Seed the database on startup
def startup_db_seed(db: Session = Depends(get_sqlalchemy_db)):
    """Seed the database on startup."""
    seed_db(db)
```

## Updating the Main Application

Let's update our main application to use the database:

```python
# rpg_character_db/main.py
from fastapi import FastAPI, Depends

from rpg_character_db.api.router import api_router
from rpg_character_db.dependencies import startup_db_seed

app = FastAPI(title="RPG Character Database API")

@app.get("/")
async def root():
    return {"message": "Welcome to the RPG Character Database API"}

# Include all API routes
app.include_router(api_router)

# Seed the database on startup
@app.on_event("startup")
async def startup():
    startup_db_seed()
```

## Writing Tests for SQLAlchemy Repositories

Now, let's write tests for our SQLAlchemy repositories:

```bash
# Create a test file for SQLAlchemy repositories
touch tests/test_sqlalchemy_repositories.py
```

```python
# tests/test_sqlalchemy_repositories.py
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from rpg_character_db.database.core import Base
from rpg_character_db.database.models.character import Character as DBCharacter
from rpg_character_db.database.models.attribute import Attribute as DBAttribute
from rpg_character_db.database.models.skill import Skill as DBSkill
from rpg_character_db.models.character import CharacterCreate
from rpg_character_db.repositories.implementations.sqlalchemy.character_repository import SQLAlchemyCharacterRepository
from rpg_character_db.repositories.implementations.sqlalchemy.attribute_repository import SQLAlchemyAttributeRepository
from rpg_character_db.repositories.implementations.sqlalchemy.skill_repository import SQLAlchemySkillRepository

# Create an in-memory SQLite database for testing
SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"
engine = create_engine(SQLALCHEMY_DATABASE_URL)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@pytest.fixture
def db():
    """Create a fresh database for each test."""
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    # Create a session
    db = TestingSessionLocal()
    
    # Add some test data
    db.add(DBAttribute(
        attribute_id="ATTR:STR",
        name="Strength",
        description="Physical power and carrying capacity"
    ))
    db.add(DBSkill(
        skill_id="SKILL:SLASH",
        name="Slash",
        description="A basic melee attack",
        required_level=1
    ))
    db.commit()
    
    # Yield the session
    yield db
    
    # Clean up
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def character_repository(db):
    """Create a character repository with a test database."""
    return SQLAlchemyCharacterRepository(db)

@pytest.fixture
def attribute_repository(db):
    """Create an attribute repository with a test database."""
    return SQLAlchemyAttributeRepository(db)

@pytest.fixture
def skill_repository(db):
    """Create a skill repository with a test database."""
    return SQLAlchemySkillRepository(db)

def test_create_character(character_repository):
    """Test creating a character."""
    # Create a character
    character_create = CharacterCreate(
        name="Test Character",
        class_type="Warrior",
        level=1,
        race="Human"
    )
    character = character_repository.create(character_create)
    
    # Check that the character was created correctly
    assert character.name == "Test Character"
    assert character.class_type == "Warrior"
    assert character.level == 1
    assert character.race == "Human"
    assert character.character_id.startswith("CHAR:")

def test_get_character(character_repository, db):
    """Test getting a character."""
    # Add a character to the database
    db_character = DBCharacter(
        character_id="CHAR:TEST",
        name="Test Character",
        class_type="Warrior",
        level=1,
        race="Human"
    )
    db.add(db_character)
    db.commit()
    
    # Get the character
    character = character_repository.get("CHAR:TEST")
    
    # Check that the character was retrieved correctly
    assert character is not None
    assert character.character_id == "CHAR:TEST"
    assert character.name == "Test Character"
    assert character.class_type == "Warrior"
    assert character.level == 1
    assert character.race == "Human"

def test_get_attribute(attribute_repository):
    """Test getting an attribute."""
    # Get the attribute
    attribute = attribute_repository.get("ATTR:STR")
    
    # Check that the attribute was retrieved correctly
    assert attribute is not None
    assert attribute.attribute_id == "ATTR:STR"
    assert attribute.name == "Strength"
    assert attribute.description == "Physical power and carrying capacity"

def test_get_skill(skill_repository):
    """Test getting a skill."""
    # Get the skill
    skill = skill_repository.get("SKILL:SLASH")
    
    # Check that the skill was retrieved correctly
    assert skill is not None
    assert skill.skill_id == "SKILL:SLASH"
    assert skill.name == "Slash"
    assert skill.description == "A basic melee attack"
    assert skill.required_level == 1
```

## Running the Tests

Let's run our tests to see if our SQLAlchemy integration works:

```bash
pytest tests/test_sqlalchemy_repositories.py -v
```

All tests should pass! We've successfully integrated our application with SQLAlchemy.

## Updating Our API Tests

Now, let's update our API tests to use the SQLAlchemy repositories:

```python
# tests/conftest.py
import pytest
from fastapi.testclient import TestClient
from unittest.mock import MagicMock
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from rpg_character_db.main import app
from rpg_character_db.database.core import Base, get_db
from rpg_character_db.database.models.character import Character as DBCharacter
from rpg_character_db.database.models.attribute import Attribute as DBAttribute
from rpg_character_db.database.models.skill import Skill as DBSkill
from rpg_character_db.models.character import Character, CharacterCreate
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.repositories.implementations.sqlalchemy.character_repository import SQLAlchemyCharacterRepository
from rpg_character_db.dependencies import override_character_repository

# Create an in-memory SQLite database for testing
SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"
engine = create_engine(SQLALCHEMY_DATABASE_URL)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@pytest.fixture
def db():
    """Create a fresh database for each test."""
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    # Create a session
    db = TestingSessionLocal()
    
    # Add some test data
    db.add(DBAttribute(
        attribute_id="ATTR:STR",
        name="Strength",
        description="Physical power and carrying capacity"
    ))
    db.add(DBSkill(
        skill_id="SKILL:SLASH",
        name="Slash",
        description="A basic melee attack",
        required_level=1
    ))
    db.commit()
    
    # Yield the session
    yield db
    
    # Clean up
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def client(db):
    """Create a test client with a test database."""
    # Override the get_db dependency
    def override_get_db():
        try:
            yield db
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    # Create the client
    client = TestClient(app)
    
    # Yield the client
    yield client
    
    # Clear the override
    app.dependency_overrides.clear()

@pytest.fixture
def character_factory(db):
    """Factory fixture to create test characters."""
    def _create_character(
        character_id=None,
        name="Test Character",
        class_type="Warrior",
        level=1,
        race="Human"
    ):
        """Create a test character with the given attributes."""
        if character_id is None:
            # Create a new character through the repository
            character_create = CharacterCreate(
                name=name,
                class_type=class_type,
                level=level,
                race=race
            )
            repository = SQLAlchemyCharacterRepository(db)
            return repository.create(character_create)
        else:
            # Create a character with a specific ID
            db_character = DBCharacter(
                character_id=character_id,
                name=name,
                class_type=class_type,
                level=level,
                race=race
            )
            db.add(db_character)
            db.commit()
            db.refresh(db_character)
            
            # Convert to domain model
            return Character(
                character_id=character_id,
                name=name,
                class_type=class_type,
                level=level,
                race=race
            )
    
    return _create_character

@pytest.fixture
def existing_character(character_factory):
    """Create a test character with a fixed ID."""
    return character_factory(character_id="CHAR:TEST")

@pytest.fixture
def multiple_characters(character_factory):
    """Create multiple test characters."""
    characters = []
    for i in range(3):
        characters.append(character_factory(
            name=f"Character {i}",
            class_type=["Warrior", "Mage", "Rogue"][i % 3],
            level=i + 1,
            race=["Human", "Elf", "Dwarf"][i % 3]
        ))
    return characters

@pytest.fixture
def mock_character_repository():
    """Create a mock character repository."""
    repository = MagicMock(spec=ICharacterRepository)
    
    # Configure the mock
    repository.get.return_value = Character(
        character_id="CHAR:TEST",
        name="Test Character",
        class_type="Warrior",
        level=1,
        race="Human"
    )
    
    return repository

@pytest.fixture
def client_with_mock_repository(mock_character_repository):
    """Create a test client with a mock character repository."""
    # Set up the override
    override_character_repository(lambda: mock_character_repository)
    
    # Create the client
    client = TestClient(app)
    
    # Yield the client for the test to use
    yield client
    
    # Clear the override after the test
    override_character_repository(None)
```

## Running All Tests

Let's run all our tests to make sure everything works:

```bash
pytest -v
```

All tests should pass! We've successfully integrated our application with SQLAlchemy while maintaining our existing functionality.

## Committing Our Changes

Let's commit our changes to Git:

```bash
git add .
git commit -m "[RED/GREEN/REFACTOR] Integrate with SQLAlchemy"
```

## Conclusion

In this lesson, we've:
1. Integrated our application with SQLAlchemy
2. Created SQLAlchemy models for our domain entities
3. Implemented SQLAlchemy repositories
4. Updated our dependencies to use the SQLAlchemy repositories
5. Written tests for our SQLAlchemy integration

We've successfully switched from an in-memory database to a real database while maintaining our existing functionality. This demonstrates the power of the repository pattern and dependency injection, which allowed us to make this significant change with minimal impact on our application code.

In the next lesson, we'll learn about API documentation and how to use Swagger UI to document our API.

Are there any questions about what we've covered today?

Thank you for your attention, and I'll see you in the next lesson!
