# Module 3, Lesson 9: Refactoring API Code

## Introduction

Welcome back to our TDD course! In our previous lesson, we improved our test organization by creating reusable fixtures and grouping tests by feature. Today, we'll focus on refactoring our API code to make it more modular and maintainable.

As our application grows, it's important to keep our code organized and follow good software design principles. In this lesson, we'll separate our API code into modules, making it easier to understand, maintain, and extend.

## The Importance of Code Organization

Before we start refactoring, let's discuss why code organization is important:

1. **Readability**: Well-organized code is easier to read and understand
2. **Maintainability**: Modular code is easier to maintain and update
3. **Testability**: Properly separated concerns make testing easier
4. **Scalability**: Good organization allows the codebase to grow without becoming unwieldy
5. **Collaboration**: Clear structure makes it easier for multiple developers to work on the same codebase

## Identifying Areas for Refactoring

Let's look at our current codebase and identify areas that could benefit from refactoring:

1. **API Routes**: Our character routes are getting complex and could be split into separate modules
2. **Models**: We have several related models that could be organized better
3. **Validation**: Character ID validation could be moved to a separate utility module
4. **Repository**: Our repository could be better structured for future expansion

## Refactoring API Routes

Let's start by refactoring our API routes. Currently, all our character-related endpoints are in a single file. Let's split them into separate files based on functionality:

```bash
# Create directories for our API modules
mkdir -p rpg_character_db/api/endpoints
touch rpg_character_db/api/endpoints/__init__.py

# Create separate files for different endpoint groups
touch rpg_character_db/api/endpoints/character_retrieval.py
touch rpg_character_db/api/endpoints/character_creation.py
touch rpg_character_db/api/endpoints/character_update.py
touch rpg_character_db/api/endpoints/character_listing.py
```

Now, let's move our endpoints to their respective files:

```python
# rpg_character_db/api/endpoints/character_retrieval.py
from fastapi import APIRouter, HTTPException, Path
import re

from rpg_character_db.models.character import Character
from rpg_character_db.repositories.character_repository import CharacterRepository
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter(tags=["characters"])

@router.get("/character/{character_id}", response_model=Character)
async def get_character_by_id(
    character_id: str = Path(..., description="The ID of the character to retrieve")
):
    """
    Get a character by ID.
    
    Args:
        character_id: The character ID
        
    Returns:
        The character if found
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character from the repository
    character = CharacterRepository.get(character_id)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return character
```

```python
# rpg_character_db/api/endpoints/character_creation.py
from fastapi import APIRouter, status

from rpg_character_db.models.character import Character, CharacterCreate
from rpg_character_db.repositories.character_repository import CharacterRepository

router = APIRouter(tags=["characters"])

@router.post("/character", response_model=Character, status_code=status.HTTP_201_CREATED)
async def create_character(character_create: CharacterCreate):
    """
    Create a new character.
    
    Args:
        character_create: The character data
        
    Returns:
        The created character
    """
    # Create a new character using the repository
    return CharacterRepository.create(character_create)
```

```python
# rpg_character_db/api/endpoints/character_update.py
from fastapi import APIRouter, HTTPException, Path, Body

from rpg_character_db.models.character import Character, CharacterUpdate, CharacterPartialUpdate
from rpg_character_db.repositories.character_repository import CharacterRepository
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter(tags=["characters"])

@router.put("/character/{character_id}", response_model=Character)
async def update_character(
    character_id: str = Path(..., description="The ID of the character to update"),
    character_update: CharacterUpdate = Body(..., description="The updated character data")
):
    """
    Update a character.
    
    Args:
        character_id: The character ID
        character_update: The updated character data
        
    Returns:
        The updated character
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Update the character
    updated_character = CharacterRepository.update(character_id, character_update.dict())
    
    # Check if the character exists
    if updated_character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return updated_character

@router.patch("/character/{character_id}", response_model=Character)
async def partial_update_character(
    character_id: str = Path(..., description="The ID of the character to update"),
    character_update: CharacterPartialUpdate = Body(..., description="The character data to update")
):
    """
    Partially update a character.
    
    Args:
        character_id: The character ID
        character_update: The character data to update
        
    Returns:
        The updated character
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Remove None values from the update data
    update_data = {k: v for k, v in character_update.dict().items() if v is not None}
    
    # Check if there's anything to update
    if not update_data:
        raise HTTPException(
            status_code=422,
            detail="No valid fields to update"
        )
    
    # Partially update the character
    updated_character = CharacterRepository.partial_update(character_id, update_data)
    
    # Check if the character exists
    if updated_character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return updated_character
```

```python
# rpg_character_db/api/endpoints/character_listing.py
from fastapi import APIRouter, Query
from typing import List, Optional

from rpg_character_db.models.character import Character
from rpg_character_db.repositories.character_repository import CharacterRepository

router = APIRouter(tags=["characters"])

@router.get("/characters", response_model=List[Character])
async def list_characters(
    class_type: Optional[str] = Query(None, description="Filter characters by class")
):
    """
    List all characters.
    
    Args:
        class_type: Optional filter for character class
        
    Returns:
        A list of characters
    """
    characters = CharacterRepository.list()
    
    # Apply filters if provided
    if class_type:
        characters = [c for c in characters if c.class_type == class_type]
    
    return characters
```

## Creating a Validation Utility Module

Let's move our character ID validation to a separate utility module:

```bash
# Create a directory for utility modules
mkdir -p rpg_character_db/utils
touch rpg_character_db/utils/__init__.py
touch rpg_character_db/utils/validation.py
```

```python
# rpg_character_db/utils/validation.py
import re
from fastapi import HTTPException

# Define a regex pattern for valid character IDs
CHAR_ID_PATTERN = r"^CHAR:[a-zA-Z0-9]+$"

def validate_character_id(character_id: str) -> None:
    """
    Validate that a character ID matches the expected format.
    
    Args:
        character_id: The character ID to validate
        
    Raises:
        HTTPException: If the character ID is invalid
    """
    if not re.match(CHAR_ID_PATTERN, character_id):
        raise HTTPException(
            status_code=422,
            detail={
                "message": "Invalid character ID format",
                "expected_pattern": CHAR_ID_PATTERN,
                "received": character_id
            }
        )
```

## Creating a Router Registry

Now, let's create a module to register all our routers:

```bash
touch rpg_character_db/api/router.py
```

```python
# rpg_character_db/api/router.py
from fastapi import APIRouter

from rpg_character_db.api.endpoints import (
    character_retrieval,
    character_creation,
    character_update,
    character_listing
)

# Create a main router
api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(character_retrieval.router)
api_router.include_router(character_creation.router)
api_router.include_router(character_update.router)
api_router.include_router(character_listing.router)
```

## Updating the Main Application

Finally, let's update our main application to use our new router structure:

```python
# rpg_character_db/main.py
from fastapi import FastAPI

from rpg_character_db.api.router import api_router

app = FastAPI(title="RPG Character Database API")

@app.get("/")
async def root():
    return {"message": "Welcome to the RPG Character Database API"}

# Include all API routes
app.include_router(api_router)
```

## Running the Tests

Let's run our tests to make sure our refactoring didn't break anything:

```bash
pytest -v
```

All our tests should still pass! This confirms that our refactoring was successful.

## Refactoring the Repository

Let's also refactor our repository to make it more maintainable:

```python
# rpg_character_db/repositories/character_repository.py
from typing import Dict, List, Optional, Any

from rpg_character_db.db import db
from rpg_character_db.models.character import Character, CharacterCreate

class CharacterRepository:
    """Repository for character data."""
    
    @classmethod
    def get(cls, character_id: str) -> Optional[Character]:
        """
        Get a character by ID.
        
        Args:
            character_id: The character ID
            
        Returns:
            The character if found, None otherwise
        """
        if character_id not in db:
            return None
        
        return Character(**db[character_id])
    
    @classmethod
    def create(cls, character_create: CharacterCreate) -> Character:
        """
        Create a new character.
        
        Args:
            character_create: The character data
            
        Returns:
            The created character
        """
        character = Character.create(character_create)
        db[character.character_id] = character.dict()
        return character
    
    @classmethod
    def update(cls, character_id: str, character_update: Dict[str, Any]) -> Optional[Character]:
        """
        Update a character.
        
        Args:
            character_id: The character ID
            character_update: The character data to update
            
        Returns:
            The updated character if found, None otherwise
        """
        if character_id not in db:
            return None
        
        # Get the current character data
        character_data = db[character_id]
        
        # Update the character data
        character_data.update(character_update)
        
        # Store the updated character
        db[character_id] = character_data
        
        return Character(**character_data)
    
    @classmethod
    def partial_update(cls, character_id: str, character_update: Dict[str, Any]) -> Optional[Character]:
        """
        Partially update a character.
        
        Args:
            character_id: The character ID
            character_update: The character data to update
            
        Returns:
            The updated character if found, None otherwise
        """
        if character_id not in db:
            return None
        
        # Get the current character data
        character_data = db[character_id].copy()
        
        # Update only the specified fields
        for key, value in character_update.items():
            if key in character_data:
                character_data[key] = value
        
        # Store the updated character
        db[character_id] = character_data
        
        return Character(**character_data)
    
    @classmethod
    def list(cls) -> List[Character]:
        """
        List all characters.
        
        Returns:
            A list of all characters
        """
        return [Character(**data) for data in db.values()]
    
    @classmethod
    def delete(cls, character_id: str) -> bool:
        """
        Delete a character.
        
        Args:
            character_id: The character ID
            
        Returns:
            True if the character was deleted, False otherwise
        """
        if character_id not in db:
            return False
        
        del db[character_id]
        return True
```

We've made several improvements:
1. Changed static methods to class methods for better extensibility
2. Added type hints for better code documentation
3. Added a delete method for future use
4. Improved method documentation

## Committing Our Changes

Let's commit our changes to Git:

```bash
git add .
git commit -m "[REFACTOR] Separate API code into modules"
```

## Benefits of Our Refactoring

Let's discuss the benefits of the refactoring we've done:

1. **Improved Readability**: Each module has a clear, focused purpose
2. **Better Maintainability**: Changes to one feature don't affect others
3. **Easier Testing**: We can test each module independently
4. **Improved Scalability**: We can add new features without cluttering existing code
5. **Better Collaboration**: Multiple developers can work on different modules simultaneously

## Conclusion

In this lesson, we've:
1. Refactored our API routes into separate modules based on functionality
2. Created a validation utility module for common validation logic
3. Set up a router registry to organize our endpoints
4. Improved our repository implementation
5. Verified that our refactoring didn't break existing functionality

This refactoring has made our codebase more modular, maintainable, and scalable. As we continue to add features, this organization will help keep our code clean and manageable.

In the next lesson, we'll implement character attributes management, which will allow us to assign attributes to characters.

Are there any questions about what we've covered today?

Thank you for your attention, and I'll see you in the next lesson!
