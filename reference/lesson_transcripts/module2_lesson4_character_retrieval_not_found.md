# Module 2, Lesson 4: Character Retrieval - Not Found

## Introduction

Welcome to Module 2! In our previous lessons, we covered TDD fundamentals, set up our project, and wrote our first test. Today, we'll continue building our RPG Character Database API by focusing on character retrieval functionality.

In this lesson, we'll expand our test coverage for the character retrieval endpoint, specifically focusing on the "not found" scenario. We'll follow the RED-GREEN-REFACTOR cycle to drive our development.

## Writing a Test for Non-Existent Character Retrieval

We already have a basic test for retrieving a non-existent character, but let's enhance it to be more comprehensive. We'll create a new test file specifically for character retrieval tests:

```bash
# If you haven't already created this file in the previous lesson
touch tests/test_get_character_by_id.py
```

Now, let's write a more detailed test:

```python
# tests/test_get_character_by_id.py
from fastapi.testclient import TestClient

from rpg_character_db.main import app

client = TestClient(app)

def test_get_non_existent_character_returns_404():
    """Test that requesting a non-existent character returns a 404 status code."""
    response = client.get("/character/NON_EXISTENT")
    assert response.status_code == 404
    assert response.json() == {"detail": "Character not found"}

def test_get_invalid_character_id_format_returns_422():
    """Test that requesting a character with an invalid ID format returns a 422 status code."""
    # Our character IDs should follow the format "CHAR:{id}"
    response = client.get("/character/invalid-format")
    assert response.status_code == 422
    assert "detail" in response.json()
```

In this enhanced test file, we've:
1. Kept our original test for a non-existent character
2. Added a new test for invalid character ID format

The second test checks that our API validates the character ID format. We've decided that our character IDs should follow the format "CHAR:{id}", and any request with an invalid format should return a 422 Unprocessable Entity status code.

## Running the Tests (RED)

Let's run our tests to see them fail:

```bash
pytest tests/test_get_character_by_id.py -v
```

The first test might pass if you implemented it in the previous lesson, but the second test should fail because we haven't implemented ID format validation yet.

## Implementing the GET /character/{character_id} Endpoint

Now, let's update our endpoint to handle both cases:

```python
# rpg_character_db/api/character_routes.py
from fastapi import APIRouter, HTTPException, Path
import re

router = APIRouter(prefix="/character", tags=["characters"])

# Define a regex pattern for valid character IDs
CHAR_ID_PATTERN = r"^CHAR:[a-zA-Z0-9]+$"

@router.get("/{character_id}")
async def get_character_by_id(
    character_id: str = Path(..., description="The ID of the character to retrieve")
):
    # Validate character ID format
    if not re.match(CHAR_ID_PATTERN, character_id):
        raise HTTPException(
            status_code=422,
            detail=f"Invalid character ID format. Must match pattern: {CHAR_ID_PATTERN}"
        )
    
    # For now, we'll just return a 404 for any character_id
    # We'll implement actual character retrieval in the next lesson
    raise HTTPException(status_code=404, detail="Character not found")
```

In this implementation, we've:
1. Added a regex pattern to validate character ID format
2. Used FastAPI's Path parameter to document the character_id parameter
3. Added validation to check if the character ID matches our expected format
4. Kept the 404 response for any character ID (even if it's valid) since we haven't implemented character storage yet

## Running the Tests Again (GREEN)

Let's run our tests again to see if they pass:

```bash
pytest tests/test_get_character_by_id.py -v
```

Both tests should now pass! We've successfully implemented character ID validation.

## Implementing the GET /character/{character_id} Endpoint with Error Messages

Let's refine our implementation to provide more helpful error messages:

```python
# rpg_character_db/api/character_routes.py
from fastapi import APIRouter, HTTPException, Path
import re

router = APIRouter(prefix="/character", tags=["characters"])

# Define a regex pattern for valid character IDs
CHAR_ID_PATTERN = r"^CHAR:[a-zA-Z0-9]+$"

@router.get("/{character_id}")
async def get_character_by_id(
    character_id: str = Path(..., description="The ID of the character to retrieve")
):
    # Validate character ID format
    if not re.match(CHAR_ID_PATTERN, character_id):
        raise HTTPException(
            status_code=422,
            detail={
                "message": "Invalid character ID format",
                "expected_pattern": CHAR_ID_PATTERN,
                "received": character_id
            }
        )
    
    # For now, we'll just return a 404 for any character_id
    # We'll implement actual character retrieval in the next lesson
    raise HTTPException(status_code=404, detail="Character not found")
```

Now we're providing a more detailed error message that includes:
- A clear message about the error
- The expected pattern
- The received value

Let's update our test to match this new format:

```python
# tests/test_get_character_by_id.py
def test_get_invalid_character_id_format_returns_422():
    """Test that requesting a character with an invalid ID format returns a 422 status code."""
    # Our character IDs should follow the format "CHAR:{id}"
    response = client.get("/character/invalid-format")
    assert response.status_code == 422
    assert "detail" in response.json()
    assert "message" in response.json()["detail"]
    assert response.json()["detail"]["message"] == "Invalid character ID format"
```

## Running the Tests Again (GREEN)

Let's run our tests again:

```bash
pytest tests/test_get_character_by_id.py -v
```

Our tests should still pass, confirming that our implementation works correctly.

## Refactoring

Now that our tests are passing, let's refactor our code to make it more maintainable. We'll move the character ID validation to a separate function:

```python
# rpg_character_db/api/character_routes.py
from fastapi import APIRouter, HTTPException, Path
import re

router = APIRouter(prefix="/character", tags=["characters"])

# Define a regex pattern for valid character IDs
CHAR_ID_PATTERN = r"^CHAR:[a-zA-Z0-9]+$"

def validate_character_id(character_id: str) -> None:
    """
    Validate that a character ID matches the expected format.
    
    Args:
        character_id: The character ID to validate
        
    Raises:
        HTTPException: If the character ID is invalid
    """
    if not re.match(CHAR_ID_PATTERN, character_id):
        raise HTTPException(
            status_code=422,
            detail={
                "message": "Invalid character ID format",
                "expected_pattern": CHAR_ID_PATTERN,
                "received": character_id
            }
        )

@router.get("/{character_id}")
async def get_character_by_id(
    character_id: str = Path(..., description="The ID of the character to retrieve")
):
    # Validate character ID format
    validate_character_id(character_id)
    
    # For now, we'll just return a 404 for any character_id
    # We'll implement actual character retrieval in the next lesson
    raise HTTPException(status_code=404, detail="Character not found")
```

This refactoring makes our code more modular and reusable. We can now use the `validate_character_id` function in other endpoints that require character ID validation.

## Running the Tests After Refactoring

Let's run our tests one more time to ensure our refactoring didn't break anything:

```bash
pytest tests/test_get_character_by_id.py -v
```

All tests should still pass, confirming that our refactoring was successful.

## Handling Error Messages

Let's take a moment to discuss how we're handling error messages. In a RESTful API, it's important to provide clear, consistent error messages to help clients understand what went wrong and how to fix it.

In our implementation, we're using FastAPI's HTTPException to return error responses with:
- An appropriate status code (404 for not found, 422 for validation errors)
- A detailed error message that explains the issue

This approach makes our API more user-friendly and easier to debug.

## Committing Our Changes

Let's commit our changes to Git:

```bash
git add .
git commit -m "[RED/GREEN/REFACTOR] Implement character ID validation"
```

## Conclusion

In this lesson, we've:
1. Enhanced our test coverage for character retrieval
2. Implemented character ID validation
3. Provided clear error messages for invalid requests
4. Refactored our code to make it more maintainable

We've continued to follow the RED-GREEN-REFACTOR cycle, letting our tests drive the development of our API.

In the next lesson, we'll implement character storage and retrieval for existing characters, which will allow us to return actual character data instead of just 404 responses.

Are there any questions about what we've covered today?

Thank you for your attention, and I'll see you in the next lesson!
