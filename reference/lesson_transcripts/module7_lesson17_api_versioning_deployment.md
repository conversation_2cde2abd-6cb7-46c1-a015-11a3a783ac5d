# Module 7, Lesson 17: API Versioning and Deployment

## Introduction

Welcome back to our TDD course! In our previous lessons, we've built a functional RPG Character Database API with character, attribute, and skill management, integrated it with SQLAlchemy, and enhanced its documentation. Today, we'll focus on API versioning and deployment.

As APIs evolve, changes are inevitable. Some changes are backward-compatible, but others might break existing clients. API versioning helps manage these changes by allowing multiple versions of an API to coexist. We'll also discuss how to deploy our API to production environments.

## Understanding API Versioning

API versioning is a strategy for managing changes to your API over time. There are several approaches to API versioning:

1. **URL Versioning**: Including the version in the URL path (e.g., `/v1/characters`)
2. **Query Parameter Versioning**: Specifying the version as a query parameter (e.g., `/characters?version=1`)
3. **Header Versioning**: Using a custom header to specify the version (e.g., `X-API-Version: 1`)
4. **Content Negotiation**: Using the `Accept` header to specify the version (e.g., `Accept: application/vnd.api+json;version=1`)

Each approach has its pros and cons, but URL versioning is the most straightforward and widely used.

## Implementing URL Versioning

Let's implement URL versioning for our API:

```python
# rpg_character_db/api/v1/router.py
from fastapi import APIRouter

from rpg_character_db.api.endpoints import (
    character_retrieval,
    character_creation,
    character_update,
    character_listing,
    character_attributes,
    character_skills
)

# Create a v1 router
v1_router = APIRouter(prefix="/v1")

# Include all endpoint routers with tags
v1_router.include_router(
    character_retrieval.router,
    tags=["Characters"],
    prefix="/character"
)
v1_router.include_router(
    character_creation.router,
    tags=["Characters"],
    prefix="/character"
)
v1_router.include_router(
    character_update.router,
    tags=["Characters"],
    prefix="/character"
)
v1_router.include_router(
    character_listing.router,
    tags=["Characters"],
    prefix="/characters"
)
v1_router.include_router(
    character_attributes.router,
    tags=["Character Attributes"],
    prefix="/character"
)
v1_router.include_router(
    character_skills.router,
    tags=["Character Skills"],
    prefix="/character"
)
```

Now, let's update our main application to use the versioned router:

```python
# rpg_character_db/main.py
from fastapi import FastAPI, Depends
from fastapi.openapi.utils import get_openapi

from rpg_character_db.api.v1.router import v1_router
from rpg_character_db.dependencies import startup_db_seed

app = FastAPI(
    title="RPG Character Database API",
    description="""
    An API for managing RPG character data, including attributes and skills.
    
    ## Features
    
    * Create, read, update, and delete characters
    * Manage character attributes
    * Manage character skills
    
    ## Versioning
    
    This API uses URL versioning. The current version is v1.
    
    ## Authentication
    
    This API doesn't require authentication for simplicity.
    """,
    version="1.0.0",
    contact={
        "name": "Your Name",
        "url": "https://yourwebsite.com",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT",
    },
)

def custom_openapi():
    """
    Generate a custom OpenAPI schema.
    
    Returns:
        The OpenAPI schema
    """
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )
    
    # Add custom metadata
    openapi_schema["info"]["x-logo"] = {
        "url": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png"
    }
    
    # Add tags metadata
    openapi_schema["tags"] = [
        {
            "name": "Characters",
            "description": "Operations related to characters",
        },
        {
            "name": "Character Attributes",
            "description": "Operations related to character attributes",
        },
        {
            "name": "Character Skills",
            "description": "Operations related to character skills",
        },
    ]
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi

@app.get("/")
async def root():
    """
    Get a welcome message.
    
    Returns:
        A welcome message with API version information
    """
    return {
        "message": "Welcome to the RPG Character Database API",
        "version": "1.0.0",
        "documentation": "/docs"
    }

# Include the v1 router
app.include_router(v1_router)

# Seed the database on startup
@app.on_event("startup")
async def startup():
    startup_db_seed()
```

## Handling Breaking Changes

When we need to make breaking changes, we can create a new version of our API while maintaining the old version. Let's see how we might handle a breaking change:

```python
# rpg_character_db/api/v2/router.py
from fastapi import APIRouter

from rpg_character_db.api.v2.endpoints import (
    character_retrieval,
    character_creation,
    character_update,
    character_listing,
    character_attributes,
    character_skills,
    character_equipment  # New endpoint in v2
)

# Create a v2 router
v2_router = APIRouter(prefix="/v2")

# Include all endpoint routers with tags
v2_router.include_router(
    character_retrieval.router,
    tags=["Characters"],
    prefix="/character"
)
v2_router.include_router(
    character_creation.router,
    tags=["Characters"],
    prefix="/character"
)
v2_router.include_router(
    character_update.router,
    tags=["Characters"],
    prefix="/character"
)
v2_router.include_router(
    character_listing.router,
    tags=["Characters"],
    prefix="/characters"
)
v2_router.include_router(
    character_attributes.router,
    tags=["Character Attributes"],
    prefix="/character"
)
v2_router.include_router(
    character_skills.router,
    tags=["Character Skills"],
    prefix="/character"
)
v2_router.include_router(
    character_equipment.router,  # New endpoint in v2
    tags=["Character Equipment"],
    prefix="/character"
)
```

Then, we would update our main application to include both versions:

```python
# rpg_character_db/main.py
from fastapi import FastAPI, Depends
from fastapi.openapi.utils import get_openapi

from rpg_character_db.api.v1.router import v1_router
from rpg_character_db.api.v2.router import v2_router
from rpg_character_db.dependencies import startup_db_seed

app = FastAPI(
    title="RPG Character Database API",
    description="""
    An API for managing RPG character data, including attributes, skills, and equipment.
    
    ## Features
    
    * Create, read, update, and delete characters
    * Manage character attributes
    * Manage character skills
    * Manage character equipment (v2 only)
    
    ## Versioning
    
    This API uses URL versioning. The available versions are v1 and v2.
    
    ## Authentication
    
    This API doesn't require authentication for simplicity.
    """,
    version="2.0.0",
    contact={
        "name": "Your Name",
        "url": "https://yourwebsite.com",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT",
    },
)

# ... rest of the code ...

# Include both routers
app.include_router(v1_router)
app.include_router(v2_router)

# ... rest of the code ...
```

## Deploying the API

Now, let's discuss how to deploy our API to production. There are several options for deploying FastAPI applications:

1. **Docker**: Containerize the application for consistent deployment
2. **Kubernetes**: Orchestrate containers for scalability and reliability
3. **Cloud Platforms**: Deploy to platforms like AWS, Azure, or Google Cloud
4. **Traditional Servers**: Deploy to traditional servers with WSGI servers like Gunicorn

Let's focus on Docker, which is a popular choice for deploying FastAPI applications.

### Creating a Dockerfile

First, let's create a Dockerfile:

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# Copy requirements file
COPY requirements.txt .

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Expose port
EXPOSE 8000

# Run the application
CMD ["uvicorn", "rpg_character_db.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Creating a requirements.txt File

Next, let's create a requirements.txt file:

```
fastapi==0.68.0
uvicorn==0.15.0
sqlalchemy==1.4.23
pydantic==1.8.2
```

### Building and Running the Docker Container

Now, we can build and run the Docker container:

```bash
# Build the Docker image
docker build -t rpg-character-db .

# Run the Docker container
docker run -p 8000:8000 rpg-character-db
```

### Deploying to a Cloud Platform

For production deployments, we might want to use a cloud platform like AWS, Azure, or Google Cloud. Here's a high-level overview of deploying to AWS Elastic Beanstalk:

1. **Create an Elastic Beanstalk Application**: Use the AWS Management Console or AWS CLI to create an Elastic Beanstalk application.
2. **Create an Environment**: Create a web server environment for your application.
3. **Deploy Your Application**: Upload your application code as a ZIP file or deploy from a Git repository.
4. **Configure Environment Variables**: Set environment variables for your application, such as database connection strings.
5. **Monitor Your Application**: Use AWS CloudWatch to monitor your application's performance and health.

## Setting Up Continuous Integration and Deployment (CI/CD)

To automate the deployment process, we can set up a CI/CD pipeline using tools like GitHub Actions, GitLab CI/CD, or Jenkins. Here's an example GitHub Actions workflow:

```yaml
# .github/workflows/deploy.yml
name: Deploy

on:
  push:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.9'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest
    - name: Test with pytest
      run: |
        pytest

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.9'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install awsebcli
    - name: Deploy to AWS Elastic Beanstalk
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_DEFAULT_REGION: us-west-2
      run: |
        eb init -p python-3.9 rpg-character-db --region us-west-2
        eb deploy
```

## Testing the Deployment

Let's write a simple test to ensure our deployment works:

```python
# tests/test_deployment.py
import requests

def test_api_deployment():
    """Test that the API is deployed and accessible."""
    # Replace with your actual deployment URL
    url = "https://your-deployment-url.com"
    
    # Make a request to the root endpoint
    response = requests.get(url)
    
    # Check that the response is successful
    assert response.status_code == 200
    
    # Check that the response contains the expected data
    data = response.json()
    assert "message" in data
    assert data["message"] == "Welcome to the RPG Character Database API"
    assert "version" in data
```

## Committing Our Changes

Let's commit our changes to Git:

```bash
git add .
git commit -m "[FEATURE] Add API versioning and deployment configuration"
```

## Conclusion

In this lesson, we've:
1. Implemented API versioning using URL versioning
2. Discussed how to handle breaking changes
3. Created a Dockerfile for containerizing our application
4. Discussed deployment options for our API
5. Set up a CI/CD pipeline for automated testing and deployment

API versioning and deployment are crucial aspects of API development. Versioning helps manage changes to your API over time, while proper deployment ensures that your API is reliable, scalable, and secure.

In the next lesson, we'll wrap up our course with a review of what we've learned and discuss best practices for API development.

Are there any questions about what we've covered today?

Thank you for your attention, and I'll see you in the next lesson!
