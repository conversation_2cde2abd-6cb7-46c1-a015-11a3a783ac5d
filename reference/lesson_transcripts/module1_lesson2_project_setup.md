# Module 1, Lesson 2: Project Setup

## Introduction

Welcome back to our TDD course! In our previous lesson, we covered the fundamental concepts of Test-Driven Development. Today, we're going to set up our development environment and project structure for our RPG Character Database API.

Having a well-organized project structure and the right tools is essential for effective TDD. Let's get everything set up so we can start writing tests in our next lesson.

## Setting Up the Development Environment

For this course, we'll need Python 3.8 or higher and a few key packages. Let's start by creating a virtual environment to keep our project dependencies isolated.

```bash
# Create a new directory for our project
mkdir rpg_character_db
cd rpg_character_db

# Create and activate a virtual environment
python -m venv venv
source venv/bin/activate  # On Windows, use: venv\Scripts\activate
```

Now that we have our virtual environment activated, let's install the packages we'll need:

```bash
# Install required packages
pip install fastapi uvicorn pytest pytest-asyncio httpx sqlalchemy

# Install development tools
pip install black isort mypy
```

Let's go through what each of these packages does:

- **FastAPI**: A modern, fast web framework for building APIs with Python
- **Uvicorn**: An ASGI server that we'll use to run our FastAPI application
- **pytest**: Our testing framework
- **pytest-asyncio**: Extension for testing asynchronous code
- **httpx**: HTTP client for testing our API
- **SQLAlchemy**: ORM for database operations
- **black**: Code formatter to keep our code consistent
- **isort**: Import sorter
- **mypy**: Static type checker

## Project Structure and Organization

Now, let's set up our project structure. A well-organized structure makes it easier to navigate and maintain our code.

```bash
# Create project directories
mkdir -p rpg_character_db/api
mkdir -p rpg_character_db/models
mkdir -p rpg_character_db/repositories
mkdir -p tests
```

Here's what each directory is for:

- **rpg_character_db/**: Our main package
  - **api/**: API endpoints and route definitions
  - **models/**: Pydantic models for data validation and SQLAlchemy models
  - **repositories/**: Repository classes for data access
- **tests/**: All our test files

Now, let's create some initial files:

```bash
# Create __init__.py files to make directories into packages
touch rpg_character_db/__init__.py
touch rpg_character_db/api/__init__.py
touch rpg_character_db/models/__init__.py
touch rpg_character_db/repositories/__init__.py
touch tests/__init__.py

# Create main application file
touch rpg_character_db/main.py

# Create configuration files
touch pyproject.toml
touch .gitignore
```

Let's add some basic content to our main.py file:

```python
# rpg_character_db/main.py
from fastapi import FastAPI

app = FastAPI(title="RPG Character Database API")

@app.get("/")
async def root():
    return {"message": "Welcome to the RPG Character Database API"}
```

And let's set up our pyproject.toml file with some basic configuration:

```toml
# pyproject.toml
[tool.poetry]
name = "rpg_character_db"
version = "0.1.0"
description = "A RESTful API for managing RPG game characters"
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.8"
fastapi = "^0.95.0"
uvicorn = "^0.21.1"
sqlalchemy = "^2.0.9"

[tool.poetry.dev-dependencies]
pytest = "^7.3.1"
pytest-asyncio = "^0.21.0"
httpx = "^0.24.0"
black = "^23.3.0"
isort = "^5.12.0"
mypy = "^1.2.0"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ["py38"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
```

Finally, let's create a basic .gitignore file:

```
# .gitignore
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.env
.venv
venv/
ENV/
.coverage
htmlcov/
.pytest_cache/
.mypy_cache/
```

## Creating the Initial Repository

Now that we have our project structure set up, let's initialize a Git repository to track our changes:

```bash
# Initialize Git repository
git init

# Add all files to staging
git add .

# Make initial commit
git commit -m "Initial project setup"
```

Using Git will help us track our progress and revert changes if needed, which is especially useful when practicing TDD.

## Running the Application

Let's make sure everything is working by running our application:

```bash
# Run the application
uvicorn rpg_character_db.main:app --reload
```

If everything is set up correctly, you should be able to access the API at http://localhost:8000. You'll also have automatic API documentation available at http://localhost:8000/docs, which is a great feature of FastAPI.

## Conclusion

Today we've:
- Set up our development environment with all necessary packages
- Created a well-organized project structure
- Set up configuration files for our development tools
- Initialized a Git repository
- Verified that our application runs correctly

In our next lesson, we'll write our first test following the TDD approach. We'll start with a simple test for retrieving a character that doesn't exist, which will drive the development of our first API endpoint.

Are there any questions about the project setup?

Thank you for your attention, and I'll see you in the next lesson where we'll start applying TDD principles to our project!
