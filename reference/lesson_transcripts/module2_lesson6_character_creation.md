# Module 2, Lesson 6: Character Creation

## Introduction

Welcome back to our TDD course! In our previous lessons, we implemented character retrieval functionality. Today, we'll expand our API by implementing character creation, which will allow clients to add new characters to our database.

As always, we'll follow the RED-GREEN-REFACTOR cycle to drive our development. We'll start by writing tests for character creation, then implement the functionality to make those tests pass, and finally refactor our code for better maintainability.

## Writing Tests for Character Creation

Let's create a new test file specifically for character creation:

```bash
# Create a new test file for character creation
touch tests/test_create_character.py
```

Now, let's write tests for our character creation endpoint:

```python
# tests/test_create_character.py
import pytest
from fastapi.testclient import TestClient

from rpg_character_db.main import app
from rpg_character_db.db import db
from rpg_character_db.models.character import Character

client = TestClient(app)

@pytest.fixture(autouse=True)
def clear_db():
    """Clear the database before each test."""
    db.clear()
    yield
    db.clear()

def test_create_character_returns_201():
    """Test that creating a character returns a 201 status code."""
    character_data = {
        "name": "New Character",
        "class_type": "Paladin",
        "level": 5,
        "race": "Dwarf"
    }
    
    response = client.post("/character", json=character_data)
    assert response.status_code == 201

def test_create_character_returns_created_character():
    """Test that creating a character returns the created character with an ID."""
    character_data = {
        "name": "New Character",
        "class_type": "Paladin",
        "level": 5,
        "race": "Dwarf"
    }
    
    response = client.post("/character", json=character_data)
    created_character = response.json()
    
    # Check that the response includes all the data we sent plus a character_id
    assert "character_id" in created_character
    assert created_character["name"] == character_data["name"]
    assert created_character["class_type"] == character_data["class_type"]
    assert created_character["level"] == character_data["level"]
    assert created_character["race"] == character_data["race"]
    
    # Check that the character_id follows our expected format
    assert created_character["character_id"].startswith("CHAR:")

def test_create_character_stores_in_database():
    """Test that creating a character stores it in the database."""
    character_data = {
        "name": "New Character",
        "class_type": "Paladin",
        "level": 5,
        "race": "Dwarf"
    }
    
    response = client.post("/character", json=character_data)
    created_character = response.json()
    
    # Check that the character is in the database
    assert created_character["character_id"] in db
    
    # Check that we can retrieve the character
    get_response = client.get(f"/character/{created_character['character_id']}")
    assert get_response.status_code == 200
    assert get_response.json() == created_character

def test_create_character_with_invalid_data_returns_422():
    """Test that creating a character with invalid data returns a 422 status code."""
    # Missing required fields
    character_data = {
        "name": "New Character",
        # Missing class_type
        "level": 5,
        "race": "Dwarf"
    }
    
    response = client.post("/character", json=character_data)
    assert response.status_code == 422
    
    # Invalid level (less than 1)
    character_data = {
        "name": "New Character",
        "class_type": "Paladin",
        "level": 0,  # Invalid level
        "race": "Dwarf"
    }
    
    response = client.post("/character", json=character_data)
    assert response.status_code == 422
```

In these tests, we're:
1. Testing that creating a character returns a 201 Created status code
2. Testing that the response includes the created character with an ID
3. Testing that the character is stored in the database and can be retrieved
4. Testing that creating a character with invalid data returns a 422 Unprocessable Entity status code

## Running the Tests (RED)

Let's run our tests to see them fail:

```bash
pytest tests/test_create_character.py -v
```

All our tests should fail because we haven't implemented the character creation endpoint yet.

## Creating a Model for Character Creation

Before we implement the endpoint, let's create a Pydantic model for character creation. This model will be used to validate the request data.

We'll update our character models file:

```python
# rpg_character_db/models/character.py
from pydantic import BaseModel, Field
import uuid

class CharacterCreate(BaseModel):
    """Model for creating a new character."""
    name: str = Field(..., description="Character name")
    class_type: str = Field(..., description="Character class (e.g., Warrior, Mage)")
    level: int = Field(..., description="Character level", ge=1)
    race: str = Field(..., description="Character race (e.g., Human, Elf)")

class Character(CharacterCreate):
    """Model representing an RPG character."""
    character_id: str = Field(..., description="Unique identifier for the character")
    
    @classmethod
    def create(cls, character_create: CharacterCreate) -> "Character":
        """Create a new character from a CharacterCreate model."""
        return cls(
            character_id=f"CHAR:{uuid.uuid4().hex[:8]}",
            **character_create.dict()
        )
```

We've made several improvements:
1. We've created a `CharacterCreate` model that doesn't include the `character_id` field
2. We've updated the `Character` model to inherit from `CharacterCreate`
3. We've added a `create` class method to generate a new `Character` from a `CharacterCreate` model

## Implementing the Character Creation Endpoint (GREEN)

Now, let's implement the character creation endpoint:

```python
# rpg_character_db/api/character_routes.py
from fastapi import APIRouter, HTTPException, Path, status
import re

from rpg_character_db.db import db
from rpg_character_db.models.character import Character, CharacterCreate

router = APIRouter(prefix="/character", tags=["characters"])

# ... existing code ...

@router.post("/", response_model=Character, status_code=status.HTTP_201_CREATED)
async def create_character(character_create: CharacterCreate):
    """
    Create a new character.
    
    Args:
        character_create: The character data
        
    Returns:
        The created character
    """
    # Create a new character
    character = Character.create(character_create)
    
    # Store the character in the database
    db[character.character_id] = character.dict()
    
    return character
```

In this implementation, we:
1. Define a POST endpoint at `/character`
2. Use our `CharacterCreate` model to validate the request data
3. Use the `Character.create` method to generate a new character with an ID
4. Store the character in our in-memory database
5. Return the created character with a 201 Created status code

## Running the Tests Again (GREEN)

Let's run our tests again to see if they pass:

```bash
pytest tests/test_create_character.py -v
```

All our tests should now pass! We've successfully implemented character creation.

## Refactoring

Our implementation is working, but there's room for improvement. Let's refactor to make our code more maintainable:

1. We should move the database operations to a separate module
2. We should add more validation for character data

Let's start by creating a repository module for database operations:

```bash
# Create a new file for our character repository
touch rpg_character_db/repositories/character_repository.py
```

```python
# rpg_character_db/repositories/character_repository.py
from typing import Dict, List, Optional

from rpg_character_db.db import db
from rpg_character_db.models.character import Character, CharacterCreate

class CharacterRepository:
    """Repository for character data."""
    
    @staticmethod
    def get(character_id: str) -> Optional[Character]:
        """
        Get a character by ID.
        
        Args:
            character_id: The character ID
            
        Returns:
            The character if found, None otherwise
        """
        if character_id not in db:
            return None
        
        return Character(**db[character_id])
    
    @staticmethod
    def create(character_create: CharacterCreate) -> Character:
        """
        Create a new character.
        
        Args:
            character_create: The character data
            
        Returns:
            The created character
        """
        character = Character.create(character_create)
        db[character.character_id] = character.dict()
        return character
    
    @staticmethod
    def list() -> List[Character]:
        """
        List all characters.
        
        Returns:
            A list of all characters
        """
        return [Character(**data) for data in db.values()]
```

Now, let's update our character routes to use this repository:

```python
# rpg_character_db/api/character_routes.py
from fastapi import APIRouter, HTTPException, Path, status
import re

from rpg_character_db.models.character import Character, CharacterCreate
from rpg_character_db.repositories.character_repository import CharacterRepository

router = APIRouter(prefix="/character", tags=["characters"])

# Define a regex pattern for valid character IDs
CHAR_ID_PATTERN = r"^CHAR:[a-zA-Z0-9]+$"

def validate_character_id(character_id: str) -> None:
    """
    Validate that a character ID matches the expected format.
    
    Args:
        character_id: The character ID to validate
        
    Raises:
        HTTPException: If the character ID is invalid
    """
    if not re.match(CHAR_ID_PATTERN, character_id):
        raise HTTPException(
            status_code=422,
            detail={
                "message": "Invalid character ID format",
                "expected_pattern": CHAR_ID_PATTERN,
                "received": character_id
            }
        )

@router.get("/{character_id}", response_model=Character)
async def get_character_by_id(
    character_id: str = Path(..., description="The ID of the character to retrieve")
):
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character from the repository
    character = CharacterRepository.get(character_id)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return character

@router.post("/", response_model=Character, status_code=status.HTTP_201_CREATED)
async def create_character(character_create: CharacterCreate):
    """
    Create a new character.
    
    Args:
        character_create: The character data
        
    Returns:
        The created character
    """
    # Create a new character using the repository
    return CharacterRepository.create(character_create)
```

We've made several improvements:
1. We've moved database operations to a dedicated repository
2. We've updated our endpoints to use this repository
3. We've added a method to list all characters (which we'll use in a future lesson)

## Running the Tests After Refactoring

Let's run our tests one more time to ensure our refactoring didn't break anything:

```bash
pytest tests/test_create_character.py tests/test_get_character_by_id.py -v
```

All tests should still pass, confirming that our refactoring was successful.

## Committing Our Changes

Let's commit our changes to Git:

```bash
git add .
git commit -m "[RED/GREEN/REFACTOR] Implement character creation"
```

## Conclusion

In this lesson, we've:
1. Written tests for character creation
2. Implemented a character creation endpoint
3. Created a repository for database operations
4. Refactored our code for better maintainability

We've continued to follow the RED-GREEN-REFACTOR cycle, letting our tests drive the development of our API.

In the next lesson, we'll implement character update functionality, which will allow clients to modify existing characters.

Are there any questions about what we've covered today?

Thank you for your attention, and I'll see you in the next lesson!
