# Module 4, Lesson 10: Character Attributes Management

## Introduction

Welcome back to our TDD course! In our previous lessons, we've implemented the core functionality of our RPG Character Database API and refactored our code for better organization. Today, we'll expand our API by implementing character attributes management.

In RPG games, characters typically have various attributes like strength, intelligence, and dexterity that define their capabilities. We'll implement functionality to assign attribute values to characters, update them, and remove them when needed.

As always, we'll follow the RED-GREEN-REFACTOR cycle to drive our development.

## Defining Our Requirements

Before we start writing tests, let's define what we want to achieve:

1. Users should be able to assign attribute values to a character
2. Users should be able to update attribute values for a character
3. Users should be able to remove attributes from a character
4. The system should validate that attribute values are within acceptable ranges

## Creating Models for Attributes

Let's start by creating models for our attributes:

```bash
# Create a new file for attribute models
touch rpg_character_db/models/attribute.py
```

```python
# rpg_character_db/models/attribute.py
from pydantic import BaseModel, Field, validator
from typing import Dict, Optional

class Attribute(BaseModel):
    """Model representing an RPG character attribute."""
    attribute_id: str = Field(..., description="Unique identifier for the attribute")
    name: str = Field(..., description="Attribute name")
    description: str = Field(..., description="Attribute description")

class AttributeValue(BaseModel):
    """Model representing an attribute value assigned to a character."""
    value: int = Field(..., description="Attribute value", ge=1, le=20)
    
    @validator('value')
    def validate_value_range(cls, v):
        """Validate that the attribute value is within the acceptable range."""
        if v < 1 or v > 20:
            raise ValueError("Attribute value must be between 1 and 20")
        return v

class CharacterAttribute(BaseModel):
    """Model representing an attribute with its value for a character."""
    attribute_id: str = Field(..., description="Unique identifier for the attribute")
    name: str = Field(..., description="Attribute name")
    description: str = Field(..., description="Attribute description")
    value: int = Field(..., description="Attribute value", ge=1, le=20)

class CharacterAttributes(BaseModel):
    """Model representing all attributes for a character."""
    attributes: Dict[str, CharacterAttribute] = Field(default_factory=dict)
```

Now, let's update our character model to include attributes:

```python
# rpg_character_db/models/character.py
from pydantic import BaseModel, Field, validator
import uuid
from typing import Dict, Optional

from rpg_character_db.models.attribute import CharacterAttributes

class CharacterBase(BaseModel):
    """Base model for character data."""
    name: str = Field(..., description="Character name")
    class_type: str = Field(..., description="Character class (e.g., Warrior, Mage)")
    level: int = Field(..., description="Character level", ge=1)
    race: str = Field(..., description="Character race (e.g., Human, Elf)")

class CharacterCreate(CharacterBase):
    """Model for creating a new character."""
    pass

class CharacterUpdate(CharacterBase):
    """Model for updating a character."""
    pass

class CharacterPartialUpdate(BaseModel):
    """Model for partially updating a character."""
    name: Optional[str] = Field(None, description="Character name")
    class_type: Optional[str] = Field(None, description="Character class (e.g., Warrior, Mage)")
    level: Optional[int] = Field(None, description="Character level", ge=1)
    race: Optional[str] = Field(None, description="Character race (e.g., Human, Elf)")
    
    @validator('*', pre=True)
    def check_not_empty(cls, v):
        """Validate that at least one field is provided."""
        if v == "":
            return None
        return v
    
    class Config:
        validate_assignment = True

class Character(CharacterBase):
    """Model representing an RPG character."""
    character_id: str = Field(..., description="Unique identifier for the character")
    attributes: CharacterAttributes = Field(default_factory=CharacterAttributes)
    
    @classmethod
    def create(cls, character_create: CharacterCreate) -> "Character":
        """Create a new character from a CharacterCreate model."""
        return cls(
            character_id=f"CHAR:{uuid.uuid4().hex[:8]}",
            **character_create.dict()
        )
```

## Initializing Some Attributes

Let's create some predefined attributes in our database:

```python
# rpg_character_db/db.py
"""
In-memory database for storing character data.
This will be replaced with a proper database later.
"""

# Our in-memory database is just a dictionary
db = {}

# Initialize with some sample attributes
db["ATTR:STR"] = {
    "attribute_id": "ATTR:STR",
    "name": "Strength",
    "description": "Physical power and carrying capacity"
}

db["ATTR:DEX"] = {
    "attribute_id": "ATTR:DEX",
    "name": "Dexterity",
    "description": "Agility, reflexes, and balance"
}

db["ATTR:CON"] = {
    "attribute_id": "ATTR:CON",
    "name": "Constitution",
    "description": "Health, stamina, and vital force"
}

db["ATTR:INT"] = {
    "attribute_id": "ATTR:INT",
    "name": "Intelligence",
    "description": "Mental acuity, information recall, analytical skill"
}

db["ATTR:WIS"] = {
    "attribute_id": "ATTR:WIS",
    "name": "Wisdom",
    "description": "Awareness, intuition, and insight"
}

db["ATTR:CHA"] = {
    "attribute_id": "ATTR:CHA",
    "name": "Charisma",
    "description": "Force of personality, persuasiveness, leadership"
}
```

## Creating an Attribute Repository

Let's create a repository for managing attributes:

```bash
# Create a new file for the attribute repository
touch rpg_character_db/repositories/attribute_repository.py
```

```python
# rpg_character_db/repositories/attribute_repository.py
from typing import Dict, List, Optional

from rpg_character_db.db import db
from rpg_character_db.models.attribute import Attribute

class AttributeRepository:
    """Repository for attribute data."""
    
    @classmethod
    def get(cls, attribute_id: str) -> Optional[Attribute]:
        """
        Get an attribute by ID.
        
        Args:
            attribute_id: The attribute ID
            
        Returns:
            The attribute if found, None otherwise
        """
        if attribute_id not in db:
            return None
        
        return Attribute(**db[attribute_id])
    
    @classmethod
    def list(cls) -> List[Attribute]:
        """
        List all attributes.
        
        Returns:
            A list of all attributes
        """
        return [
            Attribute(**data)
            for attribute_id, data in db.items()
            if attribute_id.startswith("ATTR:")
        ]
```

## Writing Tests for Character Attributes Management

Now, let's write tests for character attributes management:

```bash
# Create a new test file for character attributes
touch tests/test_character_attributes.py
```

```python
# tests/test_character_attributes.py
import pytest
from fastapi.testclient import TestClient

from rpg_character_db.models.attribute import Attribute, AttributeValue, CharacterAttribute

def test_get_character_attributes_returns_200(client, existing_character):
    """Test that getting character attributes returns a 200 status code."""
    response = client.get(f"/character/{existing_character.character_id}/attributes")
    assert response.status_code == 200

def test_get_character_attributes_returns_empty_list_initially(client, existing_character):
    """Test that getting character attributes returns an empty list initially."""
    response = client.get(f"/character/{existing_character.character_id}/attributes")
    assert response.json() == {"attributes": {}}

def test_set_character_attribute_returns_200(client, existing_character):
    """Test that setting a character attribute returns a 200 status code."""
    attribute_value = {"value": 10}
    response = client.put(
        f"/character/{existing_character.character_id}/attribute/ATTR:STR",
        json=attribute_value
    )
    assert response.status_code == 200

def test_set_character_attribute_returns_updated_character(client, existing_character):
    """Test that setting a character attribute returns the updated character."""
    attribute_value = {"value": 10}
    response = client.put(
        f"/character/{existing_character.character_id}/attribute/ATTR:STR",
        json=attribute_value
    )
    updated_character = response.json()
    
    # Check that the attribute was added
    assert "attributes" in updated_character
    assert "attributes" in updated_character["attributes"]
    assert "ATTR:STR" in updated_character["attributes"]["attributes"]
    assert updated_character["attributes"]["attributes"]["ATTR:STR"]["value"] == 10

def test_get_character_attributes_returns_attributes_after_setting(client, existing_character):
    """Test that getting character attributes returns the attributes after setting them."""
    # Set an attribute
    attribute_value = {"value": 10}
    client.put(
        f"/character/{existing_character.character_id}/attribute/ATTR:STR",
        json=attribute_value
    )
    
    # Get the attributes
    response = client.get(f"/character/{existing_character.character_id}/attributes")
    attributes = response.json()
    
    # Check that the attribute is in the response
    assert "attributes" in attributes
    assert "ATTR:STR" in attributes["attributes"]
    assert attributes["attributes"]["ATTR:STR"]["value"] == 10

def test_set_character_attribute_with_invalid_value_returns_422(client, existing_character):
    """Test that setting a character attribute with an invalid value returns a 422 status code."""
    # Value too low
    attribute_value = {"value": 0}
    response = client.put(
        f"/character/{existing_character.character_id}/attribute/ATTR:STR",
        json=attribute_value
    )
    assert response.status_code == 422
    
    # Value too high
    attribute_value = {"value": 21}
    response = client.put(
        f"/character/{existing_character.character_id}/attribute/ATTR:STR",
        json=attribute_value
    )
    assert response.status_code == 422

def test_set_character_attribute_with_non_existent_attribute_returns_404(client, existing_character):
    """Test that setting a non-existent attribute returns a 404 status code."""
    attribute_value = {"value": 10}
    response = client.put(
        f"/character/{existing_character.character_id}/attribute/ATTR:NON_EXISTENT",
        json=attribute_value
    )
    assert response.status_code == 404
    assert response.json() == {"detail": "Attribute not found"}

def test_set_character_attribute_with_non_existent_character_returns_404(client):
    """Test that setting an attribute for a non-existent character returns a 404 status code."""
    attribute_value = {"value": 10}
    response = client.put(
        "/character/CHAR:NON_EXISTENT/attribute/ATTR:STR",
        json=attribute_value
    )
    assert response.status_code == 404
    assert response.json() == {"detail": "Character not found"}

def test_delete_character_attribute_returns_200(client, existing_character):
    """Test that deleting a character attribute returns a 200 status code."""
    # First, set an attribute
    attribute_value = {"value": 10}
    client.put(
        f"/character/{existing_character.character_id}/attribute/ATTR:STR",
        json=attribute_value
    )
    
    # Then delete it
    response = client.delete(
        f"/character/{existing_character.character_id}/attribute/ATTR:STR"
    )
    assert response.status_code == 200

def test_delete_character_attribute_removes_attribute(client, existing_character):
    """Test that deleting a character attribute removes it from the character."""
    # First, set an attribute
    attribute_value = {"value": 10}
    client.put(
        f"/character/{existing_character.character_id}/attribute/ATTR:STR",
        json=attribute_value
    )
    
    # Then delete it
    client.delete(
        f"/character/{existing_character.character_id}/attribute/ATTR:STR"
    )
    
    # Check that it's gone
    response = client.get(f"/character/{existing_character.character_id}/attributes")
    attributes = response.json()
    
    assert "attributes" in attributes
    assert "ATTR:STR" not in attributes["attributes"]

def test_delete_non_existent_character_attribute_returns_404(client, existing_character):
    """Test that deleting a non-existent character attribute returns a 404 status code."""
    response = client.delete(
        f"/character/{existing_character.character_id}/attribute/ATTR:NON_EXISTENT"
    )
    assert response.status_code == 404
    assert response.json() == {"detail": "Attribute not found"}

def test_delete_attribute_from_non_existent_character_returns_404(client):
    """Test that deleting an attribute from a non-existent character returns a 404 status code."""
    response = client.delete(
        "/character/CHAR:NON_EXISTENT/attribute/ATTR:STR"
    )
    assert response.status_code == 404
    assert response.json() == {"detail": "Character not found"}
```

## Running the Tests (RED)

Let's run our tests to see them fail:

```bash
pytest tests/test_character_attributes.py -v
```

All our tests should fail because we haven't implemented the character attributes endpoints yet.

## Implementing Character Attributes Endpoints (GREEN)

Let's create a new file for character attributes endpoints:

```bash
# Create a new file for character attributes endpoints
touch rpg_character_db/api/endpoints/character_attributes.py
```

```python
# rpg_character_db/api/endpoints/character_attributes.py
from fastapi import APIRouter, HTTPException, Path, Body

from rpg_character_db.models.character import Character
from rpg_character_db.models.attribute import AttributeValue, CharacterAttributes
from rpg_character_db.repositories.character_repository import CharacterRepository
from rpg_character_db.repositories.attribute_repository import AttributeRepository
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter(tags=["character attributes"])

@router.get("/character/{character_id}/attributes", response_model=CharacterAttributes)
async def get_character_attributes(
    character_id: str = Path(..., description="The ID of the character")
):
    """
    Get all attributes for a character.
    
    Args:
        character_id: The character ID
        
    Returns:
        The character's attributes
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character
    character = CharacterRepository.get(character_id)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return character.attributes

@router.put("/character/{character_id}/attribute/{attribute_id}", response_model=Character)
async def set_character_attribute(
    character_id: str = Path(..., description="The ID of the character"),
    attribute_id: str = Path(..., description="The ID of the attribute"),
    attribute_value: AttributeValue = Body(..., description="The attribute value")
):
    """
    Set an attribute value for a character.
    
    Args:
        character_id: The character ID
        attribute_id: The attribute ID
        attribute_value: The attribute value
        
    Returns:
        The updated character
        
    Raises:
        HTTPException: If the character or attribute is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character
    character = CharacterRepository.get(character_id)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    # Get the attribute
    attribute = AttributeRepository.get(attribute_id)
    
    # Check if the attribute exists
    if attribute is None:
        raise HTTPException(status_code=404, detail="Attribute not found")
    
    # Create a character attribute
    character_attribute = {
        "attribute_id": attribute.attribute_id,
        "name": attribute.name,
        "description": attribute.description,
        "value": attribute_value.value
    }
    
    # Update the character's attributes
    character_dict = character.dict()
    if "attributes" not in character_dict:
        character_dict["attributes"] = {"attributes": {}}
    
    character_dict["attributes"]["attributes"][attribute_id] = character_attribute
    
    # Update the character in the database
    updated_character = CharacterRepository.update(character_id, character_dict)
    
    return updated_character

@router.delete("/character/{character_id}/attribute/{attribute_id}", response_model=Character)
async def delete_character_attribute(
    character_id: str = Path(..., description="The ID of the character"),
    attribute_id: str = Path(..., description="The ID of the attribute")
):
    """
    Remove an attribute from a character.
    
    Args:
        character_id: The character ID
        attribute_id: The attribute ID
        
    Returns:
        The updated character
        
    Raises:
        HTTPException: If the character or attribute is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character
    character = CharacterRepository.get(character_id)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    # Get the attribute
    attribute = AttributeRepository.get(attribute_id)
    
    # Check if the attribute exists
    if attribute is None:
        raise HTTPException(status_code=404, detail="Attribute not found")
    
    # Check if the character has the attribute
    character_dict = character.dict()
    if (
        "attributes" not in character_dict
        or "attributes" not in character_dict["attributes"]
        or attribute_id not in character_dict["attributes"]["attributes"]
    ):
        raise HTTPException(status_code=404, detail="Character does not have this attribute")
    
    # Remove the attribute from the character
    del character_dict["attributes"]["attributes"][attribute_id]
    
    # Update the character in the database
    updated_character = CharacterRepository.update(character_id, character_dict)
    
    return updated_character
```

Now, let's update our router registry to include the new endpoints:

```python
# rpg_character_db/api/router.py
from fastapi import APIRouter

from rpg_character_db.api.endpoints import (
    character_retrieval,
    character_creation,
    character_update,
    character_listing,
    character_attributes
)

# Create a main router
api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(character_retrieval.router)
api_router.include_router(character_creation.router)
api_router.include_router(character_update.router)
api_router.include_router(character_listing.router)
api_router.include_router(character_attributes.router)
```

## Running the Tests Again (GREEN)

Let's run our tests again to see if they pass:

```bash
pytest tests/test_character_attributes.py -v
```

All our tests should now pass! We've successfully implemented character attributes management.

## Refactoring

Our implementation is working, but there's room for improvement. Let's refactor to make our code more maintainable:

1. We should move the attribute validation logic to a separate function
2. We should improve error handling for attribute operations

Let's update our character attributes endpoints:

```python
# rpg_character_db/api/endpoints/character_attributes.py
from fastapi import APIRouter, HTTPException, Path, Body

from rpg_character_db.models.character import Character
from rpg_character_db.models.attribute import AttributeValue, CharacterAttributes
from rpg_character_db.repositories.character_repository import CharacterRepository
from rpg_character_db.repositories.attribute_repository import AttributeRepository
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter(tags=["character attributes"])

def get_character_and_attribute(character_id: str, attribute_id: str):
    """
    Get a character and attribute by their IDs.
    
    Args:
        character_id: The character ID
        attribute_id: The attribute ID
        
    Returns:
        A tuple of (character, attribute)
        
    Raises:
        HTTPException: If the character or attribute is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character
    character = CharacterRepository.get(character_id)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    # Get the attribute
    attribute = AttributeRepository.get(attribute_id)
    
    # Check if the attribute exists
    if attribute is None:
        raise HTTPException(status_code=404, detail="Attribute not found")
    
    return character, attribute

@router.get("/character/{character_id}/attributes", response_model=CharacterAttributes)
async def get_character_attributes(
    character_id: str = Path(..., description="The ID of the character")
):
    """
    Get all attributes for a character.
    
    Args:
        character_id: The character ID
        
    Returns:
        The character's attributes
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character
    character = CharacterRepository.get(character_id)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return character.attributes

@router.put("/character/{character_id}/attribute/{attribute_id}", response_model=Character)
async def set_character_attribute(
    character_id: str = Path(..., description="The ID of the character"),
    attribute_id: str = Path(..., description="The ID of the attribute"),
    attribute_value: AttributeValue = Body(..., description="The attribute value")
):
    """
    Set an attribute value for a character.
    
    Args:
        character_id: The character ID
        attribute_id: The attribute ID
        attribute_value: The attribute value
        
    Returns:
        The updated character
        
    Raises:
        HTTPException: If the character or attribute is not found
    """
    # Get the character and attribute
    character, attribute = get_character_and_attribute(character_id, attribute_id)
    
    # Create a character attribute
    character_attribute = {
        "attribute_id": attribute.attribute_id,
        "name": attribute.name,
        "description": attribute.description,
        "value": attribute_value.value
    }
    
    # Update the character's attributes
    character_dict = character.dict()
    if "attributes" not in character_dict:
        character_dict["attributes"] = {"attributes": {}}
    
    character_dict["attributes"]["attributes"][attribute_id] = character_attribute
    
    # Update the character in the database
    updated_character = CharacterRepository.update(character_id, character_dict)
    
    return updated_character

@router.delete("/character/{character_id}/attribute/{attribute_id}", response_model=Character)
async def delete_character_attribute(
    character_id: str = Path(..., description="The ID of the character"),
    attribute_id: str = Path(..., description="The ID of the attribute")
):
    """
    Remove an attribute from a character.
    
    Args:
        character_id: The character ID
        attribute_id: The attribute ID
        
    Returns:
        The updated character
        
    Raises:
        HTTPException: If the character or attribute is not found
    """
    # Get the character and attribute
    character, attribute = get_character_and_attribute(character_id, attribute_id)
    
    # Check if the character has the attribute
    character_dict = character.dict()
    if (
        "attributes" not in character_dict
        or "attributes" not in character_dict["attributes"]
        or attribute_id not in character_dict["attributes"]["attributes"]
    ):
        raise HTTPException(status_code=404, detail="Character does not have this attribute")
    
    # Remove the attribute from the character
    del character_dict["attributes"]["attributes"][attribute_id]
    
    # Update the character in the database
    updated_character = CharacterRepository.update(character_id, character_dict)
    
    return updated_character
```

We've made several improvements:
1. Created a helper function `get_character_and_attribute` to reduce code duplication
2. Improved error handling for attribute operations
3. Made the code more readable and maintainable

## Running the Tests After Refactoring

Let's run our tests one more time to ensure our refactoring didn't break anything:

```bash
pytest tests/test_character_attributes.py -v
```

All tests should still pass, confirming that our refactoring was successful.

## Committing Our Changes

Let's commit our changes to Git:

```bash
git add .
git commit -m "[RED/GREEN/REFACTOR] Implement character attributes management"
```

## Conclusion

In this lesson, we've:
1. Created models for attributes and character attributes
2. Implemented endpoints for managing character attributes
3. Written tests for attribute management functionality
4. Refactored our code for better maintainability

We've continued to follow the RED-GREEN-REFACTOR cycle, letting our tests drive the development of our API.

In the next lesson, we'll learn about the repository pattern and how it can help us improve our code organization and testability.

Are there any questions about what we've covered today?

Thank you for your attention, and I'll see you in the next lesson!
