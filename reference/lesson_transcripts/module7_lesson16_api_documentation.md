# Module 7, Lesson 16: API Documentation with Swagger UI

## Introduction

Welcome back to our TDD course! In our previous lessons, we've built a functional RPG Character Database API with character, attribute, and skill management, and integrated it with SQLAlchemy. Today, we'll focus on documenting our API using Swagger UI, which is built into FastAPI.

Good API documentation is crucial for developers who want to use your API. It helps them understand what endpoints are available, what parameters they accept, and what responses they return. FastAPI makes this easy by automatically generating interactive documentation based on your code and docstrings.

## Understanding API Documentation

API documentation serves several important purposes:

1. **Discoverability**: It helps users discover what functionality is available
2. **Understanding**: It helps users understand how to use the API
3. **Testing**: It provides a way to test the API without writing code
4. **Reference**: It serves as a reference for developers

Good API documentation should include:

1. **Endpoints**: What URLs are available and what HTTP methods they support
2. **Parameters**: What parameters each endpoint accepts
3. **Request Bodies**: What data should be sent in request bodies
4. **Responses**: What responses to expect, including status codes and response bodies
5. **Examples**: Examples of requests and responses

## FastAPI's Built-in Documentation

FastAPI automatically generates interactive documentation for your API using:

1. **Swagger UI**: A web-based tool for visualizing and interacting with your API
2. **ReDoc**: An alternative documentation UI that's more focused on readability

These are automatically available at `/docs` and `/redoc` respectively.

FastAPI generates this documentation based on:

1. **Type hints**: The parameter and return types you specify in your code
2. **Pydantic models**: The models you use for request and response bodies
3. **Docstrings**: The documentation strings you add to your functions
4. **Path operation decorators**: The metadata you provide in your route decorators

Let's see how we can enhance our API documentation.

## Enhancing API Documentation

Let's start by adding more detailed documentation to our API:

```python
# rpg_character_db/main.py
from fastapi import FastAPI, Depends

from rpg_character_db.api.router import api_router
from rpg_character_db.dependencies import startup_db_seed

app = FastAPI(
    title="RPG Character Database API",
    description="""
    An API for managing RPG character data, including attributes and skills.
    
    ## Features
    
    * Create, read, update, and delete characters
    * Manage character attributes
    * Manage character skills
    
    ## Authentication
    
    This API doesn't require authentication for simplicity.
    """,
    version="1.0.0",
    contact={
        "name": "Your Name",
        "url": "https://yourwebsite.com",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT",
    },
)

@app.get("/")
async def root():
    """
    Get a welcome message.
    
    Returns:
        A welcome message
    """
    return {"message": "Welcome to the RPG Character Database API"}

# Include all API routes
app.include_router(api_router)

# Seed the database on startup
@app.on_event("startup")
async def startup():
    startup_db_seed()
```

Now, let's enhance the documentation for our character endpoints:

```python
# rpg_character_db/api/endpoints/character_retrieval.py
from fastapi import APIRouter, Depends, HTTPException, Path, status
from typing import List

from rpg_character_db.models.character import Character
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.dependencies import get_character_repository
from rpg_character_db.utils.validation import validate_character_id

router = APIRouter(tags=["characters"])

@router.get(
    "/character/{character_id}",
    response_model=Character,
    summary="Get a character by ID",
    responses={
        status.HTTP_200_OK: {
            "description": "Character found",
            "model": Character,
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Character not found",
            "content": {
                "application/json": {
                    "example": {"detail": "Character not found"}
                }
            },
        },
        status.HTTP_422_UNPROCESSABLE_ENTITY: {
            "description": "Invalid character ID format",
            "content": {
                "application/json": {
                    "example": {
                        "detail": {
                            "message": "Invalid character ID format",
                            "expected_pattern": "^CHAR:[a-zA-Z0-9]+$",
                            "received": "invalid-format"
                        }
                    }
                }
            },
        },
    }
)
async def get_character_by_id(
    character_id: str = Path(..., description="The ID of the character to retrieve", example="CHAR:123"),
    character_repository: ICharacterRepository = Depends(get_character_repository)
):
    """
    Get a character by ID.
    
    This endpoint retrieves a character by its ID. The character ID must be in the format `CHAR:<id>`.
    
    Args:
        character_id: The character ID
        character_repository: The character repository
        
    Returns:
        The character if found
        
    Raises:
        HTTPException: If the character ID is invalid or the character is not found
    """
    # Validate character ID format
    validate_character_id(character_id)
    
    # Get the character from the repository
    character = character_repository.get(character_id)
    
    # Check if the character exists
    if character is None:
        raise HTTPException(status_code=404, detail="Character not found")
    
    return character
```

Let's also enhance the documentation for our character creation endpoint:

```python
# rpg_character_db/api/endpoints/character_creation.py
from fastapi import APIRouter, Depends, status
from typing import Dict, Any

from rpg_character_db.models.character import Character, CharacterCreate
from rpg_character_db.repositories.interfaces.character_repository import ICharacterRepository
from rpg_character_db.dependencies import get_character_repository

router = APIRouter(tags=["characters"])

@router.post(
    "/character",
    response_model=Character,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new character",
    responses={
        status.HTTP_201_CREATED: {
            "description": "Character created successfully",
            "model": Character,
        },
        status.HTTP_422_UNPROCESSABLE_ENTITY: {
            "description": "Invalid character data",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["body", "name"],
                                "msg": "field required",
                                "type": "value_error.missing"
                            }
                        ]
                    }
                }
            },
        },
    }
)
async def create_character(
    character_create: CharacterCreate,
    character_repository: ICharacterRepository = Depends(get_character_repository)
):
    """
    Create a new character.
    
    This endpoint creates a new character with the provided data.
    
    Args:
        character_create: The character data
        character_repository: The character repository
        
    Returns:
        The created character
    """
    # Create a new character using the repository
    return character_repository.create(character_create)
```

## Adding Examples to Pydantic Models

We can also add examples to our Pydantic models to make the documentation more helpful:

```python
# rpg_character_db/models/character.py
from pydantic import BaseModel, Field, validator
import uuid
from typing import Dict, Optional

from rpg_character_db.models.attribute import CharacterAttributes
from rpg_character_db.models.skill import CharacterSkills

class CharacterBase(BaseModel):
    """Base model for character data."""
    name: str = Field(..., description="Character name", example="Aragorn")
    class_type: str = Field(..., description="Character class (e.g., Warrior, Mage)", example="Warrior")
    level: int = Field(..., description="Character level", ge=1, example=5)
    race: str = Field(..., description="Character race (e.g., Human, Elf)", example="Human")

class CharacterCreate(CharacterBase):
    """Model for creating a new character."""
    
    class Config:
        schema_extra = {
            "example": {
                "name": "Aragorn",
                "class_type": "Warrior",
                "level": 5,
                "race": "Human"
            }
        }

class CharacterUpdate(CharacterBase):
    """Model for updating a character."""
    
    class Config:
        schema_extra = {
            "example": {
                "name": "Aragorn",
                "class_type": "Ranger",
                "level": 10,
                "race": "Human"
            }
        }

class CharacterPartialUpdate(BaseModel):
    """Model for partially updating a character."""
    name: Optional[str] = Field(None, description="Character name", example="Aragorn")
    class_type: Optional[str] = Field(None, description="Character class (e.g., Warrior, Mage)", example="Ranger")
    level: Optional[int] = Field(None, description="Character level", ge=1, example=10)
    race: Optional[str] = Field(None, description="Character race (e.g., Human, Elf)", example="Human")
    
    @validator('*', pre=True)
    def check_not_empty(cls, v):
        """Validate that at least one field is provided."""
        if v == "":
            return None
        return v
    
    class Config:
        validate_assignment = True
        schema_extra = {
            "example": {
                "name": "Aragorn",
                "level": 10
            }
        }

class Character(CharacterBase):
    """Model representing an RPG character."""
    character_id: str = Field(..., description="Unique identifier for the character", example="CHAR:123abc")
    attributes: CharacterAttributes = Field(default_factory=CharacterAttributes)
    skills: CharacterSkills = Field(default_factory=CharacterSkills)
    
    @classmethod
    def create(cls, character_create: CharacterCreate) -> "Character":
        """Create a new character from a CharacterCreate model."""
        return cls(
            character_id=f"CHAR:{uuid.uuid4().hex[:8]}",
            **character_create.dict()
        )
    
    class Config:
        schema_extra = {
            "example": {
                "character_id": "CHAR:123abc",
                "name": "Aragorn",
                "class_type": "Warrior",
                "level": 5,
                "race": "Human",
                "attributes": {
                    "attributes": {
                        "ATTR:STR": {
                            "attribute_id": "ATTR:STR",
                            "name": "Strength",
                            "description": "Physical power and carrying capacity",
                            "value": 15
                        }
                    }
                },
                "skills": {
                    "skills": {
                        "SKILL:SLASH": {
                            "skill_id": "SKILL:SLASH",
                            "name": "Slash",
                            "description": "A basic melee attack",
                            "required_level": 1,
                            "level": 3
                        }
                    }
                }
            }
        }
```

Let's also add examples to our attribute and skill models:

```python
# rpg_character_db/models/attribute.py
from pydantic import BaseModel, Field, validator
from typing import Dict, Optional

class Attribute(BaseModel):
    """Model representing an RPG character attribute."""
    attribute_id: str = Field(..., description="Unique identifier for the attribute", example="ATTR:STR")
    name: str = Field(..., description="Attribute name", example="Strength")
    description: str = Field(..., description="Attribute description", example="Physical power and carrying capacity")
    
    class Config:
        schema_extra = {
            "example": {
                "attribute_id": "ATTR:STR",
                "name": "Strength",
                "description": "Physical power and carrying capacity"
            }
        }

class AttributeValue(BaseModel):
    """Model representing an attribute value assigned to a character."""
    value: int = Field(..., description="Attribute value", ge=1, le=20, example=15)
    
    @validator('value')
    def validate_value_range(cls, v):
        """Validate that the attribute value is within the acceptable range."""
        if v < 1 or v > 20:
            raise ValueError("Attribute value must be between 1 and 20")
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "value": 15
            }
        }

class CharacterAttribute(BaseModel):
    """Model representing an attribute with its value for a character."""
    attribute_id: str = Field(..., description="Unique identifier for the attribute", example="ATTR:STR")
    name: str = Field(..., description="Attribute name", example="Strength")
    description: str = Field(..., description="Attribute description", example="Physical power and carrying capacity")
    value: int = Field(..., description="Attribute value", ge=1, le=20, example=15)
    
    class Config:
        schema_extra = {
            "example": {
                "attribute_id": "ATTR:STR",
                "name": "Strength",
                "description": "Physical power and carrying capacity",
                "value": 15
            }
        }

class CharacterAttributes(BaseModel):
    """Model representing all attributes for a character."""
    attributes: Dict[str, CharacterAttribute] = Field(default_factory=dict)
    
    class Config:
        schema_extra = {
            "example": {
                "attributes": {
                    "ATTR:STR": {
                        "attribute_id": "ATTR:STR",
                        "name": "Strength",
                        "description": "Physical power and carrying capacity",
                        "value": 15
                    }
                }
            }
        }
```

```python
# rpg_character_db/models/skill.py
from pydantic import BaseModel, Field, validator
from typing import Dict, Optional

class Skill(BaseModel):
    """Model representing an RPG character skill."""
    skill_id: str = Field(..., description="Unique identifier for the skill", example="SKILL:SLASH")
    name: str = Field(..., description="Skill name", example="Slash")
    description: str = Field(..., description="Skill description", example="A basic melee attack")
    required_level: int = Field(..., description="Minimum character level required to learn this skill", ge=1, example=1)
    
    class Config:
        schema_extra = {
            "example": {
                "skill_id": "SKILL:SLASH",
                "name": "Slash",
                "description": "A basic melee attack",
                "required_level": 1
            }
        }

class SkillLevel(BaseModel):
    """Model representing a skill level assigned to a character."""
    level: int = Field(..., description="Skill level", ge=1, le=5, example=3)
    
    @validator('level')
    def validate_level_range(cls, v):
        """Validate that the skill level is within the acceptable range."""
        if v < 1 or v > 5:
            raise ValueError("Skill level must be between 1 and 5")
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "level": 3
            }
        }

class CharacterSkill(BaseModel):
    """Model representing a skill with its level for a character."""
    skill_id: str = Field(..., description="Unique identifier for the skill", example="SKILL:SLASH")
    name: str = Field(..., description="Skill name", example="Slash")
    description: str = Field(..., description="Skill description", example="A basic melee attack")
    required_level: int = Field(..., description="Minimum character level required to learn this skill", ge=1, example=1)
    level: int = Field(..., description="Skill level", ge=1, le=5, example=3)
    
    class Config:
        schema_extra = {
            "example": {
                "skill_id": "SKILL:SLASH",
                "name": "Slash",
                "description": "A basic melee attack",
                "required_level": 1,
                "level": 3
            }
        }

class CharacterSkills(BaseModel):
    """Model representing all skills for a character."""
    skills: Dict[str, CharacterSkill] = Field(default_factory=dict)
    
    class Config:
        schema_extra = {
            "example": {
                "skills": {
                    "SKILL:SLASH": {
                        "skill_id": "SKILL:SLASH",
                        "name": "Slash",
                        "description": "A basic melee attack",
                        "required_level": 1,
                        "level": 3
                    }
                }
            }
        }
```

## Organizing Endpoints with Tags

We can organize our endpoints with tags to make the documentation more structured:

```python
# rpg_character_db/api/router.py
from fastapi import APIRouter

from rpg_character_db.api.endpoints import (
    character_retrieval,
    character_creation,
    character_update,
    character_listing,
    character_attributes,
    character_skills
)

# Create a main router
api_router = APIRouter()

# Include all endpoint routers with tags
api_router.include_router(
    character_retrieval.router,
    tags=["Characters"],
    prefix="/character"
)
api_router.include_router(
    character_creation.router,
    tags=["Characters"],
    prefix="/character"
)
api_router.include_router(
    character_update.router,
    tags=["Characters"],
    prefix="/character"
)
api_router.include_router(
    character_listing.router,
    tags=["Characters"],
    prefix="/characters"
)
api_router.include_router(
    character_attributes.router,
    tags=["Character Attributes"],
    prefix="/character"
)
api_router.include_router(
    character_skills.router,
    tags=["Character Skills"],
    prefix="/character"
)
```

## Adding API Metadata

We can also add metadata to our API to make the documentation more informative:

```python
# rpg_character_db/main.py
from fastapi import FastAPI, Depends
from fastapi.openapi.utils import get_openapi

from rpg_character_db.api.router import api_router
from rpg_character_db.dependencies import startup_db_seed

app = FastAPI(
    title="RPG Character Database API",
    description="""
    An API for managing RPG character data, including attributes and skills.
    
    ## Features
    
    * Create, read, update, and delete characters
    * Manage character attributes
    * Manage character skills
    
    ## Authentication
    
    This API doesn't require authentication for simplicity.
    """,
    version="1.0.0",
    contact={
        "name": "Your Name",
        "url": "https://yourwebsite.com",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT",
    },
)

def custom_openapi():
    """
    Generate a custom OpenAPI schema.
    
    Returns:
        The OpenAPI schema
    """
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )
    
    # Add custom metadata
    openapi_schema["info"]["x-logo"] = {
        "url": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png"
    }
    
    # Add tags metadata
    openapi_schema["tags"] = [
        {
            "name": "Characters",
            "description": "Operations related to characters",
        },
        {
            "name": "Character Attributes",
            "description": "Operations related to character attributes",
        },
        {
            "name": "Character Skills",
            "description": "Operations related to character skills",
        },
    ]
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi

@app.get("/")
async def root():
    """
    Get a welcome message.
    
    Returns:
        A welcome message
    """
    return {"message": "Welcome to the RPG Character Database API"}

# Include all API routes
app.include_router(api_router)

# Seed the database on startup
@app.on_event("startup")
async def startup():
    startup_db_seed()
```

## Running the Application

Let's run our application and check the documentation:

```bash
uvicorn rpg_character_db.main:app --reload
```

Now, we can access the Swagger UI documentation at `http://localhost:8000/docs` and the ReDoc documentation at `http://localhost:8000/redoc`.

## Testing the Documentation

Let's write a simple test to ensure our documentation is working:

```python
# tests/test_documentation.py
from fastapi.testclient import TestClient

from rpg_character_db.main import app

client = TestClient(app)

def test_openapi_schema():
    """Test that the OpenAPI schema is generated correctly."""
    response = client.get("/openapi.json")
    assert response.status_code == 200
    
    # Check that the schema has the expected structure
    schema = response.json()
    assert "info" in schema
    assert "title" in schema["info"]
    assert schema["info"]["title"] == "RPG Character Database API"
    assert "version" in schema["info"]
    assert schema["info"]["version"] == "1.0.0"
    
    # Check that the schema has the expected paths
    assert "paths" in schema
    assert "/character/{character_id}" in schema["paths"]
    assert "/character" in schema["paths"]
    assert "/characters" in schema["paths"]
    
    # Check that the schema has the expected components
    assert "components" in schema
    assert "schemas" in schema["components"]
    assert "Character" in schema["components"]["schemas"]
    assert "CharacterCreate" in schema["components"]["schemas"]
    assert "CharacterUpdate" in schema["components"]["schemas"]
    assert "CharacterPartialUpdate" in schema["components"]["schemas"]
```

## Running the Tests

Let's run our tests to make sure everything works:

```bash
pytest tests/test_documentation.py -v
```

All tests should pass! We've successfully enhanced our API documentation.

## Committing Our Changes

Let's commit our changes to Git:

```bash
git add .
git commit -m "[REFACTOR] Enhance API documentation with Swagger UI"
```

## Conclusion

In this lesson, we've:
1. Enhanced our API documentation using FastAPI's built-in features
2. Added examples to our Pydantic models
3. Organized our endpoints with tags
4. Added metadata to our API
5. Written tests for our documentation

Good API documentation is crucial for developers who want to use your API. FastAPI makes it easy to create comprehensive, interactive documentation that helps users understand and test your API.

In the next lesson, we'll learn about API versioning and how to handle breaking changes in our API.

Are there any questions about what we've covered today?

Thank you for your attention, and I'll see you in the next lesson!
