# Module 1, Lesson 3: Writing Your First Test

## Introduction

Welcome to our third lesson! In the previous lessons, we covered TDD fundamentals and set up our project environment. Today, we're going to write our first test following the TDD methodology. This is where the rubber meets the road - we'll start applying the RED-GREEN-REFACTOR cycle that's at the heart of Test-Driven Development.

## Introduction to pytest

Before we dive into writing our first test, let's briefly discuss pytest, the testing framework we'll be using throughout this course.

pytest is a powerful and flexible testing framework for Python that makes it easy to write simple and scalable tests. Some of its key features include:

- Simple syntax for writing tests
- Powerful fixture system for reusable test setup
- Rich plugin ecosystem
- Detailed test reports
- Auto-discovery of test modules and functions

In pytest, any function whose name starts with `test_` is considered a test function and will be executed when you run pytest.

## Creating Our First Test File

Let's create our first test file for testing character retrieval:

```bash
# Create a test file for character retrieval
touch tests/test_get_character_by_id.py
```

Now, let's open this file and write our first test:

```python
# tests/test_get_character_by_id.py
from fastapi.testclient import TestClient

from rpg_character_db.main import app

client = TestClient(app)

def test_get_non_existent_character_returns_404():
    """Test that requesting a non-existent character returns a 404 status code."""
    response = client.get("/character/NON_EXISTENT")
    assert response.status_code == 404
    assert response.json() == {"detail": "Character not found"}
```

Let's break down what this test does:

1. We import `TestClient` from FastAPI, which allows us to make requests to our API without actually running the server.
2. We import our FastAPI app from the main module.
3. We create a test client instance.
4. We define a test function that:
   - Makes a GET request to `/character/NON_EXISTENT`
   - Asserts that the response status code is 404
   - Asserts that the response JSON contains an error message

This test follows the AAA pattern we discussed in Lesson 1:
- **Arrange**: Set up the test client
- **Act**: Make the GET request
- **Assert**: Verify the response status and content

## Running the Test (RED)

Now, let's run our test to see it fail (the RED phase of RED-GREEN-REFACTOR):

```bash
pytest tests/test_get_character_by_id.py -v
```

You should see output indicating that the test failed. This is expected! In TDD, we always start with a failing test.

The test is failing because:
1. We haven't implemented the `/character/{character_id}` endpoint yet
2. Even if we had, it wouldn't know how to return a 404 for non-existent characters

Let's look at the error message. It's probably telling us that the endpoint doesn't exist, resulting in a 404 error, but not the specific 404 response we're expecting with the "Character not found" message.

## Implementing the Minimal Code to Pass the Test (GREEN)

Now, let's write the minimal code needed to make our test pass. We'll add a new endpoint to our main.py file:

```python
# rpg_character_db/main.py
from fastapi import FastAPI, HTTPException

app = FastAPI(title="RPG Character Database API")

@app.get("/")
async def root():
    return {"message": "Welcome to the RPG Character Database API"}

@app.get("/character/{character_id}")
async def get_character_by_id(character_id: str):
    # For now, we'll just return a 404 for any character_id
    # We'll implement actual character retrieval later
    raise HTTPException(status_code=404, detail="Character not found")
```

In this implementation, we're simply raising an HTTPException with a 404 status code and the message "Character not found" for any character ID. This is the minimal code needed to make our test pass.

## Running the Test Again (GREEN)

Let's run our test again to see if it passes:

```bash
pytest tests/test_get_character_by_id.py -v
```

Now the test should pass! We've successfully completed the GREEN phase of our RED-GREEN-REFACTOR cycle.

## Understanding Test Assertions

Let's take a moment to understand the assertions in our test:

```python
assert response.status_code == 404
assert response.json() == {"detail": "Character not found"}
```

Assertions are statements that check if a condition is true. If the condition is false, the assertion fails, and pytest reports an error.

In our test, we're asserting that:
1. The response status code is 404 (Not Found)
2. The response JSON is a dictionary with a single key "detail" and value "Character not found"

These assertions verify that our API behaves as expected when a client requests a non-existent character.

## Running Tests and Interpreting Results

When you run pytest, it provides detailed information about which tests passed and which failed. For failed tests, it shows:

- The expected value
- The actual value
- The line where the assertion failed
- A traceback to help you understand what went wrong

This information is invaluable for diagnosing and fixing issues in your code.

## Refactoring (REFACTOR)

In this simple example, there's not much to refactor yet. Our implementation is already minimal and straightforward. As we add more functionality, we'll have more opportunities for refactoring.

However, we could make a small improvement by moving our API routes to a separate module to keep our main.py file clean:

```bash
# Create a new file for character routes
touch rpg_character_db/api/character_routes.py
```

```python
# rpg_character_db/api/character_routes.py
from fastapi import APIRouter, HTTPException

router = APIRouter(prefix="/character", tags=["characters"])

@router.get("/{character_id}")
async def get_character_by_id(character_id: str):
    raise HTTPException(status_code=404, detail="Character not found")
```

And update our main.py file:

```python
# rpg_character_db/main.py
from fastapi import FastAPI

from rpg_character_db.api.character_routes import router as character_router

app = FastAPI(title="RPG Character Database API")

@app.get("/")
async def root():
    return {"message": "Welcome to the RPG Character Database API"}

app.include_router(character_router)
```

Now let's run our test again to make sure it still passes after our refactoring:

```bash
pytest tests/test_get_character_by_id.py -v
```

Great! Our test still passes, which means our refactoring didn't break anything.

## Committing Our Changes

Let's commit our changes to Git to track our progress:

```bash
git add .
git commit -m "[RED/GREEN] First test for character retrieval"
```

## Conclusion

Congratulations! You've just completed your first RED-GREEN-REFACTOR cycle:

1. **RED**: We wrote a failing test for retrieving a non-existent character
2. **GREEN**: We implemented the minimal code needed to make the test pass
3. **REFACTOR**: We improved our code organization by moving routes to a separate module

This is the essence of Test-Driven Development - letting tests drive the development of your code.

In our next lesson, we'll expand on this foundation by implementing character retrieval for existing characters, which will require us to create an in-memory database to store character data.

Are there any questions about what we've covered today?

Thank you for your attention, and I'll see you in the next lesson!
