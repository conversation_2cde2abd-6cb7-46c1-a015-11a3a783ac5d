# Course Navigation Guide

This document provides guidance on how to navigate through the course materials.

## Directory Structure

- `README.md`: Main course overview and introduction
- `rpg_character_db_requirements.md`: Detailed project requirements
- `rpg_character_db_course_structure.md`: Complete course structure and module breakdown
- `lesson_transcripts/`: Contains detailed transcripts for each lesson
- `code/`: Contains code samples for each lesson

## Recommended Learning Path

1. Start by reading the main `README.md` to understand the course structure
2. Review the project requirements in `rpg_character_db_requirements.md`
3. Examine the detailed course structure in `rpg_character_db_course_structure.md`
4. For each lesson:
   - Read the lesson transcript in `lesson_transcripts/`
   - Explore the corresponding code in `code/lesson#_*`
   - Follow the RED-GREEN-REFACTOR cycle in the code examples
   - Complete any exercises mentioned in the lesson

## Lesson Progression

The course follows a logical progression:

1. **Fundamentals & Setup** (Lessons 1-3): TDD basics and project setup
2. **Core API Implementation** (Lessons 4-7): Building basic API endpoints
3. **Test Organization** (Lessons 8-9): Test fixtures and code refactoring
4. **Advanced Features** (Lessons 10-14): Adding attributes, skills, and repository pattern
5. **Production Readiness** (Lessons 15-17): Database integration, documentation, and deployment

## Code Examples

Each lesson's code directory contains:
- `README.md`: Overview of the lesson
- Test files demonstrating the RED phase
- Implementation files demonstrating the GREEN phase
- Refactored code examples where appropriate

## Recommended Tools

- Python 3.10+
- FastAPI
- Pytest
- SQLAlchemy
- Docker (for deployment lessons)

## Getting Help

If you encounter any issues or have questions while working through the course:
- Review the lesson transcripts for detailed explanations
- Check the code examples for implementation details
- Refer to the official documentation for the tools used
